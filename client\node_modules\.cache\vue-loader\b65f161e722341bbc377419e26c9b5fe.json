{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\PQEDashboard\\PQEDashboardPage.vue?vue&type=template&id=06b3b034&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\PQEDashboard\\PQEDashboardPage.vue", "mtime": 1748987610870}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}