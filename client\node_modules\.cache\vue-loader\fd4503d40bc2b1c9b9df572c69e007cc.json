{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\Validation2\\Validation2.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\Validation2\\Validation2.vue", "mtime": 1748970271013}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Validation2.vue"], "names": [], "mappings": ";AAsPA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Validation2.vue", "sourceRoot": "src/views/Validation2", "sourcesContent": ["<template>\n  <div class=\"validation2-container\">\n    <MainHeader :expandedSideNav=\"expandedSideNav\" :useFixed=\"useFixed\" />\n\n    <!-- Classification Modal -->\n    <cv-modal\n      v-if=\"showClassificationModal\"\n      :visible=\"showClassificationModal\"\n      @modal-hidden=\"closeClassificationModal\"\n      size=\"default\"\n    >\n      <template slot=\"title\">Edit Classification</template>\n      <template slot=\"content\">\n        <div class=\"modal-content-inner\">\n          <p><strong>Defect ID:</strong> {{ selectedDefect ? selectedDefect.DEFECT_ID : '' }}</p>\n          <p class=\"description-text\"><strong>Description:</strong> {{ selectedDefect ? selectedDefect.DESCRIPTION : '' }}</p>\n\n          <div class=\"modal-form\">\n            <div class=\"form-group\">\n              <cv-dropdown\n                v-model=\"editClassification.primary\"\n                label=\"Primary Classification\"\n                :items=\"primaryClassificationOptions\"\n              />\n            </div>\n\n            <div class=\"form-group\">\n              <cv-dropdown\n                v-model=\"editClassification.subcategory\"\n                label=\"Subcategory\"\n                :items=\"subcategoryOptions\"\n              />\n            </div>\n          </div>\n        </div>\n      </template>\n      <template slot=\"secondary-button\">Cancel</template>\n      <template slot=\"primary-button\">Save</template>\n      <template slot=\"actions\">\n        <cv-button kind=\"secondary\" @click=\"closeClassificationModal\">Cancel</cv-button>\n        <cv-button kind=\"primary\" @click=\"saveClassification\">Save</cv-button>\n      </template>\n    </cv-modal>\n\n    <!-- Batch Results Modal -->\n    <cv-modal\n      v-if=\"showBatchResultsModal\"\n      :visible=\"showBatchResultsModal\"\n      @modal-hidden=\"closeBatchResultsModal\"\n      size=\"large\"\n    >\n      <template slot=\"title\">Batch Processing Results</template>\n      <template slot=\"content\">\n        <div class=\"batch-results-content\">\n          <p>Successfully processed {{ batchResults.length }} defects</p>\n\n          <cv-data-table\n            :columns=\"batchResultsColumns\"\n            :data=\"batchResults\"\n            :pagination=\"{ pageSize: 10 }\"\n            title=\"\"\n          >\n            <template slot=\"data\">\n              <cv-data-table-row\n                v-for=\"(result, index) in batchResults\"\n                :key=\"index\"\n              >\n                <cv-data-table-cell>{{ result.defectId }}</cv-data-table-cell>\n                <cv-data-table-cell>{{ result.description }}</cv-data-table-cell>\n                <cv-data-table-cell>\n                  <cv-tag :kind=\"getClassificationTagKind(result.classification.primary)\">\n                    {{ result.classification.primary }}\n                  </cv-tag>\n                </cv-data-table-cell>\n                <cv-data-table-cell>\n                  <cv-tag :kind=\"getClassificationTagKind(result.classification.subcategory)\">\n                    {{ result.classification.subcategory }}\n                  </cv-tag>\n                </cv-data-table-cell>\n              </cv-data-table-row>\n            </template>\n          </cv-data-table>\n        </div>\n      </template>\n      <template slot=\"actions\">\n        <cv-button kind=\"primary\" @click=\"closeBatchResultsModal\">Close</cv-button>\n      </template>\n    </cv-modal>\n\n    <main id=\"main-content\" class=\"main-content\">\n      <!-- Page Header -->\n      <div class=\"page-header\">\n        <h1>Defect Validation 2.0</h1>\n        <p>Analyze and validate defects with AI assistance</p>\n      </div>\n\n      <!-- Summary Tiles -->\n      <div class=\"summary-section\">\n        <cv-tile class=\"summary-tile\">\n          <h4>Total Validations</h4>\n          <p class=\"tile-value\">{{ validationsSummary.total }}</p>\n        </cv-tile>\n        <cv-tile class=\"summary-tile\">\n          <h4>Validated</h4>\n          <p class=\"tile-value validated\">{{ validationsSummary.validated }}</p>\n          <p class=\"tile-percentage\">{{ validationsSummary.validatedPercentage }}%</p>\n        </cv-tile>\n        <cv-tile class=\"summary-tile\">\n          <h4>Unvalidated</h4>\n          <p class=\"tile-value unvalidated\">{{ validationsSummary.unvalidated }}</p>\n          <p class=\"tile-percentage\">{{ validationsSummary.unvalidatedPercentage }}%</p>\n        </cv-tile>\n      </div>\n\n      <!-- Controls Row -->\n      <div class=\"controls-row\">\n        <div class=\"filter-group\">\n          <cv-dropdown\n            v-model=\"selectedPQEOwner\"\n            label=\"PQE Owner\"\n            :items=\"pqeOwnerOptions\"\n            @change=\"handlePQEOwnerChange\"\n          />\n        </div>\n\n        <div class=\"filter-group\">\n          <cv-dropdown\n            v-model=\"selectedTimeFilter\"\n            label=\"Time Period\"\n            :items=\"timeFilterOptions\"\n            @change=\"handleTimeFilterChange\"\n          />\n        </div>\n\n        <div class=\"button-group\">\n          <cv-button\n            kind=\"primary\"\n            @click=\"processNewDefects\"\n            :disabled=\"processingDefects\"\n          >\n            {{ processingDefects ? 'Processing...' : 'Process All New Defects' }}\n          </cv-button>\n\n          <div class=\"batch-process-controls\">\n            <cv-number-input\n              v-model=\"batchSize\"\n              label=\"Batch Size\"\n              :min=\"1\"\n              :max=\"20\"\n              :disabled=\"processingDefects\"\n            />\n            <cv-button\n              kind=\"secondary\"\n              @click=\"processLimitedBatch\"\n              :disabled=\"processingDefects\"\n            >\n              Process Batch & View Results\n            </cv-button>\n          </div>\n        </div>\n\n        <div v-if=\"processingMessage\" class=\"processing-message\">\n          <cv-inline-notification\n            kind=\"info\"\n            :title=\"'Processing Status'\"\n            :sub-title=\"processingMessage\"\n          />\n        </div>\n      </div>\n\n      <div class=\"pareto-section\">\n        <h2>Defect Classification Breakdown</h2>\n        <div v-if=\"loadingCounts\" class=\"loading-indicator\">\n          <cv-loading />\n        </div>\n        <div v-else-if=\"classificationCounts\" class=\"chart-container\">\n          <div class=\"pareto-chart\" data-carbon-theme=\"g90\">\n            <HBarChart\n              :data=\"chartData\"\n              :loading=\"loadingCounts\"\n              title=\"Defect Classification Breakdown\"\n              height=\"400px\"\n            />\n          </div>\n        </div>\n      </div>\n\n      <div class=\"defects-table-section\">\n        <h2>Defect Details</h2>\n        <div v-if=\"loadingData\" class=\"loading-indicator\">\n          <cv-loading />\n        </div>\n        <div v-else class=\"table-container\">\n          <cv-data-table\n            :columns=\"tableColumns\"\n            :data=\"tableData\"\n            :pagination=\"{ pageSize: 10 }\"\n            :sortable=\"true\"\n          >\n            <template slot=\"data\">\n              <cv-data-table-row\n                v-for=\"(row, rowIndex) in tableData\"\n                :key=\"rowIndex\"\n                :value=\"`${row.DEFECT_ID}`\"\n              >\n                <cv-data-table-cell>{{ row.DEFECT_ID }}</cv-data-table-cell>\n                <cv-data-table-cell>{{ formatDate(row.MFSDATE) }}</cv-data-table-cell>\n                <cv-data-table-cell>{{ row.STAGE }}</cv-data-table-cell>\n                <cv-data-table-cell>{{ row.DESCRIPTION }}</cv-data-table-cell>\n                <cv-data-table-cell>\n                  <div class=\"classification-cell\">\n                    <div v-if=\"getPrimaryClassification(row)\" class=\"classification-tag\" :class=\"getPrimaryClassification(row).toLowerCase()\">\n                      {{ getPrimaryClassification(row) }}\n                    </div>\n                    <div v-else class=\"classification-tag unclassified\">\n                      Unclassified\n                    </div>\n                  </div>\n                </cv-data-table-cell>\n                <cv-data-table-cell>\n                  <div class=\"classification-cell\">\n                    <div v-if=\"getSubcategoryClassification(row)\" class=\"classification-tag subcategory\" :class=\"getSubcategoryClass(row)\">\n                      {{ getSubcategoryClassification(row) }}\n                    </div>\n                    <div v-else class=\"classification-tag unclassified\">\n                      Unclassified\n                    </div>\n                    <button\n                      v-if=\"row.DEFECT_ID\"\n                      class=\"edit-button\"\n                      @click=\"openClassificationModal(row)\"\n                    >\n                      Edit\n                    </button>\n                  </div>\n                </cv-data-table-cell>\n              </cv-data-table-row>\n            </template>\n          </cv-data-table>\n        </div>\n      </div>\n    </main>\n  </div>\n</template>\n\n<script>\nimport MainHeader from \"@/components/MainHeader\";\nimport HBarChart from \"@/components/HBarChart/HBarChart\";\nimport {\n  CvModal,\n  CvButton,\n  CvDropdown,\n  CvTile,\n  CvDataTable,\n  CvDataTableRow,\n  CvDataTableCell,\n  CvTag,\n  CvNumberInput,\n  CvInlineNotification,\n  CvLoading\n} from '@carbon/vue';\n\nexport default {\n  name: \"ValidationAiPage\",\n  components: {\n    MainHeader,\n    HBarChart,\n    CvModal,\n    CvButton,\n    CvDropdown,\n    CvTile,\n    CvDataTable,\n    CvDataTableRow,\n    CvDataTableCell,\n    CvTag,\n    CvNumberInput,\n    CvInlineNotification,\n    CvLoading\n  },\n  data() {\n    return {\n      expandedSideNav: true,\n      useFixed: false,\n\n      // PQE Owner data\n      selectedPQEOwner: 'All',\n      pqeOwners: [],\n      pqeOwnerOptions: [{ label: 'All PQE Owners', value: 'All' }],\n\n      // Time filter data\n      selectedTimeFilter: \"month\",\n      timeFilterOptions: [\n        { label: \"Past Month\", value: \"month\" },\n        { label: \"Past Week\", value: \"week\" },\n        { label: \"Past Day\", value: \"day\" }\n      ],\n\n      // Classification options for dropdowns\n      primaryClassificationOptions: [\n        { label: \"Mechanical\", value: \"Mechanical\" },\n        { label: \"Functional\", value: \"Functional\" },\n        { label: \"Need More Info\", value: \"Need More Info\" }\n      ],\n      subcategoryOptions: [\n        { label: \"Scratches\", value: \"Scratches\" },\n        { label: \"Bent\", value: \"Bent\" },\n        { label: \"Plugging\", value: \"Plugging\" },\n        { label: \"Discolor\", value: \"Discolor\" },\n        { label: \"Misalignment\", value: \"Misalignment\" },\n        { label: \"Need More Info\", value: \"Need More Info\" },\n        { label: \"Other\", value: \"Other\" }\n      ],\n\n      // Loading states\n      loadingData: false,\n      loadingCounts: false,\n      processingDefects: false,\n      processingMessage: '',\n\n      // Data arrays\n      validationData: [],\n      filteredData: [],\n      primaryFilter: 'all',\n      subcategoryFilter: 'all',\n      classificationCounts: null,\n\n      // Modal states\n      showClassificationModal: false,\n      showBatchResultsModal: false,\n      selectedDefect: null,\n      editClassification: {\n        primary: 'Mechanical',\n        subcategory: 'Other'\n      },\n\n      // Batch processing\n      batchSize: 5,\n      batchResults: [],\n\n      // Table configurations\n      tableColumns: [\n        { label: \"Defect ID\", key: \"defect_id\" },\n        { label: \"Date\", key: \"date\" },\n        { label: \"Stage\", key: \"stage\" },\n        { label: \"Description\", key: \"description\" },\n        { label: \"Primary\", key: \"primary\" },\n        { label: \"Subcategory\", key: \"subcategory\" }\n      ],\n      batchResultsColumns: [\n        { label: \"Defect ID\", key: \"defectId\" },\n        { label: \"Description\", key: \"description\" },\n        { label: \"Primary\", key: \"primary\" },\n        { label: \"Subcategory\", key: \"subcategory\" }\n      ],\n\n      // Summary data\n      validationsSummary: {\n        total: 0,\n        validated: 0,\n        unvalidated: 0,\n        validatedPercentage: 0,\n        unvalidatedPercentage: 0\n      }\n    };\n  },\n  computed: {\n    tableData() {\n      return this.filteredData || [];\n    },\n    chartData() {\n      if (!this.classificationCounts) return [];\n\n      // Format the data for the horizontal bar chart\n      return [\n        { group: \"Classification\", key: \"Mechanical\", value: this.classificationCounts.Mechanical || 0 },\n        { group: \"Classification\", key: \"Functional\", value: this.classificationCounts.Functional || 0 },\n        { group: \"Classification\", key: \"Unclassified\", value: this.classificationCounts.Unclassified || 0 }\n      ];\n    }\n  },\n  mounted() {\n    this.loadPQEOwners();\n    this.fetchValidationData();\n    this.fetchClassificationCounts();\n  },\n  methods: {\n    // Load PQE owners from API\n    async loadPQEOwners() {\n      try {\n        const config = this.getAuthConfig();\n        const response = await fetch('/api-statit2/get_pqe_owners', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({})\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch PQE owners: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n        if (data.status_res === 'success') {\n          this.pqeOwners = data.pqe_owners || [];\n\n          // Update dropdown options\n          this.pqeOwnerOptions = [\n            { label: 'All PQE Owners', value: 'All' },\n            ...this.pqeOwners.map(owner => ({ label: owner, value: owner }))\n          ];\n\n          console.log(`Loaded ${this.pqeOwners.length} PQE owners`);\n        } else {\n          console.error('Failed to load PQE owners:', data.message);\n        }\n      } catch (error) {\n        console.error('Error loading PQE owners:', error);\n      }\n    },\n\n    // Get authentication config\n    getAuthConfig() {\n      return {\n        headers: {\n          'Authorization': 'Bearer ' + (localStorage.getItem('token') || ''),\n          'X-User-ID': localStorage.getItem('userId') || ''\n        }\n      };\n    },\n\n    // Handle PQE owner change\n    async handlePQEOwnerChange() {\n      console.log(`Selected PQE owner: ${this.selectedPQEOwner}`);\n      await this.fetchValidationData();\n      await this.fetchClassificationCounts();\n      this.updateValidationsSummary();\n    },\n\n    // Handle time filter change\n    async handleTimeFilterChange() {\n      console.log(`Selected time filter: ${this.selectedTimeFilter}`);\n      await this.fetchValidationData();\n      await this.fetchClassificationCounts();\n      this.updateValidationsSummary();\n    },\n\n    async fetchValidationData() {\n      this.loadingData = true;\n      try {\n        const response = await fetch(`${process.env.VUE_APP_API_PATH || '/api-statit2/'}get_validation_data`, {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\"\n          },\n          body: JSON.stringify({\n            timeFilter: this.selectedTimeFilter,\n            pqeOwner: this.selectedPQEOwner !== 'All' ? this.selectedPQEOwner : null\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n\n        const data = await response.json();\n        this.validationData = data.data;\n\n        // Apply filters to the data\n        this.applyFilters();\n        this.updateValidationsSummary();\n      } catch (error) {\n        console.error(\"Error fetching validation data:\", error);\n      } finally {\n        this.loadingData = false;\n      }\n    },\n\n    // Update validations summary\n    updateValidationsSummary() {\n      if (!this.validationData || this.validationData.length === 0) {\n        this.validationsSummary = {\n          total: 0,\n          validated: 0,\n          unvalidated: 0,\n          validatedPercentage: 0,\n          unvalidatedPercentage: 0\n        };\n        return;\n      }\n\n      const total = this.validationData.length;\n      const validated = this.validationData.filter(item =>\n        item.classification &&\n        (typeof item.classification === 'string' || item.classification.primary)\n      ).length;\n      const unvalidated = total - validated;\n\n      this.validationsSummary = {\n        total,\n        validated,\n        unvalidated,\n        validatedPercentage: total > 0 ? Math.round((validated / total) * 100) : 0,\n        unvalidatedPercentage: total > 0 ? Math.round((unvalidated / total) * 100) : 0\n      };\n    },\n\n    async applyFilters() {\n      this.loadingData = true;\n      // Set default values for filters since dropdowns were removed\n      this.primaryFilter = 'all';\n      this.subcategoryFilter = 'all';\n\n      try {\n        // Use the server-side filtering endpoint\n        const response = await fetch(`${process.env.VUE_APP_API_PATH || '/api-statit2/'}query_by_classification`, {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\"\n          },\n          body: JSON.stringify({\n            timeFilter: this.selectedTimeFilter,\n            primaryFilter: this.primaryFilter,\n            subcategoryFilter: this.subcategoryFilter\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n\n        const data = await response.json();\n        this.filteredData = data.data;\n\n        // Update the validation data as well to keep it in sync\n        this.validationData = data.data;\n      } catch (error) {\n        console.error(\"Error applying filters:\", error);\n\n        // Fallback to client-side filtering if server-side fails\n        if (!this.validationData || this.validationData.length === 0) {\n          this.filteredData = [];\n          return;\n        }\n\n        // Filter the data based on primary and subcategory filters\n        this.filteredData = this.validationData.filter(item => {\n          // Handle missing classification\n          if (!item.classification) {\n            return (this.primaryFilter === 'all' || this.primaryFilter === 'Need More Info') &&\n                   (this.subcategoryFilter === 'all' || this.subcategoryFilter === 'Need More Info');\n          }\n\n          // Handle string classification (backward compatibility)\n          if (typeof item.classification === 'string') {\n            const primary = item.classification;\n            return (this.primaryFilter === 'all' || this.primaryFilter === primary) &&\n                   (this.subcategoryFilter === 'all' || this.subcategoryFilter === 'Other');\n          }\n\n          // Handle object classification\n          const primary = item.classification.primary || 'Need More Info';\n          const subcategory = item.classification.subcategory || 'Need More Info';\n\n          return (this.primaryFilter === 'all' || this.primaryFilter === primary) &&\n                 (this.subcategoryFilter === 'all' || this.subcategoryFilter === subcategory);\n        });\n      } finally {\n        this.loadingData = false;\n      }\n    },\n\n    async fetchClassificationCounts() {\n      this.loadingCounts = true;\n      try {\n        const response = await fetch(`${process.env.VUE_APP_API_PATH || '/api-statit2/'}get_classification_counts`, {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\"\n          },\n          body: JSON.stringify({\n            timeFilter: this.selectedTimeFilter\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n\n        const data = await response.json();\n        this.classificationCounts = data.data;\n      } catch (error) {\n        console.error(\"Error fetching classification counts:\", error);\n      } finally {\n        this.loadingCounts = false;\n      }\n    },\n\n    async processNewDefects() {\n      this.processingDefects = true;\n      this.processingMessage = 'Starting AI classification...';\n      try {\n        const response = await fetch(`${process.env.VUE_APP_API_PATH || '/api-statit2/'}process_new_defects`, {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\"\n          },\n          body: JSON.stringify({\n            api_key: 'A-9UZe48N156o-lLuxvjR5IePXDhKwrVHu61UciQ9afl',\n            project_id: '8a74c0cb-b3c8-4fd7-9823-8301276169de',\n            timeFilter: this.selectedTimeFilter // Pass the current time filter\n          })\n        });\n\n        // Check for HTTP errors\n        if (!response.ok) {\n          const errorText = await response.text();\n          let errorMessage;\n          try {\n            const errorJson = JSON.parse(errorText);\n            errorMessage = errorJson.message || `HTTP error! status: ${response.status}`;\n          } catch (e) {\n            errorMessage = `HTTP error! status: ${response.status}: ${errorText}`;\n          }\n          throw new Error(errorMessage);\n        }\n\n        const data = await response.json();\n        console.log(\"Processing result:\", data);\n\n        // Check for API errors\n        if (data.status === 'error') {\n          throw new Error(data.message || 'Unknown error occurred');\n        }\n\n        // Update processing message\n        if (data.message) {\n          this.processingMessage = data.message;\n        }\n\n        // Refresh data after processing\n        setTimeout(() => {\n          this.fetchValidationData();\n          this.fetchClassificationCounts();\n          this.processingDefects = false;\n\n          // Keep the message visible for a bit longer\n          setTimeout(() => {\n            this.processingMessage = '';\n          }, 5000);\n        }, 2000);\n      } catch (error) {\n        console.error(\"Error processing defects:\", error);\n        this.processingDefects = false;\n        this.processingMessage = `Error: ${error.message}`;\n\n        // Clear error message after a delay\n        setTimeout(() => {\n          this.processingMessage = '';\n        }, 10000); // Show error for longer (10 seconds)\n      }\n    },\n\n    async updateClassification(defectId, classification) {\n      try {\n        const response = await fetch(`${process.env.VUE_APP_API_PATH || '/api-statit2/'}update_classification`, {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\"\n          },\n          body: JSON.stringify({\n            defectId,\n            classification\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n\n        // Update local data\n        const defect = this.validationData.find(d => d.DEFECT_ID === defectId);\n        if (defect) {\n          defect.classification = classification;\n        }\n\n        // Refresh classification counts\n        this.fetchClassificationCounts();\n      } catch (error) {\n        console.error(\"Error updating classification:\", error);\n      }\n    },\n\n\n    resetFilters() {\n      // Filters are already set to 'all' in applyFilters method\n      this.applyFilters();\n    },\n\n    formatDate(dateString) {\n      if (!dateString) return '';\n      const date = new Date(dateString);\n      if (isNaN(date.getTime())) return dateString;\n      return date.toLocaleDateString();\n    },\n\n    // Helper methods for classification\n    getPrimaryClassification(row) {\n      if (!row.classification) return null;\n\n      // Handle string classification (backward compatibility)\n      if (typeof row.classification === 'string') {\n        return row.classification;\n      }\n\n      // Handle object classification\n      return row.classification.primary || 'Need More Info';\n    },\n\n    getSubcategoryClassification(row) {\n      if (!row.classification) return null;\n\n      // Handle string classification (backward compatibility)\n      if (typeof row.classification === 'string') {\n        return 'Other';\n      }\n\n      // Handle object classification\n      return row.classification.subcategory || 'Need More Info';\n    },\n\n    getSubcategoryClass(row) {\n      const subcategory = this.getSubcategoryClassification(row);\n      if (!subcategory) return '';\n\n      // Convert to lowercase and remove spaces for CSS class\n      return subcategory.toLowerCase().replace(/\\s+/g, '-');\n    },\n\n    getSubcategoryClassFromString(subcategory) {\n      if (!subcategory) return '';\n\n      // Convert to lowercase and remove spaces for CSS class\n      return subcategory.toLowerCase().replace(/\\s+/g, '-');\n    },\n\n    async processLimitedBatch() {\n      if (this.batchSize < 1 || this.batchSize > 20) {\n        this.processingMessage = 'Batch size must be between 1 and 20';\n        setTimeout(() => {\n          this.processingMessage = '';\n        }, 3000);\n        return;\n      }\n      let token = this.$store.getters.getToken;\n      this.processingDefects = true;\n      this.processingMessage = `Processing batch of ${this.batchSize} defects...`;\n      try {\n        const response = await fetch(`${process.env.VUE_APP_API_PATH || '/api-statit2/'}process_limited_batch`, {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \" + token\n          },\n          body: JSON.stringify({\n            api_key: 'A-9UZe48N156o-lLuxvjR5IePXDhKwrVHu61UciQ9afl',\n            project_id: '8a74c0cb-b3c8-4fd7-9823-8301276169de',\n            timeFilter: this.selectedTimeFilter,\n            batchSize: this.batchSize\n          })\n        });\n\n        // Check for HTTP errors\n        if (!response.ok) {\n          const errorText = await response.text();\n          let errorMessage;\n          try {\n            const errorJson = JSON.parse(errorText);\n            errorMessage = errorJson.message || `HTTP error! status: ${response.status}`;\n          } catch (e) {\n            errorMessage = `HTTP error! status: ${response.status}: ${errorText}`;\n          }\n          throw new Error(errorMessage);\n        }\n\n        const data = await response.json();\n        console.log(\"Batch processing result:\", data);\n\n        // Check for API errors\n        if (data.status === 'error') {\n          throw new Error(data.message || 'Unknown error occurred');\n        }\n\n        // Update processing message\n        if (data.message) {\n          this.processingMessage = data.message;\n        }\n\n        // Store the batch results\n        this.batchResults = data.results || [];\n\n        // Show the batch results modal\n        if (this.batchResults.length > 0) {\n          this.showBatchResultsModal = true;\n        }\n\n        // Refresh data\n        this.fetchValidationData();\n        this.fetchClassificationCounts();\n        this.processingDefects = false;\n\n      } catch (error) {\n        console.error(\"Error processing batch:\", error);\n        this.processingDefects = false;\n        this.processingMessage = `Error: ${error.message}`;\n\n        // Clear error message after a delay\n        setTimeout(() => {\n          this.processingMessage = '';\n        }, 10000); // Show error for longer (10 seconds)\n      }\n    },\n\n    closeBatchResultsModal() {\n      this.showBatchResultsModal = false;\n      this.batchResults = [];\n    },\n\n    // Modal methods\n    openClassificationModal(row) {\n      this.selectedDefect = row;\n\n      // Initialize with current classification or defaults\n      if (row.classification) {\n        if (typeof row.classification === 'string') {\n          // Handle string classification (backward compatibility)\n          this.editClassification = {\n            primary: row.classification,\n            subcategory: 'Other'\n          };\n        } else {\n          // Handle object classification\n          this.editClassification = {\n            primary: row.classification.primary || 'Need More Info',\n            subcategory: row.classification.subcategory || 'Need More Info'\n          };\n        }\n      } else {\n        // Default values for new classification\n        this.editClassification = {\n          primary: 'Mechanical',\n          subcategory: 'Other'\n        };\n      }\n\n      this.showClassificationModal = true;\n    },\n\n    closeClassificationModal() {\n      this.showClassificationModal = false;\n      this.selectedDefect = null;\n    },\n\n    async saveClassification() {\n      if (!this.selectedDefect) return;\n\n      try {\n        const defectId = this.selectedDefect.DEFECT_ID;\n        const classification = { ...this.editClassification };\n\n        const response = await fetch(`${process.env.VUE_APP_API_PATH || '/api-statit2/'}update_classification`, {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\"\n          },\n          body: JSON.stringify({\n            defectId,\n            classification\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n\n        // Update local data\n        const defect = this.validationData.find(d => d.DEFECT_ID === defectId);\n        if (defect) {\n          defect.classification = classification;\n        }\n\n        // Refresh classification counts and apply filters\n        this.fetchClassificationCounts();\n        this.applyFilters();\n\n        // Close the modal\n        this.closeClassificationModal();\n      } catch (error) {\n        console.error(\"Error updating classification:\", error);\n      }\n    },\n\n    // Utility methods for Carbon components\n    getClassificationTagKind(classification) {\n      if (!classification) return 'red';\n\n      const classType = classification.toLowerCase();\n      switch (classType) {\n        case 'mechanical':\n          return 'purple';\n        case 'functional':\n          return 'blue';\n        case 'need more info':\n          return 'orange';\n        default:\n          return 'gray';\n      }\n    },\n\n    formatDate(dateString) {\n      if (!dateString) return 'N/A';\n      try {\n        const date = new Date(dateString);\n        return date.toLocaleDateString();\n      } catch (error) {\n        return dateString;\n      }\n    }\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n.validation2-container {\n  min-height: 100vh;\n  background-color: #161616;\n  color: #f4f4f4;\n}\n\n.main-content {\n  padding: 2rem;\n}\n\n.page-header {\n  margin-bottom: 2rem;\n\n  h1 {\n    font-size: 2rem;\n    margin-bottom: 0.5rem;\n  }\n\n  p {\n    color: #c6c6c6;\n  }\n}\n\n.summary-section {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 1rem;\n  margin-bottom: 2rem;\n\n  .summary-tile {\n    padding: 1.5rem;\n\n    h4 {\n      margin: 0 0 0.5rem 0;\n      font-size: 0.875rem;\n      color: #c6c6c6;\n      text-transform: uppercase;\n      letter-spacing: 0.16px;\n    }\n\n    .tile-value {\n      font-size: 2rem;\n      font-weight: 600;\n      margin: 0;\n\n      &.validated {\n        color: #42be65;\n      }\n\n      &.unvalidated {\n        color: #fa4d56;\n      }\n    }\n\n    .tile-percentage {\n      font-size: 0.875rem;\n      color: #c6c6c6;\n      margin: 0.25rem 0 0 0;\n    }\n  }\n}\n\n.controls-row {\n  display: flex;\n  align-items: flex-start;\n  margin-bottom: 2rem;\n  gap: 1rem;\n  flex-wrap: wrap;\n\n  .filter-group {\n    min-width: 200px;\n  }\n}\n\n.time-period-dropdown {\n  display: flex;\n  flex-direction: column;\n  min-width: 200px;\n}\n\n.button-group {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n\n.batch-process-controls {\n  display: flex;\n  align-items: flex-end;\n  gap: 1rem;\n}\n\n.batch-size-input {\n  display: flex;\n  flex-direction: column;\n  width: 80px;\n}\n\n.cv-input {\n  background-color: #262626;\n  color: #f4f4f4;\n  border: 1px solid #525252;\n  border-radius: 4px;\n  padding: 0.5rem;\n  font-size: 0.875rem;\n  height: 40px;\n  width: 100%;\n}\n\n.dropdown-label {\n  font-size: 0.75rem;\n  margin-bottom: 0.5rem;\n  color: #c6c6c6;\n}\n\n.cv-dropdown {\n  background-color: #262626;\n  color: #f4f4f4;\n  border: 1px solid #525252;\n  border-radius: 4px;\n  padding: 0.5rem;\n  font-size: 0.875rem;\n  height: 40px;\n  width: 100%;\n  appearance: none;\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16'%3E%3Cpath fill='%23f4f4f4' d='M8 11L3 6h10z'/%3E%3C/svg%3E\");\n  background-repeat: no-repeat;\n  background-position: right 0.5rem center;\n  cursor: pointer;\n}\n\n.cv-dropdown:focus {\n  outline: 2px solid #0f62fe;\n  outline-offset: -2px;\n}\n\n.pareto-section, .defects-table-section {\n  margin-bottom: 2rem;\n\n  h2 {\n    margin-bottom: 1rem;\n    font-size: 1.5rem;\n  }\n}\n\n.chart-container {\n  height: 400px;\n  width: 100%;\n}\n\n.loading-indicator {\n  display: flex;\n  justify-content: center;\n  padding: 2rem;\n}\n\n.table-container {\n  margin-top: 1rem;\n}\n\n.classification-cell {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n}\n\n.classification-tag {\n  padding: 0.25rem 0.5rem;\n  border-radius: 1rem;\n  font-size: 0.875rem;\n  font-weight: 600;\n\n  &.mechanical {\n    background-color: #8a3ffc;\n    color: white;\n  }\n\n  &.functional {\n    background-color: #33b1ff;\n    color: white;\n  }\n\n  &.unclassified {\n    background-color: #da1e28;\n    color: white;\n  }\n\n  &.need-more-info {\n    background-color: #ff832b;\n    color: white;\n  }\n\n  &.subcategory {\n    &.scratches {\n      background-color: #6929c4;\n      color: white;\n    }\n\n    &.bent {\n      background-color: #1192e8;\n      color: white;\n    }\n\n    &.plugging {\n      background-color: #005d5d;\n      color: white;\n    }\n\n    &.discolor {\n      background-color: #9f1853;\n      color: white;\n    }\n\n    &.misalignment {\n      background-color: #fa4d56;\n      color: white;\n    }\n\n    &.other {\n      background-color: #4589ff;\n      color: white;\n    }\n  }\n}\n\n.edit-button {\n  background-color: #393939;\n  color: #f4f4f4;\n  border: 1px solid #525252;\n  border-radius: 4px;\n  padding: 0.25rem 0.5rem;\n  font-size: 0.75rem;\n  cursor: pointer;\n  margin-left: 0.5rem;\n\n  &:hover {\n    background-color: #4c4c4c;\n  }\n}\n\n.filter-dropdown {\n  display: flex;\n  flex-direction: column;\n  min-width: 180px;\n  margin-right: 1rem;\n}\n\n.processing-message {\n  margin-left: 1rem;\n  color: #33b1ff;\n  font-size: 0.875rem;\n  animation: pulse 2s infinite;\n}\n\n@keyframes pulse {\n  0% {\n    opacity: 0.6;\n  }\n  50% {\n    opacity: 1;\n  }\n  100% {\n    opacity: 0.6;\n  }\n}\n\n/* Modal styles */\n.modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.7);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 1000;\n}\n\n.modal-content {\n  background-color: #262626;\n  border-radius: 4px;\n  padding: 2rem;\n  width: 90%;\n  max-width: 500px;\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);\n\n  h3 {\n    margin-top: 0;\n    margin-bottom: 1rem;\n    font-size: 1.5rem;\n  }\n\n  .description-text {\n    margin-bottom: 1.5rem;\n    padding: 0.75rem;\n    background-color: #393939;\n    border-radius: 4px;\n    max-height: 100px;\n    overflow-y: auto;\n  }\n}\n\n.batch-results-modal {\n  max-width: 800px;\n  max-height: 90vh;\n  overflow-y: auto;\n}\n\n.batch-results-container {\n  margin: 1rem 0;\n  max-height: 60vh;\n  overflow-y: auto;\n  border: 1px solid #525252;\n  border-radius: 4px;\n}\n\n.batch-results-table {\n  width: 100%;\n  border-collapse: collapse;\n\n  th, td {\n    padding: 0.75rem;\n    text-align: left;\n    border-bottom: 1px solid #525252;\n  }\n\n  th {\n    background-color: #393939;\n    font-weight: 600;\n  }\n\n  tr:hover {\n    background-color: #333333;\n  }\n\n  .description-cell {\n    max-width: 300px;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    white-space: nowrap;\n  }\n}\n\n.modal-form {\n  margin-bottom: 1.5rem;\n\n  .form-group {\n    margin-bottom: 1rem;\n\n    label {\n      display: block;\n      margin-bottom: 0.5rem;\n      font-weight: 600;\n    }\n  }\n\n  .modal-select {\n    width: 100%;\n    padding: 0.5rem;\n    background-color: #393939;\n    color: #f4f4f4;\n    border: 1px solid #525252;\n    border-radius: 4px;\n    font-size: 1rem;\n  }\n}\n\n.modal-actions {\n  display: flex;\n  justify-content: flex-end;\n  gap: 1rem;\n\n  .modal-button {\n    padding: 0.5rem 1rem;\n    border-radius: 4px;\n    font-size: 1rem;\n    cursor: pointer;\n\n    &.cancel {\n      background-color: #393939;\n      color: #f4f4f4;\n      border: 1px solid #525252;\n\n      &:hover {\n        background-color: #4c4c4c;\n      }\n    }\n\n    &.save {\n      background-color: #0f62fe;\n      color: white;\n      border: none;\n\n      &:hover {\n        background-color: #0353e9;\n      }\n    }\n  }\n}\n</style>\n"]}]}