const authentication = require('../../auth');


module.exports = (app, express) => {
    //Verify token for all POST requests except for routed specified in auth.js
    app.post('*', authentication);

    let router = express.Router();
    const admin = require('../controllers/admin.controller')
    const com = require('../controllers/com.controller')
    const wx = require('../controllers/watsonX.controller')
    const auth = require('../controllers/authentication')
    const w_auth =  require('../controllers/watsonX.auth')
    const partgroup = require('../controllers/partgroup.controller')
    const metis = require('../controllers/metis.controller')
    const actionTracker = require('../controllers/action-tracker.controller')
    const pqeDashboard = require('../controllers/pqe-dashboard.controller')
    const validation2 = require('../controllers/validation2.controller')
    const log = require('../controllers/log.controller')

    //watsonX routes
    router.post('/populate_watsonx_line', wx.populate_watsonx_line)
    router.post('/transcribe', wx.transcribe)
    router.post('/watsonx_prompt', wx.prompt_watsonx)

    //watsonX auth
    router.post('/getAuthTokensClient', w_auth.getAuthTokensClient)

    // Commodity Routes
    router.post('/get_pn_fpy_volumes', com.get_pn_fpy_volumes)
    router.post('/get_pns_from_excel', com.get_pns_from_excel)
    router.post('/get_fail_count_fpy', com.get_fail_count_fpy)
    router.post('/get_defect_percentages_fpy', com.get_defect_percentages_fpy)
    router.post('/get_xfactor_base', com.get_xfactor_base)
    router.post('/createPnPercentageArray', com.createPnPercentageArray)

    //Admin Routes
    router.post('/populate_chart', admin.populate_chart)
    router.post('/populate_xf_chart', admin.populate_xf_chart)
    router.post('/populate_heatmap', admin.populate_heatmap)
    router.post('/populate_chart2', admin.populate_chart2)
    router.post('/populate_line_chart', admin.populate_line_chart)
    router.post('/check_db', admin.check_db)
    router.post('/check_db2', admin.check_db2)
    router.post('/get_fail_count_by_category', admin.get_fail_count_by_category)
    router.post('/get_fail_count_by_category_db', admin.get_fail_count_by_category_db)
    router.post('/get_fails_drill', admin.get_fails_drill)

    router.post('/get_unique_categories', admin.get_unique_categories)
    router.post('/get_unique_categories_db', admin.get_unique_categories_db)
    router.post('/get_unique_pns', admin.get_unique_pns)
    router.post('/get_unique_categories_db2', admin.get_unique_categories_db2)
    router.post('/get_unval', admin.get_unval)

    //Part Group Analysis Routes
    router.post('/get_part_groups', partgroup.get_part_groups)
    router.post('/get_part_group_data', partgroup.get_part_group_data)
    router.post('/get_all_excel_data', partgroup.get_all_excel_data)
    router.post('/get_date_options', (req, res) => {
        partgroup.get_date_options(req.body, (err, result) => {
            if (err) {
                console.error('Error getting date options:', err);
                return res.status(500).json({ status_res: "error", message: err.message });
            }
            return res.json(result);
        });
    })

    //Metis XFactors Routes
    router.post('/get_metis_part_numbers', metis.get_metis_part_numbers)
    router.post('/get_metis_breakout_names', metis.get_metis_breakout_names)
    router.post('/get_metis_owning_groups', metis.get_metis_owning_groups)
    router.post('/get_metis_owners', metis.get_metis_owners)
    router.post('/get_metis_xfactors', metis.get_metis_xfactors)
    router.post('/get_metis_breakout_analysis', metis.get_metis_breakout_analysis)
    router.post('/get_metis_category_analysis', metis.get_metis_category_analysis)
    router.post('/get_metis_failure_modes', metis.get_metis_failure_modes)


    //Action Tracker Routes
    router.post('/get_action_tracker_data', actionTracker.get_action_tracker_data)
    router.post('/save_action_tracker_data', actionTracker.save_action_tracker_data)
    router.post('/update_action_tracker_data', actionTracker.update_action_tracker_data)
    router.post('/add_action_update', actionTracker.add_action_update)

    //PQE Dashboard Routes
    router.post('/get_daily_fails', pqeDashboard.get_daily_fails)
    router.post('/get_validation_counts', pqeDashboard.get_validation_counts)
    router.post('/get_pqe_critical_issues', pqeDashboard.get_pqe_critical_issues)
    router.post('/get_pqe_resolved_issues', pqeDashboard.get_pqe_resolved_issues)
    router.post('/get_pqe_outstanding_issues', pqeDashboard.get_pqe_outstanding_issues)
    router.post('/get_pqe_breakout_groups', pqeDashboard.get_pqe_breakout_groups)
    router.post('/get_pqe_owners', pqeDashboard.get_pqe_owners)
    router.post('/get_all_pqe_data', pqeDashboard.get_all_pqe_data)
    router.post('/update_pqe_action', pqeDashboard.update_pqe_action)
    router.post('/get_pqe_chart_data', pqeDashboard.get_pqe_chart_data)
    router.post('/get_pqe_root_cause_analysis', pqeDashboard.get_pqe_root_cause_analysis)
    router.post('/get_pqe_alert_history', pqeDashboard.get_pqe_alert_history)
    router.post('/add_pqe_alert_update', pqeDashboard.add_pqe_alert_update)
    router.post('/get_pqe_alert_ai_insight', pqeDashboard.get_pqe_alert_ai_insight)

    //Validation 2 Routes
    router.post('/get_validation_data', validation2.get_validation_data)
    router.post('/get_classification_counts', validation2.get_classification_counts)
    router.post('/process_new_defects', validation2.process_new_defects)
    router.post('/process_limited_batch', validation2.process_limited_batch)
    router.post('/update_classification', validation2.update_classification)
    router.post('/query_by_classification', validation2.query_by_classification)
    router.post('/get_pqe_part_numbers', validation2.get_pqe_part_numbers)

    //Auth Routes
    router.post("/login", auth.authUser)

    //Logging Routes
    router.post("/log", log.handleLog)

    app.use('/api-statit2/', router);

    app.get('/health/ping', (req, res) => {
        res.status(200).json({ status: 'ok' });
    });

};