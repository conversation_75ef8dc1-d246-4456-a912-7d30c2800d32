{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\PQEDashboard\\PQEOwnerDashboard.vue?vue&type=style&index=0&id=25dba680&scoped=true&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\PQEDashboard\\PQEOwnerDashboard.vue", "mtime": 1748988380528}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\@vue\\cli-service\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["PQEOwnerDashboard.vue"], "names": [], "mappings": ";AA+3DA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "PQEOwnerDashboard.vue", "sourceRoot": "src/views/PQEDashboard", "sourcesContent": ["<template>\n  <div class=\"pqe-owner-dashboard-container\">\n    <div class=\"content-wrapper\">\n      <!-- Header Section with Critical Issues Summary -->\n      <div class=\"dashboard-header\">\n        <h3>{{ pqeOwner }}'s Dashboard</h3>\n        <div class=\"last-updated-text\">\n          Last Updated: {{ new Date().toLocaleString() }}\n        </div>\n      </div>\n\n      <!-- Key Metrics Section -->\n      <div class=\"key-metrics-section\">\n        <div class=\"metric-card clickable\" @click=\"scrollToAlerts\" title=\"Click to go to alerts section\">\n          <div class=\"metric-icon alerts\">\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n              <path d=\"M16,2A14,14,0,1,0,30,16,14,14,0,0,0,16,2Zm0,26A12,12,0,1,1,28,16,12,12,0,0,1,16,28Z\"></path>\n              <path d=\"M15,8h2v11H15Zm1,14a1.5,1.5,0,1,1-1.5,1.5A1.5,1.5,0,0,1,16,22Z\"></path>\n            </svg>\n          </div>\n          <div class=\"metric-content\">\n            <div class=\"metric-label\"># Alerts</div>\n            <div class=\"metric-value\">{{ totalAlerts }}</div>\n            <div class=\"metric-description\">Current Month</div>\n          </div>\n        </div>\n\n        <div class=\"metric-card clickable\" @click=\"goToActionTracker\" title=\"Click to go to Action Tracker\">\n          <div class=\"metric-icon in-progress\">\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n              <path d=\"M16,2A14,14,0,1,0,30,16,14,14,0,0,0,16,2Zm0,26A12,12,0,1,1,28,16,12,12,0,0,1,16,28Z\"></path>\n              <path d=\"M20.59,22,15,16.41V7h2v8.58l5,5.01L20.59,22z\"></path>\n            </svg>\n          </div>\n          <div class=\"metric-content\">\n            <div class=\"metric-label\"># In Progress Issues</div>\n            <div class=\"metric-value\">{{ inProgressIssuesCount }}</div>\n            <div class=\"metric-description\">From Action Tracker</div>\n          </div>\n        </div>\n\n        <div class=\"metric-card clickable\" @click=\"goToValidationPage\" title=\"Click to go to Validation page\">\n          <div class=\"metric-icon validated\">\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n              <path d=\"M26,4H6A2,2,0,0,0,4,6V26a2,2,0,0,0,2,2H26a2,2,0,0,0,2-2V6A2,2,0,0,0,26,4ZM14,21.5l-5-5,1.59-1.5L14,18.35,21.41,11,23,12.58Z\"></path>\n            </svg>\n          </div>\n          <div class=\"metric-content\">\n            <div class=\"metric-label\">Validated Fails</div>\n            <div class=\"metric-value\">{{ validatedCount }}/{{ totalFails }}</div>\n            <div class=\"metric-description\">This Month</div>\n          </div>\n        </div>\n\n        <div class=\"metric-card clickable\" @click=\"goToHeatmap\" title=\"Click to go to Heatmap\">\n          <div class=\"metric-icon groups-over-target\">\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n              <path d=\"M4,20V12a2,2,0,0,1,2-2H8a2,2,0,0,1,2,2v8a2,2,0,0,1-2,2H6A2,2,0,0,1,4,20Z\"></path>\n              <path d=\"M12,20V6a2,2,0,0,1,2-2h2a2,2,0,0,1,2,2V20a2,2,0,0,1-2,2H14A2,2,0,0,1,12,20Z\"></path>\n              <path d=\"M20,20V16a2,2,0,0,1,2-2h2a2,2,0,0,1,2,2v4a2,2,0,0,1-2,2H22A2,2,0,0,1,20,20Z\"></path>\n            </svg>\n          </div>\n          <div class=\"metric-content\">\n            <div class=\"metric-label\"># Groups Over Target</div>\n            <div class=\"metric-value\">{{ groupsOverTargetCount }}</div>\n            <div class=\"metric-description\">Current Month</div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Main Content Layout -->\n      <div class=\"dashboard-main-content\">\n        <!-- Left Column -->\n        <div class=\"dashboard-column\">\n          <!-- Action Tracker Alerts Section -->\n          <div class=\"section-card\">\n            <div class=\"section-header\" @click=\"toggleActionTrackerAlertsExpanded\">\n              <div class=\"section-title-container\">\n                <h4 class=\"section-title\">Action Tracker Alerts</h4>\n                <div class=\"section-subtitle\">\n                  Issues from action tracker above target for the month\n                </div>\n              </div>\n              <div class=\"section-controls\">\n                <div class=\"status-indicator flashing\" v-if=\"totalActionTrackerAlerts > 0\">\n                  {{ totalActionTrackerAlerts }}\n                </div>\n                <div class=\"expand-indicator\" :class=\"{ expanded: isActionTrackerAlertsExpanded }\">\n                  <svg width=\"16\" height=\"16\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                    <path d=\"M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z\"></path>\n                  </svg>\n                </div>\n              </div>\n            </div>\n\n            <div v-if=\"isActionTrackerAlertsExpanded\" class=\"section-content\">\n              <!-- In-Progress Issue Alerts -->\n              <div class=\"alert-subsection\">\n                <div class=\"subsection-header\" @click=\"toggleInProgressAlertsExpanded\">\n                  <h5 class=\"subsection-title\">In-Progress Issue Alerts</h5>\n                  <div class=\"subsection-controls\">\n                    <div class=\"status-indicator\" v-if=\"inProgressAlerts.length > 0\" style=\"background-color: #ff832b;\">\n                      {{ inProgressAlerts.length }}\n                    </div>\n                    <div class=\"expand-indicator\" :class=\"{ expanded: isInProgressAlertsExpanded }\">\n                      <svg width=\"12\" height=\"12\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                        <path d=\"M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z\"></path>\n                      </svg>\n                    </div>\n                  </div>\n                </div>\n\n                <div v-if=\"isInProgressAlertsExpanded\" class=\"subsection-content\">\n                  <div v-if=\"inProgressAlerts.length > 0\" class=\"issues-list\">\n                    <div v-for=\"alert in inProgressAlerts\" :key=\"alert.id\" class=\"issue-card action-tracker-alert\">\n                      <div class=\"issue-header\">\n                        <div class=\"issue-tags\">\n                          <cv-tag kind=\"orange\" label=\"In-Progress\" />\n                          <cv-tag kind=\"red\" :label=\"alert.severity\" />\n                        </div>\n                        <div class=\"issue-title\">{{ alert.category }}</div>\n                        <div class=\"issue-metadata\">\n                          <span class=\"issue-multiplier medium-severity\">{{ alert.xFactor }}x</span>\n                        </div>\n                      </div>\n                      <div class=\"issue-actions\">\n                        <cv-button\n                          kind=\"primary\"\n                          size=\"small\"\n                          @click=\"viewActionTrackerAlert(alert)\"\n                        >\n                          View\n                        </cv-button>\n                      </div>\n                    </div>\n                  </div>\n                  <div v-else class=\"no-data-message\">\n                    No in-progress issues above target this month.\n                  </div>\n                </div>\n              </div>\n\n              <!-- Outstanding Issue Alerts -->\n              <div class=\"alert-subsection\">\n                <div class=\"subsection-header\" @click=\"toggleOutstandingAlertsExpanded\">\n                  <h5 class=\"subsection-title\">Outstanding Issue Alerts</h5>\n                  <div class=\"subsection-controls\">\n                    <div class=\"status-indicator\" v-if=\"outstandingAlerts.length > 0\" style=\"background-color: #0f62fe;\">\n                      {{ outstandingAlerts.length }}\n                    </div>\n                    <div class=\"expand-indicator\" :class=\"{ expanded: isOutstandingAlertsExpanded }\">\n                      <svg width=\"12\" height=\"12\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                        <path d=\"M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z\"></path>\n                      </svg>\n                    </div>\n                  </div>\n                </div>\n\n                <div v-if=\"isOutstandingAlertsExpanded\" class=\"subsection-content\">\n                  <div v-if=\"outstandingAlerts.length > 0\" class=\"issues-list\">\n                    <div v-for=\"alert in outstandingAlerts\" :key=\"alert.id\" class=\"issue-card action-tracker-alert\">\n                      <div class=\"issue-header\">\n                        <div class=\"issue-tags\">\n                          <cv-tag kind=\"blue\" label=\"Outstanding\" />\n                          <cv-tag kind=\"red\" :label=\"alert.severity\" />\n                        </div>\n                        <div class=\"issue-title\">{{ alert.category }}</div>\n                        <div class=\"issue-metadata\">\n                          <span class=\"issue-multiplier medium-severity\">{{ alert.xFactor }}x</span>\n                        </div>\n                      </div>\n                      <div class=\"issue-actions\">\n                        <cv-button\n                          kind=\"primary\"\n                          size=\"small\"\n                          @click=\"viewActionTrackerAlert(alert)\"\n                        >\n                          View\n                        </cv-button>\n                      </div>\n                    </div>\n                  </div>\n                  <div v-else class=\"no-data-message\">\n                    No outstanding issues above target this month.\n                  </div>\n                </div>\n              </div>\n\n              <!-- Resolved Issue Alerts -->\n              <div class=\"alert-subsection\">\n                <div class=\"subsection-header\" @click=\"toggleResolvedAlertsExpanded\">\n                  <h5 class=\"subsection-title\">Resolved Issue Alerts</h5>\n                  <div class=\"subsection-controls\">\n                    <div class=\"status-indicator\" v-if=\"resolvedAlerts.length > 0\" style=\"background-color: #24a148;\">\n                      {{ resolvedAlerts.length }}\n                    </div>\n                    <div class=\"expand-indicator\" :class=\"{ expanded: isResolvedAlertsExpanded }\">\n                      <svg width=\"12\" height=\"12\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                        <path d=\"M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z\"></path>\n                      </svg>\n                    </div>\n                  </div>\n                </div>\n\n                <div v-if=\"isResolvedAlertsExpanded\" class=\"subsection-content\">\n                  <div v-if=\"resolvedAlerts.length > 0\" class=\"issues-list\">\n                    <div v-for=\"alert in resolvedAlerts\" :key=\"alert.id\" class=\"issue-card action-tracker-alert\">\n                      <div class=\"issue-header\">\n                        <div class=\"issue-tags\">\n                          <cv-tag kind=\"green\" label=\"Resolved\" />\n                          <cv-tag kind=\"red\" :label=\"alert.severity\" />\n                        </div>\n                        <div class=\"issue-title\">{{ alert.category }}</div>\n                        <div class=\"issue-metadata\">\n                          <span class=\"issue-multiplier good-performance\">{{ alert.xFactor }}x</span>\n                        </div>\n                      </div>\n                      <div class=\"issue-actions\">\n                        <cv-button\n                          kind=\"primary\"\n                          size=\"small\"\n                          @click=\"viewActionTrackerAlert(alert)\"\n                        >\n                          View\n                        </cv-button>\n                      </div>\n                    </div>\n                  </div>\n                  <div v-else class=\"no-data-message\">\n                    No resolved issues above target this month.\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Critical Issues Section -->\n          <div class=\"section-card\">\n            <div class=\"section-header critical-issues-header\" @click=\"toggleCriticalIssuesExpanded\">\n              <div class=\"section-title-container\">\n                <h4 class=\"section-title\">Critical Issues</h4>\n                <div class=\"section-subtitle\">New/unknown issues requiring immediate attention this month</div>\n              </div>\n              <div class=\"section-controls\">\n                <div class=\"status-indicator\" :class=\"{ 'flashing': !isCriticalIssuesExpanded && unresolvedCriticalIssues.length > 0 }\">\n                  {{ unresolvedCriticalIssues.length }}\n                </div>\n                <div class=\"expand-indicator\" :class=\"{ 'expanded': isCriticalIssuesExpanded }\">\n                  <svg width=\"16\" height=\"16\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                    <path d=\"M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z\"></path>\n                  </svg>\n                </div>\n              </div>\n            </div>\n\n            <div v-if=\"isCriticalIssuesExpanded\" class=\"section-content\">\n              <!-- Filter Controls in a Single Row -->\n              <div class=\"filter-container\">\n                <div class=\"filter-header\">\n                  <h5 class=\"filter-title\">Filter Issues</h5>\n                  <cv-button\n                    kind=\"ghost\"\n                    size=\"small\"\n                    class=\"clear-filters-button\"\n                    @click=\"clearFilters\"\n                    v-if=\"isFiltersActive\"\n                  >\n                    Clear Filters\n                  </cv-button>\n                </div>\n\n                <div class=\"filter-controls\">\n                  <div class=\"filter-group\">\n                    <label for=\"severity-dropdown\" class=\"filter-label\">Severity:</label>\n                    <cv-dropdown\n                      id=\"severity-dropdown\"\n                      v-model=\"severityFilter\"\n                      @change=\"handleSeverityFilterChange\"\n                      class=\"filter-dropdown\"\n                    >\n                      <cv-dropdown-item value=\"all\">All Severities</cv-dropdown-item>\n                      <cv-dropdown-item value=\"high\">High</cv-dropdown-item>\n                      <cv-dropdown-item value=\"medium\">Medium</cv-dropdown-item>\n                    </cv-dropdown>\n                  </div>\n\n                  <div class=\"filter-group\">\n                    <label for=\"analysis-dropdown\" class=\"filter-label\">Analysis Type:</label>\n                    <cv-dropdown\n                      id=\"analysis-dropdown\"\n                      v-model=\"analysisTypeFilter\"\n                      @change=\"handleAnalysisFilterChange\"\n                      class=\"filter-dropdown\"\n                    >\n                      <cv-dropdown-item value=\"all\">All Analysis Types</cv-dropdown-item>\n                      <cv-dropdown-item value=\"Root Cause\">Root Cause</cv-dropdown-item>\n                    </cv-dropdown>\n                  </div>\n                </div>\n              </div>\n\n              <!-- Critical Issues List -->\n              <div v-if=\"filteredCriticalIssues.length > 0\" class=\"issues-list\">\n                <div\n                  v-for=\"issue in filteredCriticalIssues\"\n                  :key=\"issue.id\"\n                  class=\"issue-card\"\n                  @click=\"toggleIssueExpanded(issue)\"\n                  :class=\"{ 'expanded': isIssueExpanded(issue.id) }\"\n                >\n                  <div class=\"issue-header\">\n                    <div class=\"issue-tags\">\n                      <cv-tag\n                        :kind=\"issue.severity === 'high' ? 'red' : 'magenta'\"\n                        :label=\"issue.severity === 'high' ? 'High' : 'Medium'\"\n                      />\n                      <cv-tag\n                        kind=\"purple\"\n                        class=\"analysis-tag\"\n                        :label=\"issue.analysisType\"\n                      />\n                    </div>\n                    <span class=\"issue-title\">{{ issue.category }}</span>\n                    <div class=\"issue-metadata\">\n                      <cv-tag\n                        kind=\"cool-gray\"\n                        class=\"month-tag\"\n                        :label=\"issue.month\"\n                      />\n                      <span class=\"issue-multiplier\" :class=\"issue.severity === 'high' ? 'high-severity' : 'medium-severity'\">\n                        {{ issue.increaseMultiplier === '(new)' ? '(new)' : `${issue.increaseMultiplier}x` }}\n                      </span>\n                    </div>\n                    <div class=\"expand-indicator\">\n                      <svg width=\"16\" height=\"16\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                        <path d=\"M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z\"></path>\n                      </svg>\n                    </div>\n                  </div>\n\n                  <div class=\"issue-content\" v-if=\"isIssueExpanded(issue.id)\">\n                    <div class=\"ai-description\">\n                      <p>{{ issue.aiDescription || 'Loading AI description...' }}</p>\n                    </div>\n\n                    <!-- Action Comment Text Box -->\n                    <div class=\"action-comment\">\n                      <cv-text-area\n                        v-model=\"issue.comment\"\n                        label=\"Action Comments\"\n                        placeholder=\"Add your comments or action plan here...\"\n                        :helper-text=\"issue.comment ? `${issue.comment.length} characters` : 'This will be added to the Action Tracker'\"\n                      ></cv-text-area>\n                    </div>\n\n                    <div class=\"issue-actions\">\n                      <cv-button\n                        kind=\"tertiary\"\n                        size=\"small\"\n                        @click.stop=\"viewIssueDetails(issue)\"\n                      >\n                        View Data\n                      </cv-button>\n                      <cv-button\n                        kind=\"tertiary\"\n                        size=\"small\"\n                        @click.stop=\"updateIssue(issue)\"\n                      >\n                        Update\n                      </cv-button>\n                      <cv-button\n                        kind=\"secondary\"\n                        size=\"small\"\n                        @click.stop=\"updateIssue(issue, false, true)\"\n                      >\n                        Mark Outstanding\n                      </cv-button>\n                      <cv-button\n                        kind=\"primary\"\n                        size=\"small\"\n                        @click.stop=\"updateIssue(issue, true, false)\"\n                      >\n                        Mark Resolved\n                      </cv-button>\n                      <cv-button\n                        kind=\"ghost\"\n                        size=\"small\"\n                        @click.stop=\"addCriticalIssueToActionTracker(issue)\"\n                      >\n                        Add item to action tracker\n                      </cv-button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <div v-else class=\"no-data-message\">\n                No critical issues found matching the selected filters.\n              </div>\n            </div>\n          </div>\n\n          <!-- Root Cause Analysis Section -->\n          <div class=\"section-card chart-section\">\n            <div class=\"section-header\">\n              <div class=\"section-title-container\">\n                <h4 class=\"section-title\">Analysis</h4>\n                <div class=\"section-subtitle\">Defect categories and fail rates over time</div>\n              </div>\n            </div>\n\n            <div class=\"chart-controls\">\n              <div class=\"control-group\">\n                <label class=\"control-label\">View By:</label>\n                <cv-dropdown\n                  v-model=\"rootCauseViewBy\"\n                  @change=\"handleRootCauseViewByChange\"\n                  class=\"control-dropdown\"\n                >\n                  <cv-dropdown-item value=\"rootCause\">Root Cause</cv-dropdown-item>\n                  <cv-dropdown-item value=\"vintage\">Vintage</cv-dropdown-item>\n                  <cv-dropdown-item value=\"sector\">Sector</cv-dropdown-item>\n                  <cv-dropdown-item value=\"supplier\">Supplier</cv-dropdown-item>\n                </cv-dropdown>\n              </div>\n\n              <div class=\"control-group\">\n                <label class=\"control-label\">Time Range:</label>\n                <cv-dropdown\n                  v-model=\"rootCauseTimeRange\"\n                  @change=\"handleRootCauseTimeRangeChange\"\n                  class=\"control-dropdown\"\n                >\n                  <cv-dropdown-item value=\"3month\">3 Months</cv-dropdown-item>\n                  <cv-dropdown-item value=\"6month\">6 Months</cv-dropdown-item>\n                </cv-dropdown>\n              </div>\n\n              <div class=\"control-group\" v-if=\"breakoutGroups.length > 0\">\n                <label class=\"control-label\">Group:</label>\n                <cv-dropdown\n                  v-model=\"rootCauseSelectedGroup\"\n                  @change=\"handleRootCauseGroupChange\"\n                  class=\"control-dropdown\"\n                >\n                  <cv-dropdown-item value=\"all\">All Groups</cv-dropdown-item>\n                  <cv-dropdown-item\n                    v-for=\"group in breakoutGroups\"\n                    :key=\"group.name\"\n                    :value=\"group.name\"\n                  >\n                    {{ group.name }}\n                  </cv-dropdown-item>\n                </cv-dropdown>\n              </div>\n            </div>\n\n            <div class=\"chart-container\">\n              <div v-if=\"isRootCauseDataLoading\" >\n                        Loading Chart...\n                        <RootCauseChart :data = [] :loading=\"isRootCauseDataLoading\"/>\n                      </div>\n              <RootCauseChart\n               v-if=\"rootCauseChartData.length > 0\"\n                :data=\"rootCauseChartData\"\n                :viewBy=\"rootCauseViewBy\"\n                :timeRange=\"rootCauseTimeRange\"\n                :selectedGroup=\"rootCauseSelectedGroup\"\n                :loading=\"isRootCauseDataLoading\"\n                @bar-click=\"handleRootCauseBarClick\"\n              />\n\n              <div v-if=\"rootCauseChartData.length == 0 && !isRootCauseDataLoading\" >\n                        No data available\n                      </div>\n              <!-- <div v-else-if=\"isRootCauseDataLoading\" class=\"loading-container\">\n                <p>Loading root cause data...</p>\n              </div>\n              <div v-else class=\"no-data-message\">\n                No root cause data available for the selected criteria.\n              </div> -->\n            </div>\n\n            <!-- <div class=\"section-footer\">\n              <p class=\"section-description\">\n                Root cause analysis showing defect categories and their fail rates over time.\n                Click on bars to see detailed information.\n              </p>\n            </div> -->\n          </div>\n\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Action Tracker Modal (same as Action Tracker page) -->\n  <cv-modal\n    class=\"tracking-modal\"\n    :visible=\"trackingModalVisible\"\n    @modal-hidden=\"trackingModalVisible = false\"\n    :size=\"'xl'\"\n  >\n    <template slot=\"title\">\n      <div>Action Tracking - {{ selectedTrackingItem ? selectedTrackingItem.pn : '' }}</div>\n    </template>\n    <template slot=\"content\">\n      <div class=\"tracking-modal-content\" v-if=\"selectedTrackingItem\">\n        <!-- Tracking Tabs -->\n        <cv-tabs>\n          <cv-tab id=\"new-alerts-tab\" label=\"New Alerts\">\n            <div class=\"tracking-tab-content\">\n              <div class=\"new-alerts-section\">\n                <h3 class=\"section-title\">Alert Management</h3>\n                <p class=\"section-description\">Manage alerts and updates for {{ selectedTrackingItem.pn }}</p>\n\n                <!-- AI Insight Section -->\n                <div class=\"ai-insight-section\">\n                  <h4 class=\"subsection-title\">AI Insight</h4>\n                  <div v-if=\"isLoadingAiInsight\" class=\"loading-message\">\n                    Generating AI insight...\n                  </div>\n                  <div v-else class=\"ai-insight-content\">\n                    {{ aiInsight || 'No AI insight available for this alert.' }}\n                  </div>\n                </div>\n\n                <!-- Add New Alert Update -->\n                <div class=\"add-alert-section\">\n                  <h4 class=\"subsection-title\">Add Alert Update</h4>\n                  <div class=\"add-update-form\">\n                    <cv-text-area\n                      v-model=\"newAlertUpdate\"\n                      label=\"Update Content\"\n                      placeholder=\"Enter update details...\"\n                      rows=\"4\"\n                    ></cv-text-area>\n                    <div class=\"update-form-actions\">\n                      <cv-button\n                        kind=\"primary\"\n                        @click=\"addAlertUpdate\"\n                        :disabled=\"!newAlertUpdate.trim()\"\n                      >\n                        Add Update\n                      </cv-button>\n                    </div>\n                  </div>\n                </div>\n\n                <!-- Alert History Table -->\n                <div class=\"alert-updates-section\">\n                  <h4 class=\"subsection-title\">Alert History</h4>\n                  <div v-if=\"alertUpdates.length > 0\" class=\"alert-updates-table\">\n                    <div class=\"updates-header\">\n                      <div class=\"update-column\">Date</div>\n                      <div class=\"update-column\">Update</div>\n                      <div class=\"update-column\">Updated By</div>\n                    </div>\n                    <div\n                      v-for=\"(update, index) in alertUpdates\"\n                      :key=\"index\"\n                      class=\"update-row\"\n                    >\n                      <div class=\"update-cell\">{{ update.date }}</div>\n                      <div class=\"update-cell\">{{ update.update }}</div>\n                      <div class=\"update-cell\">{{ update.updatedBy }}</div>\n                    </div>\n                  </div>\n                  <div v-else class=\"no-updates-message\">\n                    No alert updates available.\n                  </div>\n                </div>\n              </div>\n            </div>\n          </cv-tab>\n\n          <cv-tab id=\"action-items-tab\" label=\"Action Items\">\n            <div class=\"tracking-tab-content\">\n              <div class=\"tracking-section\">\n                <h3 class=\"tracking-section-title\">Action Details</h3>\n                <div class=\"action-summary\">\n                  <div class=\"summary-item\">\n                    <span class=\"summary-label\">Part Group:</span>\n                    <span class=\"summary-value\">{{ selectedTrackingItem.group }}</span>\n                  </div>\n                  <div class=\"summary-item\">\n                    <span class=\"summary-label\">Part Number:</span>\n                    <span class=\"summary-value\">{{ selectedTrackingItem.pn }}</span>\n                  </div>\n                  <div class=\"summary-item\">\n                    <span class=\"summary-label\">Status:</span>\n                    <span class=\"summary-value\">{{ selectedTrackingItem.status }}</span>\n                  </div>\n                  <div class=\"summary-item\">\n                    <span class=\"summary-label\">Priority:</span>\n                    <span class=\"summary-value priority-badge\" :class=\"selectedTrackingItem.priority ? selectedTrackingItem.priority.toLowerCase() : 'medium'\">\n                      {{ selectedTrackingItem.priority || 'Medium' }}\n                    </span>\n                  </div>\n                  <div class=\"summary-item\">\n                    <span class=\"summary-label\">Progress:</span>\n                    <span class=\"summary-value\">{{ selectedTrackingItem.progress || 0 }}%</span>\n                  </div>\n                </div>\n              </div>\n\n              <div class=\"tracking-section\">\n                <h3 class=\"tracking-section-title\">Action Items & Progress</h3>\n                <div class=\"action-items-list\">\n                  <div\n                    v-for=\"(actionItem, index) in selectedTrackingItem.actionItems\"\n                    :key=\"index\"\n                    class=\"action-item-card\"\n                    :class=\"{ 'completed': actionItem.completed }\"\n                  >\n                    <!-- Action Item Header -->\n                    <div class=\"action-item-header\">\n                      <cv-checkbox\n                        v-model=\"actionItem.completed\"\n                        :label=\"actionItem.title\"\n                        @change=\"updateActionItemCompletion(actionItem, index)\"\n                      />\n                      <span class=\"completion-date\" v-if=\"actionItem.completed && actionItem.completedDate\">\n                        Completed on {{ formatDate(actionItem.completedDate) }}\n                      </span>\n                      <span class=\"last-updated\" v-if=\"actionItem.lastUpdated\">\n                        Last updated: {{ formatDate(actionItem.lastUpdated) }}\n                      </span>\n                    </div>\n\n                    <!-- Action Item Description -->\n                    <div class=\"action-item-description\" v-if=\"actionItem.description\">\n                      {{ actionItem.description }}\n                    </div>\n                  </div>\n\n                  <!-- No Action Items -->\n                  <div v-if=\"!selectedTrackingItem.actionItems || selectedTrackingItem.actionItems.length === 0\" class=\"no-action-items\">\n                    <p>No action items defined for this tracking item.</p>\n                    <cv-button\n                      kind=\"primary\"\n                      size=\"small\"\n                      @click=\"addDefaultActionItems\"\n                    >\n                      Create Default Action Items\n                    </cv-button>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </cv-tab>\n\n          <cv-tab id=\"performance-tab\" label=\"Performance Chart & History\">\n            <div class=\"tracking-tab-content\">\n              <!-- Performance Chart Section -->\n              <div class=\"chart-section\">\n                <h3 class=\"section-title\">Performance Chart</h3>\n                <PerformanceChart :trackingItem=\"selectedTrackingItem\" />\n              </div>\n\n              <!-- Performance History Table Section -->\n              <div class=\"performance-history-section\">\n                <h3 class=\"section-title\">Monthly Performance History</h3>\n                <p class=\"section-description\">Historical record of performance and status for {{ selectedTrackingItem.pn }}</p>\n\n                <cv-data-table\n                  :columns=\"alertHistoryColumns\"\n                  :title=\"''\"\n                  class=\"alert-history-table\"\n                >\n                  <template slot=\"data\">\n                    <cv-data-table-row\n                      v-for=\"(record, index) in alertHistoryData\"\n                      :key=\"index\"\n                      :class=\"getAlertRowClass(record)\"\n                    >\n                      <cv-data-table-cell>{{ record.month }}</cv-data-table-cell>\n                      <cv-data-table-cell>{{ record.year }}</cv-data-table-cell>\n                      <cv-data-table-cell>\n                        <div class=\"status-cell\">\n                          <span class=\"status-indicator\" :class=\"record.status.toLowerCase()\"></span>\n                          <span class=\"status-text\" :class=\"record.status.toLowerCase()\">{{ record.status }}</span>\n                        </div>\n                      </cv-data-table-cell>\n                      <cv-data-table-cell>{{ record.actualRate }}%</cv-data-table-cell>\n                      <cv-data-table-cell>{{ record.targetRate }}%</cv-data-table-cell>\n                      <cv-data-table-cell>{{ record.xFactor }}</cv-data-table-cell>\n                      <cv-data-table-cell>{{ record.volume }}</cv-data-table-cell>\n                      <cv-data-table-cell>{{ record.defects }}</cv-data-table-cell>\n                      <cv-data-table-cell>{{ record.notes || 'N/A' }}</cv-data-table-cell>\n                    </cv-data-table-row>\n                  </template>\n                </cv-data-table>\n\n                <div v-if=\"!alertHistoryData || alertHistoryData.length === 0\" class=\"no-alert-data\">\n                  <p>No performance history available for this part</p>\n                  <p class=\"note\">Performance history will be populated as data becomes available</p>\n                </div>\n              </div>\n            </div>\n          </cv-tab>\n        </cv-tabs>\n      </div>\n    </template>\n  </cv-modal>\n</template>\n\n<script>\nimport {\n  CvButton,\n  CvTag,\n  CvTextArea,\n  CvDropdown,\n  CvDropdownItem,\n  CvModal,\n  CvTabs,\n  CvTab,\n  CvCheckbox,\n  CvDataTable,\n  CvDataTableRow,\n  CvDataTableCell\n} from '@carbon/vue';\n\nimport RootCauseChart from '@/components/RootCauseChart/RootCauseChart';\nimport PerformanceChart from '@/components/PerformanceChart/PerformanceChart.vue';\n\nexport default {\n  name: 'PQEOwnerDashboard',\n  components: {\n    CvButton,\n    CvTag,\n    CvTextArea,\n    CvDropdown,\n    CvDropdownItem,\n    CvModal,\n    CvTabs,\n    CvTab,\n    CvCheckbox,\n    CvDataTable,\n    CvDataTableRow,\n    CvDataTableCell,\n    RootCauseChart,\n    PerformanceChart\n  },\n  props: {\n    pqeOwner: {\n      type: String,\n      required: true\n    }\n  },\n  data() {\n    return {\n      // Critical Issues Data\n      newCriticalIssues: [],\n      unresolvedCriticalIssues: [],\n      expandedIssueIds: [], // Track which issues are expanded\n      isCriticalIssuesExpanded: false, // Track if critical issues section is expanded\n\n      // Action Tracker Alerts Data\n      isActionTrackerAlertsExpanded: false,\n      isInProgressAlertsExpanded: false,\n      isOutstandingAlertsExpanded: false,\n      isResolvedAlertsExpanded: false,\n\n      // Action Tracker Alert Data\n      inProgressAlerts: [],\n      outstandingAlerts: [],\n      resolvedAlerts: [],\n\n      // Filtering for Critical Issues\n      selectedFilters: {\n        severity: [],\n        analysisType: []\n      },\n      severityFilter: 'all',\n      analysisTypeFilter: 'all',\n\n      // Validation Data\n      validatedCount: 0,\n      unvalidatedCount: 0,\n\n      // Breakout Groups Data\n      breakoutGroups: [],\n\n      // Root Cause Chart Data\n      rootCauseChartData: [],\n      isRootCauseDataLoading: true,\n\n      // Root Cause Chart Controls\n      rootCauseViewBy: 'rootCause',\n      rootCauseTimeRange: '6month',\n      rootCauseSelectedGroup: 'all',\n\n      // Loading States\n      isLoading: false,\n\n      // Action Tracker Modal Data\n      trackingModalVisible: false,\n      selectedTrackingItem: null,\n      alertUpdates: [],\n      newAlertUpdate: '',\n      aiInsight: '',\n      isLoadingAiInsight: false,\n      alertHistoryData: [],\n      alertHistoryColumns: ['Month', 'Year', 'Status', 'Actual Rate', 'Target Rate', 'X-Factor', 'Volume', 'Defects', 'Notes']\n    };\n  },\n  computed: {\n    // Filtered critical issues based on selected filters\n    filteredCriticalIssues() {\n      // If no filters are selected, return all issues\n      if (this.selectedFilters.severity.length === 0 && this.selectedFilters.analysisType.length === 0) {\n        return this.unresolvedCriticalIssues;\n      }\n\n      // Apply filters\n      return this.unresolvedCriticalIssues.filter(issue => {\n        // Check if issue passes severity filter\n        const passesSeverityFilter = this.selectedFilters.severity.length === 0 ||\n                                    this.selectedFilters.severity.includes(issue.severity);\n\n        // Check if issue passes analysis type filter\n        const passesAnalysisFilter = this.selectedFilters.analysisType.length === 0 ||\n                                    this.selectedFilters.analysisType.includes(issue.analysisType);\n\n        // Issue must pass both filters\n        return passesSeverityFilter && passesAnalysisFilter;\n      });\n    },\n\n    // Check if any filters are active\n    isFiltersActive() {\n      return this.selectedFilters.severity.length > 0 || this.selectedFilters.analysisType.length > 0;\n    },\n\n\n\n    // Calculate total alerts (critical breakout groups)\n    totalAlerts() {\n      return this.breakoutGroups.filter(group => group.xFactor >= 1.5).length;\n    },\n\n    // Calculate total in-progress issues from action tracker\n    inProgressIssuesCount() {\n      // This would normally fetch from action tracker API\n      // For now, return a calculated value based on action tracker alerts\n      return this.inProgressAlerts.length;\n    },\n\n    // Calculate total fails (validated + unvalidated)\n    totalFails() {\n      return this.validatedCount + this.unvalidatedCount;\n    },\n\n    // Calculate groups over target (xFactor > 1.0)\n    groupsOverTargetCount() {\n      return this.breakoutGroups.filter(group => group.xFactor > 1.0).length;\n    },\n\n    // Calculate total action tracker alerts\n    totalActionTrackerAlerts() {\n      return this.inProgressAlerts.length + this.outstandingAlerts.length + this.resolvedAlerts.length;\n    }\n  },\n  watch: {\n    pqeOwner: {\n      immediate: true,\n      handler(newValue, oldValue) {\n        console.log(`PQE Owner changed from ${oldValue} to ${newValue}`);\n        if (newValue) {\n          // Reset the group selection when PQE owner changes\n          this.rootCauseSelectedGroup = 'all';\n          this.loadDashboardData();\n        }\n      }\n    }\n  },\n  methods: {\n    loadDashboardData() {\n      this.loadCriticalIssues();\n      this.loadValidationCounts();\n      this.loadActionTrackerAlerts();\n      this.loadRootCauseData();\n    },\n\n\n\n\n\n    async loadCriticalIssues() {\n      console.log(`Loading critical issues for PQE owner: ${this.pqeOwner}`);\n      this.isLoading = true;\n\n      try {\n        // Get authentication config\n        const config = this.getAuthConfig();\n\n        // First, load the breakout groups for this PQE owner\n        await this.loadBreakoutGroups();\n\n        // Fetch critical issues from the API\n        const response = await fetch('/api-statit2/get_pqe_critical_issues', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({\n            pqeOwner: this.pqeOwner\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch critical issues: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success') {\n          // Filter critical issues to only include those related to this PQE's breakout groups\n          const allIssues = data.critical_issues || [];\n\n          // If we have breakout groups, filter issues to only include those for this PQE's groups\n          if (this.breakoutGroups.length > 0) {\n            this.unresolvedCriticalIssues = allIssues.filter(issue => {\n              // Check if the issue's category matches any of the PQE's breakout groups\n              return this.breakoutGroups.some(group =>\n                issue.category.includes(group.name) || group.name.includes(issue.category)\n              );\n            });\n          } else {\n            // If we don't have breakout groups yet, use all issues\n            this.unresolvedCriticalIssues = allIssues;\n          }\n\n          console.log(`Loaded ${this.unresolvedCriticalIssues.length} critical issues for ${this.pqeOwner}'s groups`);\n        } else {\n          console.error('Failed to load critical issues:', data.message);\n          // Use sample data for development\n          this.loadSampleCriticalIssues();\n        }\n      } catch (error) {\n        console.error('Error loading critical issues:', error);\n        // Use sample data for development\n        this.loadSampleCriticalIssues();\n      } finally {\n        this.isLoading = false;\n      }\n    },\n\n    async loadBreakoutGroups() {\n      console.log(`Loading breakout groups for PQE owner: ${this.pqeOwner}`);\n\n      try {\n        // Get authentication config\n        const config = this.getAuthConfig();\n\n        // Fetch breakout groups from the API\n        const response = await fetch('/api-statit2/get_pqe_breakout_groups', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({\n            pqeOwner: this.pqeOwner\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch breakout groups: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success') {\n          this.breakoutGroups = data.breakout_groups || [];\n          console.log(`Loaded ${this.breakoutGroups.length} breakout groups for ${this.pqeOwner}`);\n\n          // Reload root cause chart data now that we have the correct breakout groups\n          await this.loadRootCauseData();\n        } else {\n          console.error('Failed to load breakout groups:', data.message);\n          // Use sample data for development\n          this.loadSampleBreakoutGroups();\n          // Reload root cause chart data with sample groups\n          await this.loadRootCauseData();\n        }\n      } catch (error) {\n        console.error('Error loading breakout groups:', error);\n        // Use sample data for development\n        this.loadSampleBreakoutGroups();\n        // Reload root cause chart data with sample groups\n        await this.loadRootCauseData();\n      }\n    },\n\n    async loadSampleBreakoutGroups() {\n      // Sample data for development\n      if (this.pqeOwner === 'Albert G.') {\n        this.breakoutGroups = [\n          { name: 'Fan Themis', status: 'Short-Term Spike', xFactor: 3.2 },\n          { name: 'Victoria Crypto', status: 'Sustained Problem', xFactor: 1.8 },\n          { name: 'Quantum Nexus', status: 'Sustained Problem', xFactor: 1.6 }\n        ];\n      } else if (this.pqeOwner === 'Sarah L.') {\n        this.breakoutGroups = [\n          { name: 'Stellar Core', status: 'Short-Term Spike', xFactor: 2.5 },\n          { name: 'Nebula Drive', status: 'Sustained Problem', xFactor: 1.7 }\n        ];\n      } else {\n        // Default sample data\n        this.breakoutGroups = [\n          { name: 'Sample Group 1', status: 'Normal', xFactor: 0.9 },\n          { name: 'Sample Group 2', status: 'Normal', xFactor: 0.8 }\n        ];\n      }\n\n      // Reload root cause chart data with the sample groups\n      await this.loadRootCauseData();\n    },\n\n    loadSampleCriticalIssues() {\n      // Sample data for development\n      this.unresolvedCriticalIssues = [\n        {\n          id: 'ci1',\n          category: 'Fan Themis',\n          month: '2024-06',\n          severity: 'high',\n          increaseMultiplier: '3.2',\n          aiDescription: 'Fan Themis showing 3.2x spike in June 2024 with 12 fails vs 4 expected. Root cause appears to be airflow issues in the new design.',\n          comment: '',\n          analysisType: 'Root Cause'\n        },\n        {\n          id: 'ci2',\n          category: 'Victoria Crypto',\n          month: '2024-06',\n          severity: 'medium',\n          increaseMultiplier: '1.8',\n          aiDescription: 'Victoria Crypto has sustained problem with 1.8x target rate for 3 consecutive months. Consistent pattern of power delivery issues.',\n          comment: '',\n          analysisType: 'Root Cause'\n        },\n\n      ];\n\n      // Set new critical issues to empty array for now\n      this.newCriticalIssues = [];\n    },\n\n    async loadValidationCounts() {\n      console.log(`Loading validation counts for PQE owner: ${this.pqeOwner}`);\n\n      try {\n        // Get authentication config\n        const config = this.getAuthConfig();\n\n        // Fetch validation counts from the API\n        const response = await fetch('/api-statit2/get_validation_counts', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({\n            pqeOwner: this.pqeOwner\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch validation counts: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success') {\n          this.validatedCount = data.validated_count || 0;\n          this.unvalidatedCount = data.unvalidated_count || 0;\n          console.log(`Loaded validation counts: ${this.validatedCount} validated, ${this.unvalidatedCount} unvalidated`);\n        } else {\n          console.error('Failed to load validation counts:', data.message);\n          // Use sample data for development\n          this.validatedCount = 125;\n          this.unvalidatedCount = 37;\n        }\n      } catch (error) {\n        console.error('Error loading validation counts:', error);\n        // Use sample data for development\n        this.validatedCount = 125;\n        this.unvalidatedCount = 37;\n      }\n    },\n\n    // Load Action Tracker Alerts data\n    loadActionTrackerAlerts() {\n      console.log(`Loading Action Tracker Alerts for PQE owner: ${this.pqeOwner}`);\n\n      // Generate sample data for demonstration\n      // In a real implementation, this would fetch from the action tracker API\n      this.inProgressAlerts = [\n        {\n          id: 'ip-1',\n          category: 'Thermal Management',\n          severity: 'High',\n          xFactor: '2.1',\n          status: 'In-Progress'\n        },\n        {\n          id: 'ip-2',\n          category: 'Power Delivery',\n          severity: 'Medium',\n          xFactor: '1.8',\n          status: 'In-Progress'\n        }\n      ];\n\n      this.outstandingAlerts = [\n        {\n          id: 'out-1',\n          category: 'Signal Integrity',\n          severity: 'Medium',\n          xFactor: '1.6',\n          status: 'Outstanding'\n        }\n      ];\n\n      this.resolvedAlerts = [\n        {\n          id: 'res-1',\n          category: 'Manufacturing Defect',\n          severity: 'High',\n          xFactor: '0.8',\n          status: 'Resolved'\n        },\n        {\n          id: 'res-2',\n          category: 'Component Quality',\n          severity: 'Medium',\n          xFactor: '0.9',\n          status: 'Resolved'\n        }\n      ];\n\n      console.log(`Loaded ${this.totalActionTrackerAlerts} Action Tracker Alerts`);\n    },\n\n    toggleCriticalIssuesExpanded() {\n      this.isCriticalIssuesExpanded = !this.isCriticalIssuesExpanded;\n    },\n\n    toggleResolvedIssuesExpanded() {\n      this.isResolvedIssuesExpanded = !this.isResolvedIssuesExpanded;\n    },\n\n    toggleOutstandingIssuesExpanded() {\n      this.isOutstandingIssuesExpanded = !this.isOutstandingIssuesExpanded;\n    },\n\n    toggleIssueExpanded(issue) {\n      const issueId = issue.id;\n      const index = this.expandedIssueIds.indexOf(issueId);\n\n      if (index === -1) {\n        // Issue is not expanded, so expand it\n        this.expandedIssueIds.push(issueId);\n      } else {\n        // Issue is expanded, so collapse it\n        this.expandedIssueIds.splice(index, 1);\n      }\n    },\n\n    toggleResolvedIssueExpanded(issue) {\n      const issueId = issue.id;\n      const index = this.expandedResolvedIssueIds.indexOf(issueId);\n\n      if (index === -1) {\n        // Issue is not expanded, so expand it\n        this.expandedResolvedIssueIds.push(issueId);\n      } else {\n        // Issue is expanded, so collapse it\n        this.expandedResolvedIssueIds.splice(index, 1);\n      }\n    },\n\n    toggleOutstandingIssueExpanded(issue) {\n      const issueId = issue.id;\n      const index = this.expandedOutstandingIssueIds.indexOf(issueId);\n\n      if (index === -1) {\n        // Issue is not expanded, so expand it\n        this.expandedOutstandingIssueIds.push(issueId);\n      } else {\n        // Issue is expanded, so collapse it\n        this.expandedOutstandingIssueIds.splice(index, 1);\n      }\n    },\n\n    isIssueExpanded(issueId) {\n      return this.expandedIssueIds.includes(issueId);\n    },\n\n    isResolvedIssueExpanded(issueId) {\n      return this.expandedResolvedIssueIds.includes(issueId);\n    },\n\n    isOutstandingIssueExpanded(issueId) {\n      return this.expandedOutstandingIssueIds.includes(issueId);\n    },\n\n    markIssueAsResolved(issue) {\n      console.log('Mark issue as resolved:', issue);\n\n      // In a real implementation, this would call an API to update the issue status\n      // For now, we'll just move the issue from unresolved to resolved\n\n      // Create a resolved issue object with additional fields\n      const resolvedIssue = {\n        ...issue,\n        resolutionDate: new Date().toISOString().split('T')[0], // Today's date in YYYY-MM-DD format\n        currentPerformance: '1.0x' // Initial performance after resolution\n      };\n\n      // Add to resolved issues\n      this.resolvedIssues.push(resolvedIssue);\n\n      // Remove from unresolved issues\n      const index = this.unresolvedCriticalIssues.findIndex(i => i.id === issue.id);\n      if (index !== -1) {\n        this.unresolvedCriticalIssues.splice(index, 1);\n      }\n\n      // Show success message\n      alert(`Issue marked as resolved: ${issue.category}`);\n    },\n\n    markIssueAsOutstanding(issue) {\n      console.log('Mark issue as outstanding:', issue);\n\n      // In a real implementation, this would call an API to update the issue status\n      // For now, we'll just move the issue from unresolved to outstanding\n\n      // Create an outstanding issue object with additional fields\n      const outstandingIssue = {\n        ...issue,\n        acceptanceDate: new Date().toISOString().split('T')[0], // Today's date in YYYY-MM-DD format\n        currentPerformance: issue.increaseMultiplier, // Initial performance is the same as the issue multiplier\n        acceptedBy: 'Engineering Team' // Default value\n      };\n\n      // Add to outstanding issues\n      this.outstandingIssues.push(outstandingIssue);\n\n      // Remove from unresolved issues\n      const index = this.unresolvedCriticalIssues.findIndex(i => i.id === issue.id);\n      if (index !== -1) {\n        this.unresolvedCriticalIssues.splice(index, 1);\n      }\n\n      // Show success message\n      alert(`Issue marked as outstanding: ${issue.category}`);\n    },\n\n    handleSeverityFilterChange() {\n      // Update the selected filters based on the severity dropdown\n      if (this.severityFilter === 'all') {\n        this.selectedFilters.severity = [];\n      } else {\n        this.selectedFilters.severity = [this.severityFilter];\n      }\n    },\n\n    handleAnalysisFilterChange() {\n      // Update the selected filters based on the analysis type dropdown\n      if (this.analysisTypeFilter === 'all') {\n        this.selectedFilters.analysisType = [];\n      } else {\n        this.selectedFilters.analysisType = [this.analysisTypeFilter];\n      }\n    },\n\n    clearFilters() {\n      this.selectedFilters.severity = [];\n      this.selectedFilters.analysisType = [];\n      this.severityFilter = 'all';\n      this.analysisTypeFilter = 'all';\n    },\n\n    handleResolvedCategoryFilterChange() {\n      // Update the selected filters based on the category dropdown\n      if (this.resolvedCategoryFilter === 'all') {\n        this.resolvedFilters.category = [];\n      } else {\n        this.resolvedFilters.category = [this.resolvedCategoryFilter];\n      }\n    },\n\n    handleResolvedAnalysisFilterChange() {\n      // Update the selected filters based on the analysis type dropdown\n      if (this.resolvedAnalysisTypeFilter === 'all') {\n        this.resolvedFilters.analysisType = [];\n      } else {\n        this.resolvedFilters.analysisType = [this.resolvedAnalysisTypeFilter];\n      }\n    },\n\n    clearResolvedFilters() {\n      this.resolvedFilters.category = [];\n      this.resolvedFilters.analysisType = [];\n      this.resolvedCategoryFilter = 'all';\n      this.resolvedAnalysisTypeFilter = 'all';\n    },\n\n    handleOutstandingCategoryFilterChange() {\n      // Update the selected filters based on the category dropdown\n      if (this.outstandingCategoryFilter === 'all') {\n        this.outstandingFilters.category = [];\n      } else {\n        this.outstandingFilters.category = [this.outstandingCategoryFilter];\n      }\n    },\n\n    handleOutstandingAnalysisFilterChange() {\n      // Update the selected filters based on the analysis type dropdown\n      if (this.outstandingAnalysisTypeFilter === 'all') {\n        this.outstandingFilters.analysisType = [];\n      } else {\n        this.outstandingFilters.analysisType = [this.outstandingAnalysisTypeFilter];\n      }\n    },\n\n    clearOutstandingFilters() {\n      this.outstandingFilters.category = [];\n      this.outstandingFilters.analysisType = [];\n      this.outstandingCategoryFilter = 'all';\n      this.outstandingAnalysisTypeFilter = 'all';\n    },\n\n    updateIssue(issue, markAsResolved = false, markAsOutstanding = false) {\n      console.log('Update issue:', issue, 'Mark as resolved:', markAsResolved, 'Mark as outstanding:', markAsOutstanding);\n\n      // Emit event to update action tracker\n      this.$emit('update-action-tracker', {\n        issueId: issue.id,\n        category: issue.category,\n        comment: issue.comment,\n        severity: issue.severity,\n        pqeOwner: this.pqeOwner,\n        month: issue.month,\n        analysisType: issue.analysisType,\n        resolved: markAsResolved,\n        outstanding: markAsOutstanding\n      });\n\n      // If marking as resolved, move the issue to resolved issues\n      if (markAsResolved) {\n        this.markIssueAsResolved(issue);\n      }\n      // If marking as outstanding, move the issue to outstanding issues\n      else if (markAsOutstanding) {\n        this.markIssueAsOutstanding(issue);\n      }\n      else {\n        // Show success message for regular update\n        alert(`Action tracker updated for issue: ${issue.category}`);\n      }\n    },\n\n    viewPerformanceData(issue) {\n      console.log('View performance data for:', issue.category);\n\n      // In a real implementation, this would show a modal or chart with performance data\n      // For now, we'll just show an alert with the data\n\n      const performanceData = this.performanceData[issue.category];\n      if (performanceData && performanceData.length > 0) {\n        const performanceText = performanceData\n          .map(data => `${data.month}: ${data.xFactor}x`)\n          .join('\\n');\n\n        alert(`Performance data for ${issue.category}:\\n${performanceText}`);\n      } else {\n        alert(`No performance data available for ${issue.category}`);\n      }\n    },\n\n    viewIssueDetails(issue) {\n      console.log('View issue details for:', issue.category);\n      // In a real implementation, this would show a modal or navigate to a detailed view\n      alert(`Viewing details for issue: ${issue.category}\\nMonth: ${issue.month}\\nSeverity: ${issue.severity}\\nMultiplier: ${issue.increaseMultiplier}x`);\n    },\n\n    // Root Cause Chart methods\n    async loadRootCauseData() {\n      console.log('Loading root cause chart data for PQE Owner Dashboard');\n      this.isRootCauseDataLoading = true;\n\n      try {\n        // Get authentication config\n        const config = this.getAuthConfig();\n\n        // Calculate date range based on selected time range\n        const endDate = new Date();\n        const startDate = new Date();\n        const monthsToFetch = this.rootCauseTimeRange === '3month' ? 2 : 5; // 3 or 6 months including current\n        startDate.setMonth(endDate.getMonth() - monthsToFetch);\n\n        const startDateStr = `${startDate.getFullYear()}-${String(startDate.getMonth() + 1).padStart(2, '0')}`;\n        const endDateStr = `${endDate.getFullYear()}-${String(endDate.getMonth() + 1).padStart(2, '0')}`;\n\n        console.log(`Fetching root cause data for ${this.pqeOwner} from ${startDateStr} to ${endDateStr}, Group: ${this.rootCauseSelectedGroup}`);\n\n        // Call the new API endpoint\n        const response = await fetch('/api-statit2/get_pqe_root_cause_analysis', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({\n            pqeOwner: this.pqeOwner,\n            startDate: startDateStr,\n            endDate: endDateStr,\n            selectedGroup: this.rootCauseSelectedGroup === 'all' ? null : this.rootCauseSelectedGroup\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch root cause data: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success') {\n          // Transform the API data to chart format\n          this.rootCauseChartData = this.transformRootCauseData(data.categoryData);\n          console.log('Root cause chart data loaded:', data.categoryData);\n          console.log('Chart data length:', this.rootCauseChartData.length);\n          console.log('Sample data point:', this.rootCauseChartData[0]);\n          console.log('Breakout groups:', data.breakoutGroups);\n          console.log('Part numbers:', data.partNumbers);\n\n          console.log('Chart data loaded, watcher should handle update');\n        } else {\n          console.error('Failed to load root cause data:', data.sql_error_msg || data.message);\n          // Fall back to mock data\n          this.rootCauseChartData = this.generateMockRootCauseData();\n          console.log('Using mock data:', this.rootCauseChartData.length);\n        }\n      } catch (error) {\n        console.error('Error loading root cause data:', error);\n        // Fall back to mock data\n        this.rootCauseChartData = this.generateMockRootCauseData();\n      } finally {\n        this.isRootCauseDataLoading = false;\n      }\n    },\n\n    transformRootCauseData(categoryData) {\n      // Transform the API response into chart format\n      const chartData = [];\n\n      // categoryData structure: { \"category\": { \"2024-03\": { defects: 5, volume: 1000, failRate: 0.5 }, ... }, ... }\n      for (const [category, monthData] of Object.entries(categoryData)) {\n        // Clean up category name (trim whitespace)\n        const cleanCategory = category.trim();\n        console.log(\"trim cat\", cleanCategory)\n\n        for (const [month, data] of Object.entries(monthData)) {\n          chartData.push({\n            group: cleanCategory,\n            key: month,\n            value: parseFloat(data.failRate.toFixed(2))\n          });\n        }\n      }\n\n      console.log('Transformed root cause data:', chartData);\n      console.log('Unique groups in data:', [...new Set(chartData.map(d => d.group))]);\n      console.log('Unique keys in data:', [...new Set(chartData.map(d => d.key))]);\n      console.log('Value range:', Math.min(...chartData.map(d => d.value)), 'to', Math.max(...chartData.map(d => d.value)));\n      return chartData;\n    },\n\n    generateMockRootCauseData() {\n      // Use the actual breakout groups for this PQE owner instead of generic categories\n      let categories = [];\n\n      if (this.breakoutGroups && this.breakoutGroups.length > 0) {\n        // Use the actual breakout groups for this PQE owner\n        categories = this.breakoutGroups.map(group => group.name);\n\n        // If a specific group is selected, filter to only that group\n        if (this.rootCauseSelectedGroup !== 'all') {\n          categories = categories.filter(category => category === this.rootCauseSelectedGroup);\n        }\n      } else {\n        // Fallback to sample data if breakout groups aren't loaded yet\n        if (this.pqeOwner === 'Albert G.') {\n          categories = ['Fan Themis', 'Victoria Crypto', 'Quantum Nexus'];\n        } else if (this.pqeOwner === 'Sarah L.') {\n          categories = ['Stellar Core', 'Nebula Drive'];\n        } else {\n          categories = ['Sample Group 1', 'Sample Group 2'];\n        }\n\n        // If a specific group is selected, filter to only that group\n        if (this.rootCauseSelectedGroup !== 'all') {\n          categories = categories.filter(category => category === this.rootCauseSelectedGroup);\n        }\n      }\n\n      // Generate months based on selected time range\n      const now = new Date();\n      const months = [];\n      const monthsToGenerate = this.rootCauseTimeRange === '3month' ? 3 : 6;\n\n      for (let i = monthsToGenerate - 1; i >= 0; i--) {\n        const date = new Date(now.getFullYear(), now.getMonth() - i, 1);\n        const year = date.getFullYear();\n        const month = String(date.getMonth() + 1).padStart(2, '0');\n        months.push(`${year}-${month}`);\n      }\n\n      const data = [];\n\n      months.forEach(month => {\n        categories.forEach(category => {\n          // Generate realistic fail rate data (0-5% range)\n          const baseRate = Math.random() * 3 + 0.5; // 0.5% to 3.5%\n          const variation = (Math.random() - 0.5) * 1; // ±0.5%\n          const failRate = Math.max(0.1, baseRate + variation);\n\n          data.push({\n            group: category,\n            key: month,\n            value: parseFloat(failRate.toFixed(2))\n          });\n        });\n      });\n\n      return data;\n    },\n\n    handleRootCauseBarClick(event) {\n      if (event && event.data) {\n        const clickedData = event.data;\n        console.log(`Root cause bar clicked for category: ${clickedData.group}, month: ${clickedData.key}`);\n\n        // You can implement additional functionality here, such as showing more details\n        // or navigating to the MetisXFactors Group tab with this specific category\n        alert(`Clicked on ${clickedData.group} for ${clickedData.key}\\nFail Rate: ${clickedData.value}%`);\n      }\n    },\n\n    handleRootCauseTimeRangeChange() {\n      console.log(`Root cause time range changed to: ${this.rootCauseTimeRange}`);\n      this.loadRootCauseData();\n    },\n\n    handleRootCauseViewByChange() {\n      console.log(`Root cause time range changed to: ${this.rootCauseTimeRange}`);\n      this.loadRootCauseData();\n    },\n\n    handleRootCauseGroupChange() {\n      console.log(`Root cause group changed to: ${this.rootCauseSelectedGroup}`);\n      console.log('Current chart data length before reload:', this.rootCauseChartData.length);\n      this.loadRootCauseData();\n    },\n\n    getAuthConfig() {\n      // Get authentication token from localStorage or Vuex store\n      const token = localStorage.getItem('token') || this.$store.state.token;\n\n      return {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      };\n    },\n\n    // Navigate to Action Tracker\n    goToActionTracker() {\n      console.log('Navigating to Action Tracker...');\n      this.$router.push('/action-tracker');\n    },\n\n    // Navigate to Validation Page\n    goToValidationPage() {\n      console.log('Navigating to Validation Page...');\n      this.$router.push('/defect-validations');\n    },\n\n    // Navigate to Heatmap tab in Metis XFactors\n    goToHeatmap() {\n      console.log('Navigating to Metis XFactors Heatmap...');\n      this.$router.push('/metis-xfactors?tab=heatmap');\n    },\n\n    // Scroll to alerts section (action tracker alerts section)\n    scrollToAlerts() {\n      console.log('Scrolling to action tracker alerts section...');\n      // Scroll to the Action Tracker Alerts section\n      this.$nextTick(() => {\n        const actionTrackerSection = document.querySelector('.section-card:first-child'); // First section card (Action Tracker Alerts)\n        if (actionTrackerSection) {\n          actionTrackerSection.scrollIntoView({\n            behavior: 'smooth',\n            block: 'start'\n          });\n\n          // Add a brief highlight effect\n          actionTrackerSection.style.transition = 'box-shadow 0.3s ease';\n          actionTrackerSection.style.boxShadow = '0 0 20px rgba(0, 98, 255, 0.5)';\n\n          setTimeout(() => {\n            actionTrackerSection.style.boxShadow = '0 2px 6px rgba(0, 0, 0, 0.2)';\n          }, 2000);\n        }\n      });\n    },\n\n    // Toggle Action Tracker Alerts section\n    toggleActionTrackerAlertsExpanded() {\n      this.isActionTrackerAlertsExpanded = !this.isActionTrackerAlertsExpanded;\n      console.log('Action Tracker Alerts expanded:', this.isActionTrackerAlertsExpanded);\n    },\n\n    // Toggle In-Progress Alerts subsection\n    toggleInProgressAlertsExpanded() {\n      this.isInProgressAlertsExpanded = !this.isInProgressAlertsExpanded;\n      console.log('In-Progress Alerts expanded:', this.isInProgressAlertsExpanded);\n    },\n\n    // Toggle Outstanding Alerts subsection\n    toggleOutstandingAlertsExpanded() {\n      this.isOutstandingAlertsExpanded = !this.isOutstandingAlertsExpanded;\n      console.log('Outstanding Alerts expanded:', this.isOutstandingAlertsExpanded);\n    },\n\n    // Toggle Resolved Alerts subsection\n    toggleResolvedAlertsExpanded() {\n      this.isResolvedAlertsExpanded = !this.isResolvedAlertsExpanded;\n      console.log('Resolved Alerts expanded:', this.isResolvedAlertsExpanded);\n    },\n\n    // View Action Tracker Alert - opens tracking modal\n    async viewActionTrackerAlert(alert) {\n      console.log('Opening tracking modal for alert:', alert);\n\n      // Create a mock action tracker item from the alert data\n      this.selectedTrackingItem = {\n        id: alert.actionTrackerId || alert.id,\n        pn: alert.category || 'Unknown',\n        group: alert.category || 'Unknown',\n        status: alert.status || 'Unknown',\n        priority: alert.severity || 'Medium',\n        progress: 0,\n        commodity: 'Unknown',\n        assignee: 'Unknown',\n        actionItems: []\n      };\n\n      // Load alert data\n      await this.loadAlertUpdates(this.selectedTrackingItem);\n      await this.loadAiInsight(this.selectedTrackingItem, alert);\n      await this.loadAlertHistoryData(this.selectedTrackingItem);\n\n      // Open the modal\n      this.trackingModalVisible = true;\n    },\n\n    // Add critical issue to action tracker\n    async addCriticalIssueToActionTracker(issue) {\n      console.log('Adding critical issue to action tracker:', issue);\n\n      try {\n        const response = await fetch('/api-statit2/update_pqe_action', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...this.getAuthConfig().headers\n          },\n          body: JSON.stringify({\n            issueId: issue.id,\n            category: issue.category,\n            comment: issue.comment || `Critical issue: ${issue.aiDescription}`,\n            severity: issue.severity,\n            pqeOwner: this.pqeOwner,\n            month: issue.month,\n            analysisType: issue.analysisType\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to add to action tracker: ${response.status}`);\n        }\n\n        const data = await response.json();\n        if (data.status_res === 'success') {\n          alert(`Critical issue \"${issue.category}\" has been added to the action tracker with ID: ${data.actionId}`);\n        } else {\n          throw new Error(data.message || 'Failed to add to action tracker');\n        }\n      } catch (error) {\n        console.error('Error adding critical issue to action tracker:', error);\n        alert('Failed to add critical issue to action tracker: ' + error.message);\n      }\n    },\n\n    // Load alert updates for the selected item\n    async loadAlertUpdates(item) {\n      try {\n        const response = await fetch('/api-statit2/get_pqe_alert_history', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...this.getAuthConfig().headers\n          },\n          body: JSON.stringify({\n            actionTrackerId: item.id,\n            pqeOwner: this.pqeOwner\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch alert history: ${response.status}`);\n        }\n\n        const data = await response.json();\n        if (data.status_res === 'success') {\n          this.alertUpdates = data.alert_history || [];\n        } else {\n          console.error('Failed to load alert updates:', data.message);\n          this.alertUpdates = [];\n        }\n      } catch (error) {\n        console.error('Error loading alert updates:', error);\n        this.alertUpdates = [];\n      }\n    },\n\n    // Load AI insight for the selected item\n    async loadAiInsight(item, alertData = null) {\n      this.isLoadingAiInsight = true;\n      try {\n        const alertInfo = alertData || {\n          category: item.group || 'Unknown',\n          severity: item.priority || 'Medium',\n          xFactor: '1.5',\n          status: item.status || 'Unknown'\n        };\n\n        const response = await fetch('/api-statit2/get_pqe_alert_ai_insight', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...this.getAuthConfig().headers\n          },\n          body: JSON.stringify({\n            actionTrackerId: item.id,\n            alertData: alertInfo,\n            pqeOwner: this.pqeOwner\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch AI insight: ${response.status}`);\n        }\n\n        const data = await response.json();\n        if (data.status_res === 'success') {\n          this.aiInsight = data.ai_insight || 'No insight available';\n        } else {\n          console.error('Failed to load AI insight:', data.message);\n          this.aiInsight = 'Unable to generate insight at this time';\n        }\n      } catch (error) {\n        console.error('Error loading AI insight:', error);\n        this.aiInsight = 'Unable to generate insight at this time';\n      } finally {\n        this.isLoadingAiInsight = false;\n      }\n    },\n\n    // Add alert update\n    async addAlertUpdate() {\n      if (!this.newAlertUpdate.trim()) {\n        alert('Please enter an update');\n        return;\n      }\n\n      try {\n        const response = await fetch('/api-statit2/add_pqe_alert_update', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...this.getAuthConfig().headers\n          },\n          body: JSON.stringify({\n            actionTrackerId: this.selectedTrackingItem.id,\n            update: this.newAlertUpdate,\n            updatedBy: this.pqeOwner,\n            alertType: 'PQE Update'\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to add alert update: ${response.status}`);\n        }\n\n        const data = await response.json();\n        if (data.status_res === 'success') {\n          // Add the new alert to the history\n          this.alertUpdates.push(data.alert);\n          this.newAlertUpdate = '';\n          console.log('Alert update added successfully');\n        } else {\n          throw new Error(data.message || 'Failed to add alert update');\n        }\n      } catch (error) {\n        console.error('Error adding alert update:', error);\n        alert('Failed to add alert update: ' + error.message);\n      }\n    },\n\n    // Load alert history data for performance table\n    async loadAlertHistoryData(item) {\n      try {\n        // Mock data for now - this would come from your API\n        this.alertHistoryData = [\n          {\n            month: 'Jan',\n            year: '2024',\n            status: 'Normal',\n            actualRate: '0.5',\n            targetRate: '1.0',\n            xFactor: '0.5',\n            volume: '1000',\n            defects: '5',\n            notes: 'Within target'\n          },\n          {\n            month: 'Feb',\n            year: '2024',\n            status: 'Alert',\n            actualRate: '2.1',\n            targetRate: '1.0',\n            xFactor: '2.1',\n            volume: '1200',\n            defects: '25',\n            notes: 'Above target - investigating'\n          }\n        ];\n      } catch (error) {\n        console.error('Error loading alert history data:', error);\n        this.alertHistoryData = [];\n      }\n    },\n\n    // Get alert row class for styling\n    getAlertRowClass(record) {\n      if (record.status === 'Alert') return 'alert-row';\n      if (record.status === 'Normal') return 'normal-row';\n      return '';\n    },\n\n    // Format date helper\n    formatDate(dateString) {\n      if (!dateString) return '';\n      return new Date(dateString).toLocaleDateString();\n    },\n\n    // Update action item completion\n    updateActionItemCompletion(actionItem, index) {\n      if (actionItem.completed) {\n        actionItem.completedDate = new Date().toISOString();\n      } else {\n        actionItem.completedDate = null;\n      }\n      actionItem.lastUpdated = new Date().toISOString();\n    },\n\n    // Add default action items\n    addDefaultActionItems() {\n      if (!this.selectedTrackingItem.actionItems) {\n        this.selectedTrackingItem.actionItems = [];\n      }\n\n      const defaultItems = [\n        {\n          title: 'Investigate root cause',\n          description: 'Analyze data to identify potential root causes',\n          completed: false,\n          lastUpdated: new Date().toISOString()\n        },\n        {\n          title: 'Implement corrective action',\n          description: 'Execute plan to address identified issues',\n          completed: false,\n          lastUpdated: new Date().toISOString()\n        },\n        {\n          title: 'Monitor results',\n          description: 'Track performance to verify effectiveness',\n          completed: false,\n          lastUpdated: new Date().toISOString()\n        }\n      ];\n\n      this.selectedTrackingItem.actionItems.push(...defaultItems);\n    }\n  }\n};\n</script>\n\n<style scoped>\n.pqe-owner-dashboard-container {\n  color: #f4f4f4;\n}\n\n.content-wrapper {\n  padding: 1rem;\n}\n\n.dashboard-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1.5rem;\n}\n\n.dashboard-header h3 {\n  margin: 0;\n  font-size: 1.5rem;\n  font-weight: 400;\n}\n\n.last-updated-text {\n  font-size: 0.875rem;\n  color: #8d8d8d;\n}\n\n.key-metrics-section {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1rem;\n  margin-bottom: 1.5rem;\n}\n\n.metric-card {\n  background-color: #262626;\n  border-radius: 8px;\n  padding: 1.25rem;\n  display: flex;\n  align-items: center;\n  border: 1px solid #333333;\n  transition: transform 0.2s ease, box-shadow 0.2s ease;\n}\n\n.metric-card.clickable {\n  cursor: pointer;\n\n  &:hover {\n    transform: translateY(-3px);\n    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);\n    background-color: #353535; /* Darker gray on hover */\n  }\n\n  &:active {\n    transform: translateY(-1px);\n    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);\n  }\n}\n\n.metric-icon {\n  margin-right: 1rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 48px;\n  height: 48px;\n  border-radius: 50%;\n}\n\n.metric-icon.alerts {\n  background-color: rgba(250, 77, 86, 0.1);\n  color: #fa4d56;\n}\n\n.metric-icon.in-progress {\n  background-color: rgba(255, 131, 43, 0.1);\n  color: #ff832b;\n}\n\n.metric-icon.validated {\n  background-color: rgba(36, 161, 72, 0.1);\n  color: #24a148;\n}\n\n.metric-icon.new-issues {\n  background-color: rgba(15, 98, 254, 0.1);\n  color: #0f62fe;\n}\n\n.metric-icon.critical-issues {\n  background-color: rgba(250, 77, 86, 0.1);\n  color: #fa4d56;\n}\n\n.metric-icon.unvalidated {\n  background-color: rgba(255, 131, 43, 0.1);\n  color: #ff832b;\n}\n\n.metric-icon.groups-over-target {\n  background-color: rgba(138, 63, 252, 0.1);\n  color: #8a3ffc;\n}\n\n/* Action Tracker Alerts Styles */\n.alert-subsection {\n  margin-bottom: 1.5rem;\n  border: 1px solid #393939;\n  border-radius: 8px;\n  background-color: #2a2a2a;\n}\n\n.subsection-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 1rem 1.25rem;\n  cursor: pointer;\n  border-bottom: 1px solid #393939;\n  transition: background-color 0.2s ease;\n}\n\n.subsection-header:hover {\n  background-color: #333333;\n}\n\n.subsection-title {\n  color: #f4f4f4;\n  font-size: 1rem;\n  font-weight: 500;\n  margin: 0;\n}\n\n.subsection-controls {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n}\n\n.subsection-content {\n  padding: 1rem 1.25rem;\n}\n\n.action-tracker-alert {\n  background-color: #333333;\n  border: 1px solid #525252;\n  border-radius: 6px;\n  margin-bottom: 0.75rem;\n  transition: all 0.2s ease;\n}\n\n.action-tracker-alert:hover {\n  background-color: #393939;\n  border-color: #6f6f6f;\n}\n\n.action-tracker-alert .issue-header {\n  padding: 1rem;\n}\n\n.action-tracker-alert .issue-tags {\n  display: flex;\n  gap: 0.5rem;\n  margin-bottom: 0.5rem;\n}\n\n.action-tracker-alert .issue-title {\n  color: #f4f4f4;\n  font-size: 1rem;\n  font-weight: 500;\n  margin-bottom: 0.5rem;\n  display: block;\n}\n\n.action-tracker-alert .issue-metadata {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n}\n\n.action-tracker-alert .issue-multiplier {\n  font-size: 0.875rem;\n  font-weight: 600;\n  padding: 0.25rem 0.5rem;\n  border-radius: 4px;\n}\n\n.action-tracker-alert .issue-multiplier.medium-severity {\n  background-color: rgba(255, 131, 43, 0.2);\n  color: #ff832b;\n}\n\n.action-tracker-alert .issue-multiplier.good-performance {\n  background-color: rgba(36, 161, 72, 0.2);\n  color: #24a148;\n}\n\n.no-data-message {\n  text-align: center;\n  color: #8d8d8d;\n  font-style: italic;\n  padding: 2rem;\n}\n\n.metric-content {\n  display: flex;\n  flex-direction: column;\n}\n\n.metric-label {\n  font-size: 0.875rem;\n  color: #8d8d8d;\n  margin-bottom: 0.25rem;\n}\n\n.metric-value {\n  font-size: 1.5rem;\n  font-weight: 600;\n  margin-bottom: 0.25rem;\n}\n\n.metric-description {\n  font-size: 0.75rem;\n  color: #8d8d8d;\n}\n\n.chart-section {\n  margin-bottom: 1.5rem;\n}\n\n.chart-controls {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 1rem;\n  padding: 1rem;\n  background-color: #262626;\n  border-bottom: 1px solid #333333;\n}\n\n.control-group {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.control-label {\n  color: #8d8d8d;\n  font-size: 0.875rem;\n  white-space: nowrap;\n}\n\n.control-dropdown {\n  width: 200px;\n}\n\n.chart-container {\n  height: 500px;\n  padding: 1rem;\n  background-color: #161616;\n  border-radius: 8px;\n  margin: 1rem;\n}\n\n.section-footer {\n  padding: 0 1.25rem 1.25rem;\n}\n\n.section-description {\n  margin: 0;\n  font-size: 0.875rem;\n  color: #8d8d8d;\n  line-height: 1.4;\n}\n\n.dashboard-main-content {\n  display: grid;\n  grid-template-columns: 1fr;\n  gap: 1.5rem;\n}\n\n.dashboard-column {\n  display: flex;\n  flex-direction: column;\n  gap: 1.5rem;\n}\n\n.section-card {\n  background-color: #262626;\n  border-radius: 8px;\n  overflow: hidden;\n  border: 1px solid #333333;\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 1.25rem;\n  cursor: pointer;\n  transition: background-color 0.2s ease;\n}\n\n.section-header:hover {\n  background-color: #333333;\n}\n\n.section-title-container {\n  display: flex;\n  flex-direction: column;\n}\n\n.section-title {\n  margin: 0;\n  font-size: 1.25rem;\n  font-weight: 400;\n}\n\n.section-subtitle {\n  font-size: 0.875rem;\n  color: #8d8d8d;\n  margin-top: 0.25rem;\n}\n\n.section-controls {\n  display: flex;\n  align-items: center;\n}\n\n.status-indicator {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 24px;\n  height: 24px;\n  border-radius: 50%;\n  background-color: #fa4d56;\n  color: #ffffff;\n  font-size: 0.75rem;\n  font-weight: 600;\n  margin-right: 0.75rem;\n}\n\n.status-indicator.flashing {\n  animation: flash 2s infinite;\n}\n\n.status-indicator.resolved-indicator {\n  background-color: #24a148;\n}\n\n.status-indicator.outstanding-indicator {\n  background-color: #0f62fe;\n}\n\n@keyframes flash {\n  0%, 100% { opacity: 1; }\n  50% { opacity: 0.5; }\n}\n\n.expand-indicator {\n  transition: transform 0.2s ease;\n}\n\n.expand-indicator.expanded {\n  transform: rotate(180deg);\n}\n\n.section-content {\n  padding: 0 1.25rem 1.25rem;\n}\n\n.filter-container {\n  margin-bottom: 1.5rem;\n  background-color: #333333;\n  padding: 1rem;\n  border-radius: 8px;\n}\n\n.filter-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1rem;\n}\n\n.filter-title {\n  margin: 0;\n  font-size: 1rem;\n  font-weight: 400;\n}\n\n.filter-controls {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 1rem;\n}\n\n.filter-group {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.filter-label {\n  color: #8d8d8d;\n  font-size: 0.875rem;\n  white-space: nowrap;\n}\n\n.filter-dropdown {\n  width: 200px;\n}\n\n.issues-list {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n\n.issue-card {\n  background-color: #333333;\n  border-radius: 8px;\n  overflow: hidden;\n  transition: transform 0.2s ease, box-shadow 0.2s ease;\n  cursor: pointer;\n}\n\n.issue-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);\n}\n\n.issue-header {\n  display: flex;\n  align-items: center;\n  padding: 1rem;\n  position: relative;\n}\n\n.issue-tags {\n  display: flex;\n  gap: 0.5rem;\n  margin-right: 1rem;\n}\n\n.issue-title {\n  flex-grow: 1;\n  font-weight: 500;\n}\n\n.issue-metadata {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  margin-right: 1rem;\n}\n\n.issue-multiplier {\n  font-weight: 600;\n  padding: 0.125rem 0.375rem;\n  border-radius: 4px;\n}\n\n.issue-multiplier.high-severity {\n  background-color: rgba(250, 77, 86, 0.1);\n  color: #fa4d56;\n}\n\n.issue-multiplier.medium-severity {\n  background-color: rgba(255, 131, 43, 0.1);\n  color: #ff832b;\n}\n\n.issue-multiplier.good-performance {\n  background-color: rgba(36, 161, 72, 0.1);\n  color: #24a148;\n}\n\n.issue-multiplier.medium-performance {\n  background-color: rgba(255, 131, 43, 0.1);\n  color: #ff832b;\n}\n\n.resolved-issue-card {\n  border-left: 4px solid #24a148;\n}\n\n.outstanding-issue-card {\n  border-left: 4px solid #0f62fe;\n}\n\n.resolution-details, .acceptance-details {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 1rem;\n  font-size: 0.875rem;\n  color: #c6c6c6;\n}\n\n.issue-multiplier.low-performance {\n  background-color: rgba(36, 161, 72, 0.1);\n  color: #24a148;\n}\n\n.resolution-comment, .acceptance-comment {\n  margin: 1rem 0;\n  padding: 0.75rem;\n  background-color: #333333;\n  border-radius: 4px;\n}\n\n.comment-label {\n  font-weight: 600;\n  margin-bottom: 0.5rem;\n  color: #c6c6c6;\n}\n\n.comment-text {\n  font-size: 0.875rem;\n  white-space: pre-wrap;\n}\n\n.issue-content {\n  padding: 0 1rem 1rem;\n  border-top: 1px solid #444444;\n}\n\n.ai-description {\n  margin-bottom: 1rem;\n  padding: 1rem;\n  background-color: #262626;\n  border-radius: 8px;\n  font-size: 0.875rem;\n  line-height: 1.5;\n}\n\n.action-comment {\n  margin-bottom: 1rem;\n}\n\n.issue-actions {\n  display: flex;\n  justify-content: flex-end;\n  gap: 0.5rem;\n}\n\n.no-data-message {\n  color: #8d8d8d;\n  font-size: 1rem;\n  text-align: center;\n  padding: 2rem;\n}\n\n@media (max-width: 768px) {\n  .key-metrics-section {\n    grid-template-columns: 1fr;\n  }\n\n  .dashboard-main-content {\n    grid-template-columns: 1fr;\n  }\n\n  .filter-controls {\n    flex-direction: column;\n  }\n\n  .filter-group {\n    width: 100%;\n  }\n}\n\n/* Issue actions styling */\n.issue-actions {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 0.5rem;\n  margin-top: 1rem;\n  padding-top: 1rem;\n  border-top: 1px solid #444444;\n}\n\n.issue-actions .cv-button {\n  flex: 0 0 auto;\n}\n\n@media (max-width: 768px) {\n  .issue-actions {\n    flex-direction: column;\n  }\n\n  .issue-actions .cv-button {\n    width: 100%;\n  }\n}\n\n/* Action Tracker Modal Styles */\n.tracking-modal .cv-modal-container {\n  max-width: 95vw;\n  width: 1200px;\n}\n\n.tracking-modal-content {\n  padding: 1rem;\n  color: #f4f4f4;\n}\n\n.tracking-tab-content {\n  padding: 1.5rem;\n  background-color: #262626;\n  border-radius: 8px;\n  margin-top: 1rem;\n}\n\n.section-title {\n  color: #f4f4f4;\n  font-size: 1.25rem;\n  font-weight: 600;\n  margin: 0 0 1rem 0;\n  border-bottom: 1px solid #444444;\n  padding-bottom: 0.5rem;\n}\n\n.section-description {\n  color: #c6c6c6;\n  font-size: 0.875rem;\n  margin-bottom: 2rem;\n}\n\n.subsection-title {\n  color: #f4f4f4;\n  font-size: 1rem;\n  font-weight: 500;\n  margin: 0 0 1rem 0;\n  border-bottom: 1px solid #444444;\n  padding-bottom: 0.5rem;\n}\n\n/* New Alert Functionality Styles */\n.new-alerts-section {\n  padding: 0;\n}\n\n.ai-insight-section {\n  background-color: #333333;\n  border-radius: 8px;\n  padding: 1.5rem;\n  margin-bottom: 2rem;\n  border: 1px solid #444444;\n}\n\n.ai-insight-content {\n  background-color: #262626;\n  border-radius: 6px;\n  padding: 1rem;\n  border: 1px solid #525252;\n  font-size: 0.875rem;\n  line-height: 1.5;\n  color: #f4f4f4;\n}\n\n.loading-message {\n  text-align: center;\n  color: #8d8d8d;\n  font-style: italic;\n  padding: 1rem;\n}\n\n.add-alert-section {\n  background-color: #333333;\n  border-radius: 8px;\n  padding: 1.5rem;\n  margin-bottom: 2rem;\n  border: 1px solid #444444;\n}\n\n.add-update-form {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n\n.update-form-actions {\n  display: flex;\n  justify-content: flex-end;\n}\n\n.alert-updates-section {\n  background-color: #333333;\n  border-radius: 8px;\n  padding: 1.5rem;\n  border: 1px solid #444444;\n}\n\n.alert-updates-table {\n  background-color: #262626;\n  border-radius: 6px;\n  overflow: hidden;\n  border: 1px solid #525252;\n}\n\n.updates-header {\n  display: grid;\n  grid-template-columns: 120px 1fr 150px;\n  background-color: #393939;\n  border-bottom: 1px solid #525252;\n}\n\n.update-column {\n  padding: 0.75rem;\n  font-weight: 600;\n  color: #f4f4f4;\n  border-right: 1px solid #525252;\n}\n\n.update-column:last-child {\n  border-right: none;\n}\n\n.update-row {\n  display: grid;\n  grid-template-columns: 120px 1fr 150px;\n  border-bottom: 1px solid #525252;\n}\n\n.update-row:last-child {\n  border-bottom: none;\n}\n\n.update-cell {\n  padding: 0.75rem;\n  color: #f4f4f4;\n  border-right: 1px solid #525252;\n  font-size: 0.875rem;\n}\n\n.update-cell:last-child {\n  border-right: none;\n}\n\n.no-updates-message {\n  text-align: center;\n  color: #8d8d8d;\n  font-style: italic;\n  padding: 2rem;\n}\n\n/* Action Items Tab Styles */\n.tracking-section {\n  background-color: #333333;\n  border-radius: 8px;\n  padding: 1.5rem;\n  margin-bottom: 2rem;\n  border: 1px solid #444444;\n}\n\n.tracking-section-title {\n  color: #f4f4f4;\n  font-size: 1.125rem;\n  font-weight: 600;\n  margin: 0 0 1rem 0;\n  border-bottom: 1px solid #444444;\n  padding-bottom: 0.5rem;\n}\n\n.action-summary {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1rem;\n}\n\n.summary-item {\n  display: flex;\n  flex-direction: column;\n  gap: 0.25rem;\n}\n\n.summary-label {\n  font-weight: 600;\n  color: #c6c6c6;\n  font-size: 0.875rem;\n}\n\n.summary-value {\n  color: #f4f4f4;\n  font-size: 1rem;\n}\n\n.priority-badge {\n  padding: 0.25rem 0.5rem;\n  border-radius: 4px;\n  font-size: 0.75rem;\n  font-weight: 600;\n  text-transform: uppercase;\n  width: fit-content;\n}\n\n.priority-badge.high {\n  background-color: #da1e28;\n  color: #ffffff;\n}\n\n.priority-badge.medium {\n  background-color: #f1c21b;\n  color: #000000;\n}\n\n.priority-badge.low {\n  background-color: #24a148;\n  color: #ffffff;\n}\n\n.action-items-list {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n\n.action-item-card {\n  background-color: #262626;\n  border-radius: 6px;\n  padding: 1rem;\n  border: 1px solid #525252;\n}\n\n.action-item-card.completed {\n  opacity: 0.7;\n  background-color: #1e3a1e;\n}\n\n.action-item-header {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n}\n\n.completion-date,\n.last-updated {\n  font-size: 0.75rem;\n  color: #8d8d8d;\n}\n\n.action-item-description {\n  margin-top: 0.5rem;\n  color: #c6c6c6;\n  font-size: 0.875rem;\n}\n\n.no-action-items {\n  text-align: center;\n  padding: 2rem;\n  color: #8d8d8d;\n}\n\n/* Performance Chart & History Tab Styles */\n.chart-section {\n  background-color: #333333;\n  border-radius: 8px;\n  padding: 1.5rem;\n  margin-bottom: 2rem;\n  border: 1px solid #444444;\n}\n\n.performance-history-section {\n  background-color: #333333;\n  border-radius: 8px;\n  padding: 1.5rem;\n  border: 1px solid #444444;\n}\n\n.alert-history-table {\n  margin-top: 1rem;\n}\n\n.status-cell {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.status-indicator {\n  width: 8px;\n  height: 8px;\n  border-radius: 50%;\n}\n\n.status-indicator.normal {\n  background-color: #24a148;\n}\n\n.status-indicator.alert {\n  background-color: #da1e28;\n}\n\n.status-text.normal {\n  color: #24a148;\n}\n\n.status-text.alert {\n  color: #da1e28;\n}\n\n.alert-row {\n  background-color: rgba(218, 30, 40, 0.1);\n}\n\n.normal-row {\n  background-color: rgba(36, 161, 72, 0.1);\n}\n\n.no-alert-data {\n  text-align: center;\n  color: #8d8d8d;\n  padding: 2rem;\n}\n\n.no-alert-data .note {\n  font-size: 0.875rem;\n  font-style: italic;\n}\n\n@media (max-width: 768px) {\n  .tracking-modal .cv-modal-container {\n    max-width: 95vw;\n    width: 95vw;\n  }\n\n  .updates-header,\n  .update-row {\n    grid-template-columns: 1fr;\n  }\n\n  .update-column,\n  .update-cell {\n    border-right: none;\n    border-bottom: 1px solid #525252;\n  }\n\n  .action-summary {\n    grid-template-columns: 1fr;\n  }\n}\n</style>\n"]}]}