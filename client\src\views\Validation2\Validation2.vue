<template>
  <div class="validation2-container">
    <MainHeader :expandedSideNav="expandedSideNav" :useFixed="useFixed" />

    <!-- Classification Modal -->
    <cv-modal
      v-if="showClassificationModal"
      :visible="showClassificationModal"
      @modal-hidden="closeClassificationModal"
      size="default"
    >
      <template slot="title">Edit Classification</template>
      <template slot="content">
        <div class="modal-content-inner">
          <p><strong>Defect ID:</strong> {{ selectedDefect ? selectedDefect.DEFECT_ID : '' }}</p>
          <p class="description-text"><strong>Description:</strong> {{ selectedDefect ? selectedDefect.DESCRIPTION : '' }}</p>

          <div class="modal-form">
            <div class="form-group">
              <cv-dropdown
                v-model="editClassification.primary"
                label="Primary Classification"
                :items="primaryClassificationOptions"
              />
            </div>

            <div class="form-group">
              <cv-dropdown
                v-model="editClassification.subcategory"
                label="Subcategory"
                :items="subcategoryOptions"
              />
            </div>
          </div>
        </div>
      </template>
      <template slot="secondary-button">Cancel</template>
      <template slot="primary-button">Save</template>
      <template slot="actions">
        <cv-button kind="secondary" @click="closeClassificationModal">Cancel</cv-button>
        <cv-button kind="primary" @click="saveClassification">Save</cv-button>
      </template>
    </cv-modal>

    <!-- Batch Results Modal -->
    <cv-modal
      v-if="showBatchResultsModal"
      :visible="showBatchResultsModal"
      @modal-hidden="closeBatchResultsModal"
      size="large"
    >
      <template slot="title">Batch Processing Results</template>
      <template slot="content">
        <div class="batch-results-content">
          <p>Successfully processed {{ batchResults.length }} defects</p>

          <cv-data-table
            :columns="batchResultsColumns"
            :data="batchResults"
            :pagination="{ pageSize: 10 }"
            title=""
          >
            <template slot="data">
              <cv-data-table-row
                v-for="(result, index) in batchResults"
                :key="index"
              >
                <cv-data-table-cell>{{ result.defectId }}</cv-data-table-cell>
                <cv-data-table-cell>{{ result.description }}</cv-data-table-cell>
                <cv-data-table-cell>
                  <cv-tag :kind="getClassificationTagKind(result.classification.primary)">
                    {{ result.classification.primary }}
                  </cv-tag>
                </cv-data-table-cell>
                <cv-data-table-cell>
                  <cv-tag :kind="getClassificationTagKind(result.classification.subcategory)">
                    {{ result.classification.subcategory }}
                  </cv-tag>
                </cv-data-table-cell>
              </cv-data-table-row>
            </template>
          </cv-data-table>
        </div>
      </template>
      <template slot="actions">
        <cv-button kind="primary" @click="closeBatchResultsModal">Close</cv-button>
      </template>
    </cv-modal>

    <main id="main-content" class="main-content">
      <!-- Page Header -->
      <div class="page-header">
        <h1>Defect Validation 2.0</h1>
        <p>Analyze and validate defects with AI assistance</p>
      </div>

      <!-- Summary Tiles -->
      <div class="summary-section">
        <cv-tile class="summary-tile">
          <h4>Total Validations</h4>
          <p class="tile-value">{{ validationsSummary.total }}</p>
        </cv-tile>
        <cv-tile class="summary-tile">
          <h4>Validated</h4>
          <p class="tile-value validated">{{ validationsSummary.validated }}</p>
          <p class="tile-percentage">{{ validationsSummary.validatedPercentage }}%</p>
        </cv-tile>
        <cv-tile class="summary-tile">
          <h4>Unvalidated</h4>
          <p class="tile-value unvalidated">{{ validationsSummary.unvalidated }}</p>
          <p class="tile-percentage">{{ validationsSummary.unvalidatedPercentage }}%</p>
        </cv-tile>
      </div>

      <!-- Controls Row -->
      <div class="controls-row">
        <div class="filter-group">
          <cv-dropdown
            v-model="selectedPQEOwner"
            label="PQE Owner"
            :items="pqeOwnerOptions"
            @change="handlePQEOwnerChange"
          />
        </div>

        <div class="filter-group">
          <cv-dropdown
            v-model="selectedTimeFilter"
            label="Time Period"
            :items="timeFilterOptions"
            @change="handleTimeFilterChange"
          />
        </div>

        <div class="button-group">
          <cv-button
            kind="primary"
            @click="processNewDefects"
            :disabled="processingDefects"
          >
            {{ processingDefects ? 'Processing...' : 'Process All New Defects' }}
          </cv-button>

          <div class="batch-process-controls">
            <cv-number-input
              v-model="batchSize"
              label="Batch Size"
              :min="1"
              :max="20"
              :disabled="processingDefects"
            />
            <cv-button
              kind="secondary"
              @click="processLimitedBatch"
              :disabled="processingDefects"
            >
              Process Batch & View Results
            </cv-button>
          </div>
        </div>

        <div v-if="processingMessage" class="processing-message">
          <cv-inline-notification
            kind="info"
            :title="'Processing Status'"
            :sub-title="processingMessage"
          />
        </div>
      </div>

      <div class="pareto-section">
        <h2>Defect Classification Breakdown</h2>
        <div v-if="loadingCounts" class="loading-indicator">
          <cv-loading />
        </div>
        <div v-else-if="classificationCounts" class="chart-container">
          <div class="pareto-chart" data-carbon-theme="g90">
            <HBarChart
              :data="chartData"
              :loading="loadingCounts"
              title="Defect Classification Breakdown"
              height="400px"
            />
          </div>
        </div>
      </div>

      <div class="defects-table-section">
        <h2>Defect Details</h2>
        <div v-if="loadingData" class="loading-indicator">
          <cv-loading />
        </div>
        <div v-else class="table-container">
          <cv-data-table
            :columns="tableColumns"
            :data="tableData"
            :pagination="{ pageSize: 10 }"
            :sortable="true"
          >
            <template slot="data">
              <cv-data-table-row
                v-for="(row, rowIndex) in tableData"
                :key="rowIndex"
                :value="`${row.DEFECT_ID}`"
              >
                <cv-data-table-cell>{{ row.DEFECT_ID }}</cv-data-table-cell>
                <cv-data-table-cell>{{ formatDate(row.MFSDATE) }}</cv-data-table-cell>
                <cv-data-table-cell>{{ row.STAGE }}</cv-data-table-cell>
                <cv-data-table-cell>{{ row.DESCRIPTION }}</cv-data-table-cell>
                <cv-data-table-cell>
                  <div class="classification-cell">
                    <div v-if="getPrimaryClassification(row)" class="classification-tag" :class="getPrimaryClassification(row).toLowerCase()">
                      {{ getPrimaryClassification(row) }}
                    </div>
                    <div v-else class="classification-tag unclassified">
                      Unclassified
                    </div>
                  </div>
                </cv-data-table-cell>
                <cv-data-table-cell>
                  <div class="classification-cell">
                    <div v-if="getSubcategoryClassification(row)" class="classification-tag subcategory" :class="getSubcategoryClass(row)">
                      {{ getSubcategoryClassification(row) }}
                    </div>
                    <div v-else class="classification-tag unclassified">
                      Unclassified
                    </div>
                    <button
                      v-if="row.DEFECT_ID"
                      class="edit-button"
                      @click="openClassificationModal(row)"
                    >
                      Edit
                    </button>
                  </div>
                </cv-data-table-cell>
              </cv-data-table-row>
            </template>
          </cv-data-table>
        </div>
      </div>
    </main>
  </div>
</template>

<script>
import MainHeader from "@/components/MainHeader";
import HBarChart from "@/components/HBarChart/HBarChart";
import {
  CvModal,
  CvButton,
  CvDropdown,
  CvTile,
  CvDataTable,
  CvDataTableRow,
  CvDataTableCell,
  CvTag,
  CvNumberInput,
  CvInlineNotification,
  CvLoading
} from '@carbon/vue';

export default {
  name: "ValidationAiPage",
  components: {
    MainHeader,
    HBarChart,
    CvModal,
    CvButton,
    CvDropdown,
    CvTile,
    CvDataTable,
    CvDataTableRow,
    CvDataTableCell,
    CvTag,
    CvNumberInput,
    CvInlineNotification,
    CvLoading
  },
  data() {
    return {
      expandedSideNav: true,
      useFixed: false,

      // PQE Owner data
      selectedPQEOwner: 'All',
      pqeOwners: [],
      pqeOwnerOptions: [{ label: 'All PQE Owners', value: 'All' }],

      // Time filter data
      selectedTimeFilter: "month",
      timeFilterOptions: [
        { label: "Past Month", value: "month" },
        { label: "Past Week", value: "week" },
        { label: "Past Day", value: "day" }
      ],

      // Classification options for dropdowns
      primaryClassificationOptions: [
        { label: "Mechanical", value: "Mechanical" },
        { label: "Functional", value: "Functional" },
        { label: "Need More Info", value: "Need More Info" }
      ],
      subcategoryOptions: [
        { label: "Scratches", value: "Scratches" },
        { label: "Bent", value: "Bent" },
        { label: "Plugging", value: "Plugging" },
        { label: "Discolor", value: "Discolor" },
        { label: "Misalignment", value: "Misalignment" },
        { label: "Need More Info", value: "Need More Info" },
        { label: "Other", value: "Other" }
      ],

      // Loading states
      loadingData: false,
      loadingCounts: false,
      processingDefects: false,
      processingMessage: '',

      // Data arrays
      validationData: [],
      filteredData: [],
      primaryFilter: 'all',
      subcategoryFilter: 'all',
      classificationCounts: null,

      // Modal states
      showClassificationModal: false,
      showBatchResultsModal: false,
      selectedDefect: null,
      editClassification: {
        primary: 'Mechanical',
        subcategory: 'Other'
      },

      // Batch processing
      batchSize: 5,
      batchResults: [],

      // Table configurations
      tableColumns: [
        { label: "Defect ID", key: "defect_id" },
        { label: "Date", key: "date" },
        { label: "Stage", key: "stage" },
        { label: "Description", key: "description" },
        { label: "Primary", key: "primary" },
        { label: "Subcategory", key: "subcategory" }
      ],
      batchResultsColumns: [
        { label: "Defect ID", key: "defectId" },
        { label: "Description", key: "description" },
        { label: "Primary", key: "primary" },
        { label: "Subcategory", key: "subcategory" }
      ],

      // Summary data
      validationsSummary: {
        total: 0,
        validated: 0,
        unvalidated: 0,
        validatedPercentage: 0,
        unvalidatedPercentage: 0
      }
    };
  },
  computed: {
    tableData() {
      return this.filteredData || [];
    },
    chartData() {
      if (!this.classificationCounts) return [];

      // Format the data for the horizontal bar chart
      return [
        { group: "Classification", key: "Mechanical", value: this.classificationCounts.Mechanical || 0 },
        { group: "Classification", key: "Functional", value: this.classificationCounts.Functional || 0 },
        { group: "Classification", key: "Unclassified", value: this.classificationCounts.Unclassified || 0 }
      ];
    }
  },
  mounted() {
    this.loadPQEOwners();
    this.fetchValidationData();
    this.fetchClassificationCounts();
  },
  methods: {
    // Load PQE owners from API
    async loadPQEOwners() {
      try {
        const config = this.getAuthConfig();
        const response = await fetch('/api-statit2/get_pqe_owners', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...config.headers
          },
          body: JSON.stringify({})
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch PQE owners: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        if (data.status_res === 'success') {
          this.pqeOwners = data.pqe_owners || [];

          // Update dropdown options
          this.pqeOwnerOptions = [
            { label: 'All PQE Owners', value: 'All' },
            ...this.pqeOwners.map(owner => ({ label: owner, value: owner }))
          ];

          console.log(`Loaded ${this.pqeOwners.length} PQE owners`);
        } else {
          console.error('Failed to load PQE owners:', data.message);
        }
      } catch (error) {
        console.error('Error loading PQE owners:', error);
      }
    },

    // Get authentication config
    getAuthConfig() {
      return {
        headers: {
          'Authorization': 'Bearer ' + (localStorage.getItem('token') || ''),
          'X-User-ID': localStorage.getItem('userId') || ''
        }
      };
    },

    // Handle PQE owner change
    async handlePQEOwnerChange() {
      console.log(`Selected PQE owner: ${this.selectedPQEOwner}`);
      await this.fetchValidationData();
      await this.fetchClassificationCounts();
      this.updateValidationsSummary();
    },

    // Handle time filter change
    async handleTimeFilterChange() {
      console.log(`Selected time filter: ${this.selectedTimeFilter}`);
      await this.fetchValidationData();
      await this.fetchClassificationCounts();
      this.updateValidationsSummary();
    },

    async fetchValidationData() {
      this.loadingData = true;
      try {
        const response = await fetch(`${process.env.VUE_APP_API_PATH || '/api-statit2/'}get_validation_data`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify({
            timeFilter: this.selectedTimeFilter,
            pqeOwner: this.selectedPQEOwner !== 'All' ? this.selectedPQEOwner : null
          })
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        this.validationData = data.data;

        // Apply filters to the data
        this.applyFilters();
        this.updateValidationsSummary();
      } catch (error) {
        console.error("Error fetching validation data:", error);
      } finally {
        this.loadingData = false;
      }
    },

    // Update validations summary
    updateValidationsSummary() {
      if (!this.validationData || this.validationData.length === 0) {
        this.validationsSummary = {
          total: 0,
          validated: 0,
          unvalidated: 0,
          validatedPercentage: 0,
          unvalidatedPercentage: 0
        };
        return;
      }

      const total = this.validationData.length;
      const validated = this.validationData.filter(item =>
        item.classification &&
        (typeof item.classification === 'string' || item.classification.primary)
      ).length;
      const unvalidated = total - validated;

      this.validationsSummary = {
        total,
        validated,
        unvalidated,
        validatedPercentage: total > 0 ? Math.round((validated / total) * 100) : 0,
        unvalidatedPercentage: total > 0 ? Math.round((unvalidated / total) * 100) : 0
      };
    },

    async applyFilters() {
      this.loadingData = true;
      // Set default values for filters since dropdowns were removed
      this.primaryFilter = 'all';
      this.subcategoryFilter = 'all';

      try {
        // Use the server-side filtering endpoint
        const response = await fetch(`${process.env.VUE_APP_API_PATH || '/api-statit2/'}query_by_classification`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify({
            timeFilter: this.selectedTimeFilter,
            primaryFilter: this.primaryFilter,
            subcategoryFilter: this.subcategoryFilter
          })
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        this.filteredData = data.data;

        // Update the validation data as well to keep it in sync
        this.validationData = data.data;
      } catch (error) {
        console.error("Error applying filters:", error);

        // Fallback to client-side filtering if server-side fails
        if (!this.validationData || this.validationData.length === 0) {
          this.filteredData = [];
          return;
        }

        // Filter the data based on primary and subcategory filters
        this.filteredData = this.validationData.filter(item => {
          // Handle missing classification
          if (!item.classification) {
            return (this.primaryFilter === 'all' || this.primaryFilter === 'Need More Info') &&
                   (this.subcategoryFilter === 'all' || this.subcategoryFilter === 'Need More Info');
          }

          // Handle string classification (backward compatibility)
          if (typeof item.classification === 'string') {
            const primary = item.classification;
            return (this.primaryFilter === 'all' || this.primaryFilter === primary) &&
                   (this.subcategoryFilter === 'all' || this.subcategoryFilter === 'Other');
          }

          // Handle object classification
          const primary = item.classification.primary || 'Need More Info';
          const subcategory = item.classification.subcategory || 'Need More Info';

          return (this.primaryFilter === 'all' || this.primaryFilter === primary) &&
                 (this.subcategoryFilter === 'all' || this.subcategoryFilter === subcategory);
        });
      } finally {
        this.loadingData = false;
      }
    },

    async fetchClassificationCounts() {
      this.loadingCounts = true;
      try {
        const response = await fetch(`${process.env.VUE_APP_API_PATH || '/api-statit2/'}get_classification_counts`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify({
            timeFilter: this.selectedTimeFilter
          })
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        this.classificationCounts = data.data;
      } catch (error) {
        console.error("Error fetching classification counts:", error);
      } finally {
        this.loadingCounts = false;
      }
    },

    async processNewDefects() {
      this.processingDefects = true;
      this.processingMessage = 'Starting AI classification...';
      try {
        const response = await fetch(`${process.env.VUE_APP_API_PATH || '/api-statit2/'}process_new_defects`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify({
            api_key: 'A-9UZe48N156o-lLuxvjR5IePXDhKwrVHu61UciQ9afl',
            project_id: '8a74c0cb-b3c8-4fd7-9823-8301276169de',
            timeFilter: this.selectedTimeFilter // Pass the current time filter
          })
        });

        // Check for HTTP errors
        if (!response.ok) {
          const errorText = await response.text();
          let errorMessage;
          try {
            const errorJson = JSON.parse(errorText);
            errorMessage = errorJson.message || `HTTP error! status: ${response.status}`;
          } catch (e) {
            errorMessage = `HTTP error! status: ${response.status}: ${errorText}`;
          }
          throw new Error(errorMessage);
        }

        const data = await response.json();
        console.log("Processing result:", data);

        // Check for API errors
        if (data.status === 'error') {
          throw new Error(data.message || 'Unknown error occurred');
        }

        // Update processing message
        if (data.message) {
          this.processingMessage = data.message;
        }

        // Refresh data after processing
        setTimeout(() => {
          this.fetchValidationData();
          this.fetchClassificationCounts();
          this.processingDefects = false;

          // Keep the message visible for a bit longer
          setTimeout(() => {
            this.processingMessage = '';
          }, 5000);
        }, 2000);
      } catch (error) {
        console.error("Error processing defects:", error);
        this.processingDefects = false;
        this.processingMessage = `Error: ${error.message}`;

        // Clear error message after a delay
        setTimeout(() => {
          this.processingMessage = '';
        }, 10000); // Show error for longer (10 seconds)
      }
    },

    async updateClassification(defectId, classification) {
      try {
        const response = await fetch(`${process.env.VUE_APP_API_PATH || '/api-statit2/'}update_classification`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify({
            defectId,
            classification
          })
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        // Update local data
        const defect = this.validationData.find(d => d.DEFECT_ID === defectId);
        if (defect) {
          defect.classification = classification;
        }

        // Refresh classification counts
        this.fetchClassificationCounts();
      } catch (error) {
        console.error("Error updating classification:", error);
      }
    },


    resetFilters() {
      // Filters are already set to 'all' in applyFilters method
      this.applyFilters();
    },

    formatDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return dateString;
      return date.toLocaleDateString();
    },

    // Helper methods for classification
    getPrimaryClassification(row) {
      if (!row.classification) return null;

      // Handle string classification (backward compatibility)
      if (typeof row.classification === 'string') {
        return row.classification;
      }

      // Handle object classification
      return row.classification.primary || 'Need More Info';
    },

    getSubcategoryClassification(row) {
      if (!row.classification) return null;

      // Handle string classification (backward compatibility)
      if (typeof row.classification === 'string') {
        return 'Other';
      }

      // Handle object classification
      return row.classification.subcategory || 'Need More Info';
    },

    getSubcategoryClass(row) {
      const subcategory = this.getSubcategoryClassification(row);
      if (!subcategory) return '';

      // Convert to lowercase and remove spaces for CSS class
      return subcategory.toLowerCase().replace(/\s+/g, '-');
    },

    getSubcategoryClassFromString(subcategory) {
      if (!subcategory) return '';

      // Convert to lowercase and remove spaces for CSS class
      return subcategory.toLowerCase().replace(/\s+/g, '-');
    },

    async processLimitedBatch() {
      if (this.batchSize < 1 || this.batchSize > 20) {
        this.processingMessage = 'Batch size must be between 1 and 20';
        setTimeout(() => {
          this.processingMessage = '';
        }, 3000);
        return;
      }
      let token = this.$store.getters.getToken;
      this.processingDefects = true;
      this.processingMessage = `Processing batch of ${this.batchSize} defects...`;
      try {
        const response = await fetch(`${process.env.VUE_APP_API_PATH || '/api-statit2/'}process_limited_batch`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: "Bearer " + token
          },
          body: JSON.stringify({
            api_key: 'A-9UZe48N156o-lLuxvjR5IePXDhKwrVHu61UciQ9afl',
            project_id: '8a74c0cb-b3c8-4fd7-9823-8301276169de',
            timeFilter: this.selectedTimeFilter,
            batchSize: this.batchSize
          })
        });

        // Check for HTTP errors
        if (!response.ok) {
          const errorText = await response.text();
          let errorMessage;
          try {
            const errorJson = JSON.parse(errorText);
            errorMessage = errorJson.message || `HTTP error! status: ${response.status}`;
          } catch (e) {
            errorMessage = `HTTP error! status: ${response.status}: ${errorText}`;
          }
          throw new Error(errorMessage);
        }

        const data = await response.json();
        console.log("Batch processing result:", data);

        // Check for API errors
        if (data.status === 'error') {
          throw new Error(data.message || 'Unknown error occurred');
        }

        // Update processing message
        if (data.message) {
          this.processingMessage = data.message;
        }

        // Store the batch results
        this.batchResults = data.results || [];

        // Show the batch results modal
        if (this.batchResults.length > 0) {
          this.showBatchResultsModal = true;
        }

        // Refresh data
        this.fetchValidationData();
        this.fetchClassificationCounts();
        this.processingDefects = false;

      } catch (error) {
        console.error("Error processing batch:", error);
        this.processingDefects = false;
        this.processingMessage = `Error: ${error.message}`;

        // Clear error message after a delay
        setTimeout(() => {
          this.processingMessage = '';
        }, 10000); // Show error for longer (10 seconds)
      }
    },

    closeBatchResultsModal() {
      this.showBatchResultsModal = false;
      this.batchResults = [];
    },

    // Modal methods
    openClassificationModal(row) {
      this.selectedDefect = row;

      // Initialize with current classification or defaults
      if (row.classification) {
        if (typeof row.classification === 'string') {
          // Handle string classification (backward compatibility)
          this.editClassification = {
            primary: row.classification,
            subcategory: 'Other'
          };
        } else {
          // Handle object classification
          this.editClassification = {
            primary: row.classification.primary || 'Need More Info',
            subcategory: row.classification.subcategory || 'Need More Info'
          };
        }
      } else {
        // Default values for new classification
        this.editClassification = {
          primary: 'Mechanical',
          subcategory: 'Other'
        };
      }

      this.showClassificationModal = true;
    },

    closeClassificationModal() {
      this.showClassificationModal = false;
      this.selectedDefect = null;
    },

    async saveClassification() {
      if (!this.selectedDefect) return;

      try {
        const defectId = this.selectedDefect.DEFECT_ID;
        const classification = { ...this.editClassification };

        const response = await fetch(`${process.env.VUE_APP_API_PATH || '/api-statit2/'}update_classification`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify({
            defectId,
            classification
          })
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        // Update local data
        const defect = this.validationData.find(d => d.DEFECT_ID === defectId);
        if (defect) {
          defect.classification = classification;
        }

        // Refresh classification counts and apply filters
        this.fetchClassificationCounts();
        this.applyFilters();

        // Close the modal
        this.closeClassificationModal();
      } catch (error) {
        console.error("Error updating classification:", error);
      }
    },

    // Utility methods for Carbon components
    getClassificationTagKind(classification) {
      if (!classification) return 'red';

      const classType = classification.toLowerCase();
      switch (classType) {
        case 'mechanical':
          return 'purple';
        case 'functional':
          return 'blue';
        case 'need more info':
          return 'orange';
        default:
          return 'gray';
      }
    },

  }
};
</script>

<style scoped lang="scss">
.validation2-container {
  min-height: 100vh;
  background-color: #161616;
  color: #f4f4f4;
}

.main-content {
  padding: 2rem;
}

.page-header {
  margin-bottom: 2rem;

  h1 {
    font-size: 2rem;
    margin-bottom: 0.5rem;
  }

  p {
    color: #c6c6c6;
  }
}

.summary-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;

  .summary-tile {
    padding: 1.5rem;

    h4 {
      margin: 0 0 0.5rem 0;
      font-size: 0.875rem;
      color: #c6c6c6;
      text-transform: uppercase;
      letter-spacing: 0.16px;
    }

    .tile-value {
      font-size: 2rem;
      font-weight: 600;
      margin: 0;

      &.validated {
        color: #42be65;
      }

      &.unvalidated {
        color: #fa4d56;
      }
    }

    .tile-percentage {
      font-size: 0.875rem;
      color: #c6c6c6;
      margin: 0.25rem 0 0 0;
    }
  }
}

.controls-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 2rem;
  gap: 1rem;
  flex-wrap: wrap;

  .filter-group {
    min-width: 200px;
  }
}

.time-period-dropdown {
  display: flex;
  flex-direction: column;
  min-width: 200px;
}

.button-group {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.batch-process-controls {
  display: flex;
  align-items: flex-end;
  gap: 1rem;
}

.batch-size-input {
  display: flex;
  flex-direction: column;
  width: 80px;
}

.cv-input {
  background-color: #262626;
  color: #f4f4f4;
  border: 1px solid #525252;
  border-radius: 4px;
  padding: 0.5rem;
  font-size: 0.875rem;
  height: 40px;
  width: 100%;
}

.dropdown-label {
  font-size: 0.75rem;
  margin-bottom: 0.5rem;
  color: #c6c6c6;
}

.cv-dropdown {
  background-color: #262626;
  color: #f4f4f4;
  border: 1px solid #525252;
  border-radius: 4px;
  padding: 0.5rem;
  font-size: 0.875rem;
  height: 40px;
  width: 100%;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16'%3E%3Cpath fill='%23f4f4f4' d='M8 11L3 6h10z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 0.5rem center;
  cursor: pointer;
}

.cv-dropdown:focus {
  outline: 2px solid #0f62fe;
  outline-offset: -2px;
}

.pareto-section, .defects-table-section {
  margin-bottom: 2rem;

  h2 {
    margin-bottom: 1rem;
    font-size: 1.5rem;
  }
}

.chart-container {
  height: 400px;
  width: 100%;
}

.loading-indicator {
  display: flex;
  justify-content: center;
  padding: 2rem;
}

.table-container {
  margin-top: 1rem;
}

.classification-cell {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.classification-tag {
  padding: 0.25rem 0.5rem;
  border-radius: 1rem;
  font-size: 0.875rem;
  font-weight: 600;

  &.mechanical {
    background-color: #8a3ffc;
    color: white;
  }

  &.functional {
    background-color: #33b1ff;
    color: white;
  }

  &.unclassified {
    background-color: #da1e28;
    color: white;
  }

  &.need-more-info {
    background-color: #ff832b;
    color: white;
  }

  &.subcategory {
    &.scratches {
      background-color: #6929c4;
      color: white;
    }

    &.bent {
      background-color: #1192e8;
      color: white;
    }

    &.plugging {
      background-color: #005d5d;
      color: white;
    }

    &.discolor {
      background-color: #9f1853;
      color: white;
    }

    &.misalignment {
      background-color: #fa4d56;
      color: white;
    }

    &.other {
      background-color: #4589ff;
      color: white;
    }
  }
}

.edit-button {
  background-color: #393939;
  color: #f4f4f4;
  border: 1px solid #525252;
  border-radius: 4px;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  cursor: pointer;
  margin-left: 0.5rem;

  &:hover {
    background-color: #4c4c4c;
  }
}

.filter-dropdown {
  display: flex;
  flex-direction: column;
  min-width: 180px;
  margin-right: 1rem;
}

.processing-message {
  margin-left: 1rem;
  color: #33b1ff;
  font-size: 0.875rem;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

/* Modal styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: #262626;
  border-radius: 4px;
  padding: 2rem;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);

  h3 {
    margin-top: 0;
    margin-bottom: 1rem;
    font-size: 1.5rem;
  }

  .description-text {
    margin-bottom: 1.5rem;
    padding: 0.75rem;
    background-color: #393939;
    border-radius: 4px;
    max-height: 100px;
    overflow-y: auto;
  }
}

.batch-results-modal {
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
}

.batch-results-container {
  margin: 1rem 0;
  max-height: 60vh;
  overflow-y: auto;
  border: 1px solid #525252;
  border-radius: 4px;
}

.batch-results-table {
  width: 100%;
  border-collapse: collapse;

  th, td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #525252;
  }

  th {
    background-color: #393939;
    font-weight: 600;
  }

  tr:hover {
    background-color: #333333;
  }

  .description-cell {
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.modal-form {
  margin-bottom: 1.5rem;

  .form-group {
    margin-bottom: 1rem;

    label {
      display: block;
      margin-bottom: 0.5rem;
      font-weight: 600;
    }
  }

  .modal-select {
    width: 100%;
    padding: 0.5rem;
    background-color: #393939;
    color: #f4f4f4;
    border: 1px solid #525252;
    border-radius: 4px;
    font-size: 1rem;
  }
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;

  .modal-button {
    padding: 0.5rem 1rem;
    border-radius: 4px;
    font-size: 1rem;
    cursor: pointer;

    &.cancel {
      background-color: #393939;
      color: #f4f4f4;
      border: 1px solid #525252;

      &:hover {
        background-color: #4c4c4c;
      }
    }

    &.save {
      background-color: #0f62fe;
      color: white;
      border: none;

      &:hover {
        background-color: #0353e9;
      }
    }
  }
}
</style>
