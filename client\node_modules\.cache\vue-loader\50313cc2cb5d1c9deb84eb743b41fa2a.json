{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\ActionTracker\\ActionTracker.vue?vue&type=template&id=55f84a0b&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\ActionTracker\\ActionTracker.vue", "mtime": 1748988452772}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}