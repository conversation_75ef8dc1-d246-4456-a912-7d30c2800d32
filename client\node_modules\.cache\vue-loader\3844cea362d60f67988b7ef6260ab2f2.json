{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\PQEDashboard\\PQEOwnerDashboard.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\PQEDashboard\\PQEOwnerDashboard.vue", "mtime": 1748986897909}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["PQEOwnerDashboard.vue"], "names": [], "mappings": ";AA8l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file": "PQEOwnerDashboard.vue", "sourceRoot": "src/views/PQEDashboard", "sourcesContent": ["<template>\n  <div class=\"pqe-owner-dashboard-container\">\n    <div class=\"content-wrapper\">\n      <!-- Header Section with Critical Issues Summary -->\n      <div class=\"dashboard-header\">\n        <h3>{{ pqeOwner }}'s Dashboard</h3>\n        <div class=\"last-updated-text\">\n          Last Updated: {{ new Date().toLocaleString() }}\n        </div>\n      </div>\n\n      <!-- Key Metrics Section -->\n      <div class=\"key-metrics-section\">\n        <div class=\"metric-card clickable\" @click=\"scrollToAlerts\" title=\"Click to go to alerts section\">\n          <div class=\"metric-icon alerts\">\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n              <path d=\"M16,2A14,14,0,1,0,30,16,14,14,0,0,0,16,2Zm0,26A12,12,0,1,1,28,16,12,12,0,0,1,16,28Z\"></path>\n              <path d=\"M15,8h2v11H15Zm1,14a1.5,1.5,0,1,1-1.5,1.5A1.5,1.5,0,0,1,16,22Z\"></path>\n            </svg>\n          </div>\n          <div class=\"metric-content\">\n            <div class=\"metric-label\"># Alerts</div>\n            <div class=\"metric-value\">{{ totalAlerts }}</div>\n            <div class=\"metric-description\">Current Month</div>\n          </div>\n        </div>\n\n        <div class=\"metric-card clickable\" @click=\"goToActionTracker\" title=\"Click to go to Action Tracker\">\n          <div class=\"metric-icon in-progress\">\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n              <path d=\"M16,2A14,14,0,1,0,30,16,14,14,0,0,0,16,2Zm0,26A12,12,0,1,1,28,16,12,12,0,0,1,16,28Z\"></path>\n              <path d=\"M20.59,22,15,16.41V7h2v8.58l5,5.01L20.59,22z\"></path>\n            </svg>\n          </div>\n          <div class=\"metric-content\">\n            <div class=\"metric-label\"># In Progress Issues</div>\n            <div class=\"metric-value\">{{ inProgressIssuesCount }}</div>\n            <div class=\"metric-description\">From Action Tracker</div>\n          </div>\n        </div>\n\n        <div class=\"metric-card clickable\" @click=\"goToValidationPage\" title=\"Click to go to Validation page\">\n          <div class=\"metric-icon validated\">\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n              <path d=\"M26,4H6A2,2,0,0,0,4,6V26a2,2,0,0,0,2,2H26a2,2,0,0,0,2-2V6A2,2,0,0,0,26,4ZM14,21.5l-5-5,1.59-1.5L14,18.35,21.41,11,23,12.58Z\"></path>\n            </svg>\n          </div>\n          <div class=\"metric-content\">\n            <div class=\"metric-label\">Validated Fails</div>\n            <div class=\"metric-value\">{{ validatedCount }}/{{ totalFails }}</div>\n            <div class=\"metric-description\">This Month</div>\n          </div>\n        </div>\n\n        <div class=\"metric-card clickable\" @click=\"goToHeatmap\" title=\"Click to go to Heatmap\">\n          <div class=\"metric-icon groups-over-target\">\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n              <path d=\"M4,20V12a2,2,0,0,1,2-2H8a2,2,0,0,1,2,2v8a2,2,0,0,1-2,2H6A2,2,0,0,1,4,20Z\"></path>\n              <path d=\"M12,20V6a2,2,0,0,1,2-2h2a2,2,0,0,1,2,2V20a2,2,0,0,1-2,2H14A2,2,0,0,1,12,20Z\"></path>\n              <path d=\"M20,20V16a2,2,0,0,1,2-2h2a2,2,0,0,1,2,2v4a2,2,0,0,1-2,2H22A2,2,0,0,1,20,20Z\"></path>\n            </svg>\n          </div>\n          <div class=\"metric-content\">\n            <div class=\"metric-label\"># Groups Over Target</div>\n            <div class=\"metric-value\">{{ groupsOverTargetCount }}</div>\n            <div class=\"metric-description\">Current Month</div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Main Content Layout -->\n      <div class=\"dashboard-main-content\">\n        <!-- Left Column -->\n        <div class=\"dashboard-column\">\n          <!-- Action Tracker Alerts Section -->\n          <div class=\"section-card\">\n            <div class=\"section-header\" @click=\"toggleActionTrackerAlertsExpanded\">\n              <div class=\"section-title-container\">\n                <h4 class=\"section-title\">Action Tracker Alerts</h4>\n                <div class=\"section-subtitle\">\n                  Issues from action tracker above target for the month\n                </div>\n              </div>\n              <div class=\"section-controls\">\n                <div class=\"status-indicator flashing\" v-if=\"totalActionTrackerAlerts > 0\">\n                  {{ totalActionTrackerAlerts }}\n                </div>\n                <div class=\"expand-indicator\" :class=\"{ expanded: isActionTrackerAlertsExpanded }\">\n                  <svg width=\"16\" height=\"16\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                    <path d=\"M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z\"></path>\n                  </svg>\n                </div>\n              </div>\n            </div>\n\n            <div v-if=\"isActionTrackerAlertsExpanded\" class=\"section-content\">\n              <!-- In-Progress Issue Alerts -->\n              <div class=\"alert-subsection\">\n                <div class=\"subsection-header\" @click=\"toggleInProgressAlertsExpanded\">\n                  <h5 class=\"subsection-title\">In-Progress Issue Alerts</h5>\n                  <div class=\"subsection-controls\">\n                    <div class=\"status-indicator\" v-if=\"inProgressAlerts.length > 0\" style=\"background-color: #ff832b;\">\n                      {{ inProgressAlerts.length }}\n                    </div>\n                    <div class=\"expand-indicator\" :class=\"{ expanded: isInProgressAlertsExpanded }\">\n                      <svg width=\"12\" height=\"12\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                        <path d=\"M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z\"></path>\n                      </svg>\n                    </div>\n                  </div>\n                </div>\n\n                <div v-if=\"isInProgressAlertsExpanded\" class=\"subsection-content\">\n                  <div v-if=\"inProgressAlerts.length > 0\" class=\"issues-list\">\n                    <div v-for=\"alert in inProgressAlerts\" :key=\"alert.id\" class=\"issue-card action-tracker-alert\">\n                      <div class=\"issue-header\">\n                        <div class=\"issue-tags\">\n                          <cv-tag kind=\"orange\" label=\"In-Progress\" />\n                          <cv-tag kind=\"red\" :label=\"alert.severity\" />\n                        </div>\n                        <div class=\"issue-title\">{{ alert.category }}</div>\n                        <div class=\"issue-metadata\">\n                          <span class=\"issue-multiplier medium-severity\">{{ alert.xFactor }}x</span>\n                        </div>\n                      </div>\n                      <div class=\"issue-actions\">\n                        <cv-button\n                          kind=\"primary\"\n                          size=\"small\"\n                          @click=\"viewActionTrackerAlert(alert)\"\n                        >\n                          View\n                        </cv-button>\n                      </div>\n                    </div>\n                  </div>\n                  <div v-else class=\"no-data-message\">\n                    No in-progress issues above target this month.\n                  </div>\n                </div>\n              </div>\n\n              <!-- Outstanding Issue Alerts -->\n              <div class=\"alert-subsection\">\n                <div class=\"subsection-header\" @click=\"toggleOutstandingAlertsExpanded\">\n                  <h5 class=\"subsection-title\">Outstanding Issue Alerts</h5>\n                  <div class=\"subsection-controls\">\n                    <div class=\"status-indicator\" v-if=\"outstandingAlerts.length > 0\" style=\"background-color: #0f62fe;\">\n                      {{ outstandingAlerts.length }}\n                    </div>\n                    <div class=\"expand-indicator\" :class=\"{ expanded: isOutstandingAlertsExpanded }\">\n                      <svg width=\"12\" height=\"12\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                        <path d=\"M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z\"></path>\n                      </svg>\n                    </div>\n                  </div>\n                </div>\n\n                <div v-if=\"isOutstandingAlertsExpanded\" class=\"subsection-content\">\n                  <div v-if=\"outstandingAlerts.length > 0\" class=\"issues-list\">\n                    <div v-for=\"alert in outstandingAlerts\" :key=\"alert.id\" class=\"issue-card action-tracker-alert\">\n                      <div class=\"issue-header\">\n                        <div class=\"issue-tags\">\n                          <cv-tag kind=\"blue\" label=\"Outstanding\" />\n                          <cv-tag kind=\"red\" :label=\"alert.severity\" />\n                        </div>\n                        <div class=\"issue-title\">{{ alert.category }}</div>\n                        <div class=\"issue-metadata\">\n                          <span class=\"issue-multiplier medium-severity\">{{ alert.xFactor }}x</span>\n                        </div>\n                      </div>\n                      <div class=\"issue-actions\">\n                        <cv-button\n                          kind=\"primary\"\n                          size=\"small\"\n                          @click=\"viewActionTrackerAlert(alert)\"\n                        >\n                          View\n                        </cv-button>\n                      </div>\n                    </div>\n                  </div>\n                  <div v-else class=\"no-data-message\">\n                    No outstanding issues above target this month.\n                  </div>\n                </div>\n              </div>\n\n              <!-- Resolved Issue Alerts -->\n              <div class=\"alert-subsection\">\n                <div class=\"subsection-header\" @click=\"toggleResolvedAlertsExpanded\">\n                  <h5 class=\"subsection-title\">Resolved Issue Alerts</h5>\n                  <div class=\"subsection-controls\">\n                    <div class=\"status-indicator\" v-if=\"resolvedAlerts.length > 0\" style=\"background-color: #24a148;\">\n                      {{ resolvedAlerts.length }}\n                    </div>\n                    <div class=\"expand-indicator\" :class=\"{ expanded: isResolvedAlertsExpanded }\">\n                      <svg width=\"12\" height=\"12\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                        <path d=\"M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z\"></path>\n                      </svg>\n                    </div>\n                  </div>\n                </div>\n\n                <div v-if=\"isResolvedAlertsExpanded\" class=\"subsection-content\">\n                  <div v-if=\"resolvedAlerts.length > 0\" class=\"issues-list\">\n                    <div v-for=\"alert in resolvedAlerts\" :key=\"alert.id\" class=\"issue-card action-tracker-alert\">\n                      <div class=\"issue-header\">\n                        <div class=\"issue-tags\">\n                          <cv-tag kind=\"green\" label=\"Resolved\" />\n                          <cv-tag kind=\"red\" :label=\"alert.severity\" />\n                        </div>\n                        <div class=\"issue-title\">{{ alert.category }}</div>\n                        <div class=\"issue-metadata\">\n                          <span class=\"issue-multiplier good-performance\">{{ alert.xFactor }}x</span>\n                        </div>\n                      </div>\n                      <div class=\"issue-actions\">\n                        <cv-button\n                          kind=\"primary\"\n                          size=\"small\"\n                          @click=\"viewActionTrackerAlert(alert)\"\n                        >\n                          View\n                        </cv-button>\n                      </div>\n                    </div>\n                  </div>\n                  <div v-else class=\"no-data-message\">\n                    No resolved issues above target this month.\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Critical Issues Section -->\n          <div class=\"section-card\">\n            <div class=\"section-header critical-issues-header\" @click=\"toggleCriticalIssuesExpanded\">\n              <div class=\"section-title-container\">\n                <h4 class=\"section-title\">Critical Issues</h4>\n                <div class=\"section-subtitle\">New/unknown issues requiring immediate attention this month</div>\n              </div>\n              <div class=\"section-controls\">\n                <div class=\"status-indicator\" :class=\"{ 'flashing': !isCriticalIssuesExpanded && unresolvedCriticalIssues.length > 0 }\">\n                  {{ unresolvedCriticalIssues.length }}\n                </div>\n                <div class=\"expand-indicator\" :class=\"{ 'expanded': isCriticalIssuesExpanded }\">\n                  <svg width=\"16\" height=\"16\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                    <path d=\"M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z\"></path>\n                  </svg>\n                </div>\n              </div>\n            </div>\n\n            <div v-if=\"isCriticalIssuesExpanded\" class=\"section-content\">\n              <!-- Filter Controls in a Single Row -->\n              <div class=\"filter-container\">\n                <div class=\"filter-header\">\n                  <h5 class=\"filter-title\">Filter Issues</h5>\n                  <cv-button\n                    kind=\"ghost\"\n                    size=\"small\"\n                    class=\"clear-filters-button\"\n                    @click=\"clearFilters\"\n                    v-if=\"isFiltersActive\"\n                  >\n                    Clear Filters\n                  </cv-button>\n                </div>\n\n                <div class=\"filter-controls\">\n                  <div class=\"filter-group\">\n                    <label for=\"severity-dropdown\" class=\"filter-label\">Severity:</label>\n                    <cv-dropdown\n                      id=\"severity-dropdown\"\n                      v-model=\"severityFilter\"\n                      @change=\"handleSeverityFilterChange\"\n                      class=\"filter-dropdown\"\n                    >\n                      <cv-dropdown-item value=\"all\">All Severities</cv-dropdown-item>\n                      <cv-dropdown-item value=\"high\">High</cv-dropdown-item>\n                      <cv-dropdown-item value=\"medium\">Medium</cv-dropdown-item>\n                    </cv-dropdown>\n                  </div>\n\n                  <div class=\"filter-group\">\n                    <label for=\"analysis-dropdown\" class=\"filter-label\">Analysis Type:</label>\n                    <cv-dropdown\n                      id=\"analysis-dropdown\"\n                      v-model=\"analysisTypeFilter\"\n                      @change=\"handleAnalysisFilterChange\"\n                      class=\"filter-dropdown\"\n                    >\n                      <cv-dropdown-item value=\"all\">All Analysis Types</cv-dropdown-item>\n                      <cv-dropdown-item value=\"Root Cause\">Root Cause</cv-dropdown-item>\n                    </cv-dropdown>\n                  </div>\n                </div>\n              </div>\n\n              <!-- Critical Issues List -->\n              <div v-if=\"filteredCriticalIssues.length > 0\" class=\"issues-list\">\n                <div\n                  v-for=\"issue in filteredCriticalIssues\"\n                  :key=\"issue.id\"\n                  class=\"issue-card\"\n                  @click=\"toggleIssueExpanded(issue)\"\n                  :class=\"{ 'expanded': isIssueExpanded(issue.id) }\"\n                >\n                  <div class=\"issue-header\">\n                    <div class=\"issue-tags\">\n                      <cv-tag\n                        :kind=\"issue.severity === 'high' ? 'red' : 'magenta'\"\n                        :label=\"issue.severity === 'high' ? 'High' : 'Medium'\"\n                      />\n                      <cv-tag\n                        kind=\"purple\"\n                        class=\"analysis-tag\"\n                        :label=\"issue.analysisType\"\n                      />\n                    </div>\n                    <span class=\"issue-title\">{{ issue.category }}</span>\n                    <div class=\"issue-metadata\">\n                      <cv-tag\n                        kind=\"cool-gray\"\n                        class=\"month-tag\"\n                        :label=\"issue.month\"\n                      />\n                      <span class=\"issue-multiplier\" :class=\"issue.severity === 'high' ? 'high-severity' : 'medium-severity'\">\n                        {{ issue.increaseMultiplier === '(new)' ? '(new)' : `${issue.increaseMultiplier}x` }}\n                      </span>\n                    </div>\n                    <div class=\"expand-indicator\">\n                      <svg width=\"16\" height=\"16\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                        <path d=\"M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z\"></path>\n                      </svg>\n                    </div>\n                  </div>\n\n                  <div class=\"issue-content\" v-if=\"isIssueExpanded(issue.id)\">\n                    <div class=\"ai-description\">\n                      <p>{{ issue.aiDescription || 'Loading AI description...' }}</p>\n                    </div>\n\n                    <!-- Action Comment Text Box -->\n                    <div class=\"action-comment\">\n                      <cv-text-area\n                        v-model=\"issue.comment\"\n                        label=\"Action Comments\"\n                        placeholder=\"Add your comments or action plan here...\"\n                        :helper-text=\"issue.comment ? `${issue.comment.length} characters` : 'This will be added to the Action Tracker'\"\n                      ></cv-text-area>\n                    </div>\n\n                    <div class=\"issue-actions\">\n                      <cv-button\n                        kind=\"tertiary\"\n                        size=\"small\"\n                        @click.stop=\"viewIssueDetails(issue)\"\n                      >\n                        View Data\n                      </cv-button>\n                      <cv-button\n                        kind=\"tertiary\"\n                        size=\"small\"\n                        @click.stop=\"updateIssue(issue)\"\n                      >\n                        Update\n                      </cv-button>\n                      <cv-button\n                        kind=\"secondary\"\n                        size=\"small\"\n                        @click.stop=\"updateIssue(issue, false, true)\"\n                      >\n                        Mark Outstanding\n                      </cv-button>\n                      <cv-button\n                        kind=\"primary\"\n                        size=\"small\"\n                        @click.stop=\"updateIssue(issue, true, false)\"\n                      >\n                        Mark Resolved\n                      </cv-button>\n                      <cv-button\n                        kind=\"ghost\"\n                        size=\"small\"\n                        @click.stop=\"addCriticalIssueToActionTracker(issue)\"\n                      >\n                        Add item to action tracker\n                      </cv-button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <div v-else class=\"no-data-message\">\n                No critical issues found matching the selected filters.\n              </div>\n            </div>\n          </div>\n\n          <!-- Root Cause Analysis Section -->\n          <div class=\"section-card chart-section\">\n            <div class=\"section-header\">\n              <div class=\"section-title-container\">\n                <h4 class=\"section-title\">Analysis</h4>\n                <div class=\"section-subtitle\">Defect categories and fail rates over time</div>\n              </div>\n            </div>\n\n            <div class=\"chart-controls\">\n              <div class=\"control-group\">\n                <label class=\"control-label\">View By:</label>\n                <cv-dropdown\n                  v-model=\"rootCauseViewBy\"\n                  @change=\"handleRootCauseViewByChange\"\n                  class=\"control-dropdown\"\n                >\n                  <cv-dropdown-item value=\"rootCause\">Root Cause</cv-dropdown-item>\n                  <cv-dropdown-item value=\"vintage\">Vintage</cv-dropdown-item>\n                  <cv-dropdown-item value=\"sector\">Sector</cv-dropdown-item>\n                  <cv-dropdown-item value=\"supplier\">Supplier</cv-dropdown-item>\n                </cv-dropdown>\n              </div>\n\n              <div class=\"control-group\">\n                <label class=\"control-label\">Time Range:</label>\n                <cv-dropdown\n                  v-model=\"rootCauseTimeRange\"\n                  @change=\"handleRootCauseTimeRangeChange\"\n                  class=\"control-dropdown\"\n                >\n                  <cv-dropdown-item value=\"3month\">3 Months</cv-dropdown-item>\n                  <cv-dropdown-item value=\"6month\">6 Months</cv-dropdown-item>\n                </cv-dropdown>\n              </div>\n\n              <div class=\"control-group\" v-if=\"breakoutGroups.length > 0\">\n                <label class=\"control-label\">Group:</label>\n                <cv-dropdown\n                  v-model=\"rootCauseSelectedGroup\"\n                  @change=\"handleRootCauseGroupChange\"\n                  class=\"control-dropdown\"\n                >\n                  <cv-dropdown-item value=\"all\">All Groups</cv-dropdown-item>\n                  <cv-dropdown-item\n                    v-for=\"group in breakoutGroups\"\n                    :key=\"group.name\"\n                    :value=\"group.name\"\n                  >\n                    {{ group.name }}\n                  </cv-dropdown-item>\n                </cv-dropdown>\n              </div>\n            </div>\n\n            <div class=\"chart-container\">\n              <div v-if=\"isRootCauseDataLoading\" >\n                        Loading Chart...\n                        <RootCauseChart :data = [] :loading=\"isRootCauseDataLoading\"/>\n                      </div>\n              <RootCauseChart\n               v-if=\"rootCauseChartData.length > 0\"\n                :data=\"rootCauseChartData\"\n                :viewBy=\"rootCauseViewBy\"\n                :timeRange=\"rootCauseTimeRange\"\n                :selectedGroup=\"rootCauseSelectedGroup\"\n                :loading=\"isRootCauseDataLoading\"\n                @bar-click=\"handleRootCauseBarClick\"\n              />\n\n              <div v-if=\"rootCauseChartData.length == 0 && !isRootCauseDataLoading\" >\n                        No data available\n                      </div>\n              <!-- <div v-else-if=\"isRootCauseDataLoading\" class=\"loading-container\">\n                <p>Loading root cause data...</p>\n              </div>\n              <div v-else class=\"no-data-message\">\n                No root cause data available for the selected criteria.\n              </div> -->\n            </div>\n\n            <!-- <div class=\"section-footer\">\n              <p class=\"section-description\">\n                Root cause analysis showing defect categories and their fail rates over time.\n                Click on bars to see detailed information.\n              </p>\n            </div> -->\n          </div>\n\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Action Tracker Alert Modal -->\n  <cv-modal\n    :visible=\"showActionTrackerAlertModal\"\n    @modal-hidden=\"closeActionTrackerAlertModal\"\n    class=\"action-tracker-alert-modal\"\n    :size=\"'lg'\"\n  >\n    <template slot=\"title\">\n      <span v-if=\"selectedAlert\">\n        Alert Details - {{ selectedAlert.category }}\n      </span>\n      <span v-else>\n        Alert Details\n      </span>\n    </template>\n    <template slot=\"content\">\n      <div v-if=\"selectedAlert\" class=\"modal-content\">\n        <!-- Alert Information -->\n        <div class=\"modal-section\">\n          <div class=\"section-title\">Alert Information</div>\n          <div class=\"alert-info-grid\">\n            <div class=\"info-item\">\n              <span class=\"info-label\">Category:</span>\n              <span class=\"info-value\">{{ selectedAlert.category }}</span>\n            </div>\n            <div class=\"info-item\">\n              <span class=\"info-label\">Severity:</span>\n              <cv-tag :kind=\"selectedAlert.severity === 'High' ? 'red' : 'magenta'\" :label=\"selectedAlert.severity\" />\n            </div>\n            <div class=\"info-item\">\n              <span class=\"info-label\">X-Factor:</span>\n              <span class=\"info-value\">{{ selectedAlert.xFactor }}x</span>\n            </div>\n            <div class=\"info-item\">\n              <span class=\"info-label\">Status:</span>\n              <cv-tag\n                :kind=\"selectedAlert.status === 'In-Progress' ? 'orange' : selectedAlert.status === 'Outstanding' ? 'blue' : 'green'\"\n                :label=\"selectedAlert.status\"\n              />\n            </div>\n          </div>\n        </div>\n\n        <!-- AI Insight Section -->\n        <div class=\"modal-section\">\n          <div class=\"section-title\">AI Insight</div>\n          <div v-if=\"isLoadingAiInsight\" class=\"loading-message\">\n            Generating AI insight...\n          </div>\n          <div v-else class=\"ai-insight-content\">\n            {{ aiInsight }}\n          </div>\n        </div>\n\n        <!-- New Alert Update Section -->\n        <div class=\"modal-section\">\n          <div class=\"section-title\">Add New Alert Update</div>\n          <div class=\"add-update-form\">\n            <cv-text-area\n              v-model=\"newAlertUpdate\"\n              label=\"Update Content\"\n              placeholder=\"Enter update details...\"\n              rows=\"4\"\n            ></cv-text-area>\n            <div class=\"update-form-actions\">\n              <cv-button\n                kind=\"primary\"\n                @click=\"addAlertUpdate\"\n                :disabled=\"!newAlertUpdate.trim()\"\n              >\n                Add Update\n              </cv-button>\n            </div>\n          </div>\n        </div>\n\n        <!-- Alert History Section -->\n        <div class=\"modal-section\">\n          <div class=\"section-title\">Alert History</div>\n          <div v-if=\"alertHistory.length > 0\" class=\"alert-history-table\">\n            <div class=\"history-header\">\n              <div class=\"history-column\">Date</div>\n              <div class=\"history-column\">Update</div>\n              <div class=\"history-column\">Updated By</div>\n            </div>\n            <div\n              v-for=\"(historyItem, index) in alertHistory\"\n              :key=\"index\"\n              class=\"history-row\"\n            >\n              <div class=\"history-cell\">{{ historyItem.date }}</div>\n              <div class=\"history-cell\">{{ historyItem.update }}</div>\n              <div class=\"history-cell\">{{ historyItem.updatedBy }}</div>\n            </div>\n          </div>\n          <div v-else class=\"no-history-message\">\n            No alert history available.\n          </div>\n        </div>\n\n        <!-- Modal Actions -->\n        <div class=\"modal-actions\">\n          <cv-button kind=\"secondary\" @click=\"closeActionTrackerAlertModal\">Close</cv-button>\n        </div>\n      </div>\n    </template>\n  </cv-modal>\n</template>\n\n<script>\nimport {\n  CvButton,\n  CvTag,\n  CvTextArea,\n  CvDropdown,\n  CvDropdownItem,\n  CvModal\n} from '@carbon/vue';\n\nimport RootCauseChart from '@/components/RootCauseChart/RootCauseChart';\n\nexport default {\n  name: 'PQEOwnerDashboard',\n  components: {\n    CvButton,\n    CvTag,\n    CvTextArea,\n    CvDropdown,\n    CvDropdownItem,\n    CvModal,\n    RootCauseChart\n  },\n  props: {\n    pqeOwner: {\n      type: String,\n      required: true\n    }\n  },\n  data() {\n    return {\n      // Critical Issues Data\n      newCriticalIssues: [],\n      unresolvedCriticalIssues: [],\n      expandedIssueIds: [], // Track which issues are expanded\n      isCriticalIssuesExpanded: false, // Track if critical issues section is expanded\n\n      // Action Tracker Alerts Data\n      isActionTrackerAlertsExpanded: false,\n      isInProgressAlertsExpanded: false,\n      isOutstandingAlertsExpanded: false,\n      isResolvedAlertsExpanded: false,\n\n      // Action Tracker Alert Data\n      inProgressAlerts: [],\n      outstandingAlerts: [],\n      resolvedAlerts: [],\n\n      // Filtering for Critical Issues\n      selectedFilters: {\n        severity: [],\n        analysisType: []\n      },\n      severityFilter: 'all',\n      analysisTypeFilter: 'all',\n\n      // Validation Data\n      validatedCount: 0,\n      unvalidatedCount: 0,\n\n      // Breakout Groups Data\n      breakoutGroups: [],\n\n      // Root Cause Chart Data\n      rootCauseChartData: [],\n      isRootCauseDataLoading: true,\n\n      // Root Cause Chart Controls\n      rootCauseViewBy: 'rootCause',\n      rootCauseTimeRange: '6month',\n      rootCauseSelectedGroup: 'all',\n\n      // Loading States\n      isLoading: false,\n\n      // Action Tracker Alert Modal Data\n      showActionTrackerAlertModal: false,\n      selectedAlert: null,\n      alertHistory: [],\n      newAlertUpdate: '',\n      aiInsight: '',\n      isLoadingAiInsight: false\n    };\n  },\n  computed: {\n    // Filtered critical issues based on selected filters\n    filteredCriticalIssues() {\n      // If no filters are selected, return all issues\n      if (this.selectedFilters.severity.length === 0 && this.selectedFilters.analysisType.length === 0) {\n        return this.unresolvedCriticalIssues;\n      }\n\n      // Apply filters\n      return this.unresolvedCriticalIssues.filter(issue => {\n        // Check if issue passes severity filter\n        const passesSeverityFilter = this.selectedFilters.severity.length === 0 ||\n                                    this.selectedFilters.severity.includes(issue.severity);\n\n        // Check if issue passes analysis type filter\n        const passesAnalysisFilter = this.selectedFilters.analysisType.length === 0 ||\n                                    this.selectedFilters.analysisType.includes(issue.analysisType);\n\n        // Issue must pass both filters\n        return passesSeverityFilter && passesAnalysisFilter;\n      });\n    },\n\n    // Check if any filters are active\n    isFiltersActive() {\n      return this.selectedFilters.severity.length > 0 || this.selectedFilters.analysisType.length > 0;\n    },\n\n\n\n    // Calculate total alerts (critical breakout groups)\n    totalAlerts() {\n      return this.breakoutGroups.filter(group => group.xFactor >= 1.5).length;\n    },\n\n    // Calculate total in-progress issues from action tracker\n    inProgressIssuesCount() {\n      // This would normally fetch from action tracker API\n      // For now, return a calculated value based on action tracker alerts\n      return this.inProgressAlerts.length;\n    },\n\n    // Calculate total fails (validated + unvalidated)\n    totalFails() {\n      return this.validatedCount + this.unvalidatedCount;\n    },\n\n    // Calculate groups over target (xFactor > 1.0)\n    groupsOverTargetCount() {\n      return this.breakoutGroups.filter(group => group.xFactor > 1.0).length;\n    },\n\n    // Calculate total action tracker alerts\n    totalActionTrackerAlerts() {\n      return this.inProgressAlerts.length + this.outstandingAlerts.length + this.resolvedAlerts.length;\n    }\n  },\n  watch: {\n    pqeOwner: {\n      immediate: true,\n      handler(newValue, oldValue) {\n        console.log(`PQE Owner changed from ${oldValue} to ${newValue}`);\n        if (newValue) {\n          // Reset the group selection when PQE owner changes\n          this.rootCauseSelectedGroup = 'all';\n          this.loadDashboardData();\n        }\n      }\n    }\n  },\n  methods: {\n    loadDashboardData() {\n      this.loadCriticalIssues();\n      this.loadValidationCounts();\n      this.loadActionTrackerAlerts();\n      this.loadRootCauseData();\n    },\n\n\n\n\n\n    async loadCriticalIssues() {\n      console.log(`Loading critical issues for PQE owner: ${this.pqeOwner}`);\n      this.isLoading = true;\n\n      try {\n        // Get authentication config\n        const config = this.getAuthConfig();\n\n        // First, load the breakout groups for this PQE owner\n        await this.loadBreakoutGroups();\n\n        // Fetch critical issues from the API\n        const response = await fetch('/api-statit2/get_pqe_critical_issues', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({\n            pqeOwner: this.pqeOwner\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch critical issues: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success') {\n          // Filter critical issues to only include those related to this PQE's breakout groups\n          const allIssues = data.critical_issues || [];\n\n          // If we have breakout groups, filter issues to only include those for this PQE's groups\n          if (this.breakoutGroups.length > 0) {\n            this.unresolvedCriticalIssues = allIssues.filter(issue => {\n              // Check if the issue's category matches any of the PQE's breakout groups\n              return this.breakoutGroups.some(group =>\n                issue.category.includes(group.name) || group.name.includes(issue.category)\n              );\n            });\n          } else {\n            // If we don't have breakout groups yet, use all issues\n            this.unresolvedCriticalIssues = allIssues;\n          }\n\n          console.log(`Loaded ${this.unresolvedCriticalIssues.length} critical issues for ${this.pqeOwner}'s groups`);\n        } else {\n          console.error('Failed to load critical issues:', data.message);\n          // Use sample data for development\n          this.loadSampleCriticalIssues();\n        }\n      } catch (error) {\n        console.error('Error loading critical issues:', error);\n        // Use sample data for development\n        this.loadSampleCriticalIssues();\n      } finally {\n        this.isLoading = false;\n      }\n    },\n\n    async loadBreakoutGroups() {\n      console.log(`Loading breakout groups for PQE owner: ${this.pqeOwner}`);\n\n      try {\n        // Get authentication config\n        const config = this.getAuthConfig();\n\n        // Fetch breakout groups from the API\n        const response = await fetch('/api-statit2/get_pqe_breakout_groups', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({\n            pqeOwner: this.pqeOwner\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch breakout groups: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success') {\n          this.breakoutGroups = data.breakout_groups || [];\n          console.log(`Loaded ${this.breakoutGroups.length} breakout groups for ${this.pqeOwner}`);\n\n          // Reload root cause chart data now that we have the correct breakout groups\n          await this.loadRootCauseData();\n        } else {\n          console.error('Failed to load breakout groups:', data.message);\n          // Use sample data for development\n          this.loadSampleBreakoutGroups();\n          // Reload root cause chart data with sample groups\n          await this.loadRootCauseData();\n        }\n      } catch (error) {\n        console.error('Error loading breakout groups:', error);\n        // Use sample data for development\n        this.loadSampleBreakoutGroups();\n        // Reload root cause chart data with sample groups\n        await this.loadRootCauseData();\n      }\n    },\n\n    async loadSampleBreakoutGroups() {\n      // Sample data for development\n      if (this.pqeOwner === 'Albert G.') {\n        this.breakoutGroups = [\n          { name: 'Fan Themis', status: 'Short-Term Spike', xFactor: 3.2 },\n          { name: 'Victoria Crypto', status: 'Sustained Problem', xFactor: 1.8 },\n          { name: 'Quantum Nexus', status: 'Sustained Problem', xFactor: 1.6 }\n        ];\n      } else if (this.pqeOwner === 'Sarah L.') {\n        this.breakoutGroups = [\n          { name: 'Stellar Core', status: 'Short-Term Spike', xFactor: 2.5 },\n          { name: 'Nebula Drive', status: 'Sustained Problem', xFactor: 1.7 }\n        ];\n      } else {\n        // Default sample data\n        this.breakoutGroups = [\n          { name: 'Sample Group 1', status: 'Normal', xFactor: 0.9 },\n          { name: 'Sample Group 2', status: 'Normal', xFactor: 0.8 }\n        ];\n      }\n\n      // Reload root cause chart data with the sample groups\n      await this.loadRootCauseData();\n    },\n\n    loadSampleCriticalIssues() {\n      // Sample data for development\n      this.unresolvedCriticalIssues = [\n        {\n          id: 'ci1',\n          category: 'Fan Themis',\n          month: '2024-06',\n          severity: 'high',\n          increaseMultiplier: '3.2',\n          aiDescription: 'Fan Themis showing 3.2x spike in June 2024 with 12 fails vs 4 expected. Root cause appears to be airflow issues in the new design.',\n          comment: '',\n          analysisType: 'Root Cause'\n        },\n        {\n          id: 'ci2',\n          category: 'Victoria Crypto',\n          month: '2024-06',\n          severity: 'medium',\n          increaseMultiplier: '1.8',\n          aiDescription: 'Victoria Crypto has sustained problem with 1.8x target rate for 3 consecutive months. Consistent pattern of power delivery issues.',\n          comment: '',\n          analysisType: 'Root Cause'\n        },\n\n      ];\n\n      // Set new critical issues to empty array for now\n      this.newCriticalIssues = [];\n    },\n\n    async loadValidationCounts() {\n      console.log(`Loading validation counts for PQE owner: ${this.pqeOwner}`);\n\n      try {\n        // Get authentication config\n        const config = this.getAuthConfig();\n\n        // Fetch validation counts from the API\n        const response = await fetch('/api-statit2/get_validation_counts', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({\n            pqeOwner: this.pqeOwner\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch validation counts: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success') {\n          this.validatedCount = data.validated_count || 0;\n          this.unvalidatedCount = data.unvalidated_count || 0;\n          console.log(`Loaded validation counts: ${this.validatedCount} validated, ${this.unvalidatedCount} unvalidated`);\n        } else {\n          console.error('Failed to load validation counts:', data.message);\n          // Use sample data for development\n          this.validatedCount = 125;\n          this.unvalidatedCount = 37;\n        }\n      } catch (error) {\n        console.error('Error loading validation counts:', error);\n        // Use sample data for development\n        this.validatedCount = 125;\n        this.unvalidatedCount = 37;\n      }\n    },\n\n    // Load Action Tracker Alerts data\n    loadActionTrackerAlerts() {\n      console.log(`Loading Action Tracker Alerts for PQE owner: ${this.pqeOwner}`);\n\n      // Generate sample data for demonstration\n      // In a real implementation, this would fetch from the action tracker API\n      this.inProgressAlerts = [\n        {\n          id: 'ip-1',\n          category: 'Thermal Management',\n          severity: 'High',\n          xFactor: '2.1',\n          status: 'In-Progress'\n        },\n        {\n          id: 'ip-2',\n          category: 'Power Delivery',\n          severity: 'Medium',\n          xFactor: '1.8',\n          status: 'In-Progress'\n        }\n      ];\n\n      this.outstandingAlerts = [\n        {\n          id: 'out-1',\n          category: 'Signal Integrity',\n          severity: 'Medium',\n          xFactor: '1.6',\n          status: 'Outstanding'\n        }\n      ];\n\n      this.resolvedAlerts = [\n        {\n          id: 'res-1',\n          category: 'Manufacturing Defect',\n          severity: 'High',\n          xFactor: '0.8',\n          status: 'Resolved'\n        },\n        {\n          id: 'res-2',\n          category: 'Component Quality',\n          severity: 'Medium',\n          xFactor: '0.9',\n          status: 'Resolved'\n        }\n      ];\n\n      console.log(`Loaded ${this.totalActionTrackerAlerts} Action Tracker Alerts`);\n    },\n\n    toggleCriticalIssuesExpanded() {\n      this.isCriticalIssuesExpanded = !this.isCriticalIssuesExpanded;\n    },\n\n    toggleResolvedIssuesExpanded() {\n      this.isResolvedIssuesExpanded = !this.isResolvedIssuesExpanded;\n    },\n\n    toggleOutstandingIssuesExpanded() {\n      this.isOutstandingIssuesExpanded = !this.isOutstandingIssuesExpanded;\n    },\n\n    toggleIssueExpanded(issue) {\n      const issueId = issue.id;\n      const index = this.expandedIssueIds.indexOf(issueId);\n\n      if (index === -1) {\n        // Issue is not expanded, so expand it\n        this.expandedIssueIds.push(issueId);\n      } else {\n        // Issue is expanded, so collapse it\n        this.expandedIssueIds.splice(index, 1);\n      }\n    },\n\n    toggleResolvedIssueExpanded(issue) {\n      const issueId = issue.id;\n      const index = this.expandedResolvedIssueIds.indexOf(issueId);\n\n      if (index === -1) {\n        // Issue is not expanded, so expand it\n        this.expandedResolvedIssueIds.push(issueId);\n      } else {\n        // Issue is expanded, so collapse it\n        this.expandedResolvedIssueIds.splice(index, 1);\n      }\n    },\n\n    toggleOutstandingIssueExpanded(issue) {\n      const issueId = issue.id;\n      const index = this.expandedOutstandingIssueIds.indexOf(issueId);\n\n      if (index === -1) {\n        // Issue is not expanded, so expand it\n        this.expandedOutstandingIssueIds.push(issueId);\n      } else {\n        // Issue is expanded, so collapse it\n        this.expandedOutstandingIssueIds.splice(index, 1);\n      }\n    },\n\n    isIssueExpanded(issueId) {\n      return this.expandedIssueIds.includes(issueId);\n    },\n\n    isResolvedIssueExpanded(issueId) {\n      return this.expandedResolvedIssueIds.includes(issueId);\n    },\n\n    isOutstandingIssueExpanded(issueId) {\n      return this.expandedOutstandingIssueIds.includes(issueId);\n    },\n\n    markIssueAsResolved(issue) {\n      console.log('Mark issue as resolved:', issue);\n\n      // In a real implementation, this would call an API to update the issue status\n      // For now, we'll just move the issue from unresolved to resolved\n\n      // Create a resolved issue object with additional fields\n      const resolvedIssue = {\n        ...issue,\n        resolutionDate: new Date().toISOString().split('T')[0], // Today's date in YYYY-MM-DD format\n        currentPerformance: '1.0x' // Initial performance after resolution\n      };\n\n      // Add to resolved issues\n      this.resolvedIssues.push(resolvedIssue);\n\n      // Remove from unresolved issues\n      const index = this.unresolvedCriticalIssues.findIndex(i => i.id === issue.id);\n      if (index !== -1) {\n        this.unresolvedCriticalIssues.splice(index, 1);\n      }\n\n      // Show success message\n      alert(`Issue marked as resolved: ${issue.category}`);\n    },\n\n    markIssueAsOutstanding(issue) {\n      console.log('Mark issue as outstanding:', issue);\n\n      // In a real implementation, this would call an API to update the issue status\n      // For now, we'll just move the issue from unresolved to outstanding\n\n      // Create an outstanding issue object with additional fields\n      const outstandingIssue = {\n        ...issue,\n        acceptanceDate: new Date().toISOString().split('T')[0], // Today's date in YYYY-MM-DD format\n        currentPerformance: issue.increaseMultiplier, // Initial performance is the same as the issue multiplier\n        acceptedBy: 'Engineering Team' // Default value\n      };\n\n      // Add to outstanding issues\n      this.outstandingIssues.push(outstandingIssue);\n\n      // Remove from unresolved issues\n      const index = this.unresolvedCriticalIssues.findIndex(i => i.id === issue.id);\n      if (index !== -1) {\n        this.unresolvedCriticalIssues.splice(index, 1);\n      }\n\n      // Show success message\n      alert(`Issue marked as outstanding: ${issue.category}`);\n    },\n\n    handleSeverityFilterChange() {\n      // Update the selected filters based on the severity dropdown\n      if (this.severityFilter === 'all') {\n        this.selectedFilters.severity = [];\n      } else {\n        this.selectedFilters.severity = [this.severityFilter];\n      }\n    },\n\n    handleAnalysisFilterChange() {\n      // Update the selected filters based on the analysis type dropdown\n      if (this.analysisTypeFilter === 'all') {\n        this.selectedFilters.analysisType = [];\n      } else {\n        this.selectedFilters.analysisType = [this.analysisTypeFilter];\n      }\n    },\n\n    clearFilters() {\n      this.selectedFilters.severity = [];\n      this.selectedFilters.analysisType = [];\n      this.severityFilter = 'all';\n      this.analysisTypeFilter = 'all';\n    },\n\n    handleResolvedCategoryFilterChange() {\n      // Update the selected filters based on the category dropdown\n      if (this.resolvedCategoryFilter === 'all') {\n        this.resolvedFilters.category = [];\n      } else {\n        this.resolvedFilters.category = [this.resolvedCategoryFilter];\n      }\n    },\n\n    handleResolvedAnalysisFilterChange() {\n      // Update the selected filters based on the analysis type dropdown\n      if (this.resolvedAnalysisTypeFilter === 'all') {\n        this.resolvedFilters.analysisType = [];\n      } else {\n        this.resolvedFilters.analysisType = [this.resolvedAnalysisTypeFilter];\n      }\n    },\n\n    clearResolvedFilters() {\n      this.resolvedFilters.category = [];\n      this.resolvedFilters.analysisType = [];\n      this.resolvedCategoryFilter = 'all';\n      this.resolvedAnalysisTypeFilter = 'all';\n    },\n\n    handleOutstandingCategoryFilterChange() {\n      // Update the selected filters based on the category dropdown\n      if (this.outstandingCategoryFilter === 'all') {\n        this.outstandingFilters.category = [];\n      } else {\n        this.outstandingFilters.category = [this.outstandingCategoryFilter];\n      }\n    },\n\n    handleOutstandingAnalysisFilterChange() {\n      // Update the selected filters based on the analysis type dropdown\n      if (this.outstandingAnalysisTypeFilter === 'all') {\n        this.outstandingFilters.analysisType = [];\n      } else {\n        this.outstandingFilters.analysisType = [this.outstandingAnalysisTypeFilter];\n      }\n    },\n\n    clearOutstandingFilters() {\n      this.outstandingFilters.category = [];\n      this.outstandingFilters.analysisType = [];\n      this.outstandingCategoryFilter = 'all';\n      this.outstandingAnalysisTypeFilter = 'all';\n    },\n\n    updateIssue(issue, markAsResolved = false, markAsOutstanding = false) {\n      console.log('Update issue:', issue, 'Mark as resolved:', markAsResolved, 'Mark as outstanding:', markAsOutstanding);\n\n      // Emit event to update action tracker\n      this.$emit('update-action-tracker', {\n        issueId: issue.id,\n        category: issue.category,\n        comment: issue.comment,\n        severity: issue.severity,\n        pqeOwner: this.pqeOwner,\n        month: issue.month,\n        analysisType: issue.analysisType,\n        resolved: markAsResolved,\n        outstanding: markAsOutstanding\n      });\n\n      // If marking as resolved, move the issue to resolved issues\n      if (markAsResolved) {\n        this.markIssueAsResolved(issue);\n      }\n      // If marking as outstanding, move the issue to outstanding issues\n      else if (markAsOutstanding) {\n        this.markIssueAsOutstanding(issue);\n      }\n      else {\n        // Show success message for regular update\n        alert(`Action tracker updated for issue: ${issue.category}`);\n      }\n    },\n\n    viewPerformanceData(issue) {\n      console.log('View performance data for:', issue.category);\n\n      // In a real implementation, this would show a modal or chart with performance data\n      // For now, we'll just show an alert with the data\n\n      const performanceData = this.performanceData[issue.category];\n      if (performanceData && performanceData.length > 0) {\n        const performanceText = performanceData\n          .map(data => `${data.month}: ${data.xFactor}x`)\n          .join('\\n');\n\n        alert(`Performance data for ${issue.category}:\\n${performanceText}`);\n      } else {\n        alert(`No performance data available for ${issue.category}`);\n      }\n    },\n\n    viewIssueDetails(issue) {\n      console.log('View issue details for:', issue.category);\n      // In a real implementation, this would show a modal or navigate to a detailed view\n      alert(`Viewing details for issue: ${issue.category}\\nMonth: ${issue.month}\\nSeverity: ${issue.severity}\\nMultiplier: ${issue.increaseMultiplier}x`);\n    },\n\n    // Root Cause Chart methods\n    async loadRootCauseData() {\n      console.log('Loading root cause chart data for PQE Owner Dashboard');\n      this.isRootCauseDataLoading = true;\n\n      try {\n        // Get authentication config\n        const config = this.getAuthConfig();\n\n        // Calculate date range based on selected time range\n        const endDate = new Date();\n        const startDate = new Date();\n        const monthsToFetch = this.rootCauseTimeRange === '3month' ? 2 : 5; // 3 or 6 months including current\n        startDate.setMonth(endDate.getMonth() - monthsToFetch);\n\n        const startDateStr = `${startDate.getFullYear()}-${String(startDate.getMonth() + 1).padStart(2, '0')}`;\n        const endDateStr = `${endDate.getFullYear()}-${String(endDate.getMonth() + 1).padStart(2, '0')}`;\n\n        console.log(`Fetching root cause data for ${this.pqeOwner} from ${startDateStr} to ${endDateStr}, Group: ${this.rootCauseSelectedGroup}`);\n\n        // Call the new API endpoint\n        const response = await fetch('/api-statit2/get_pqe_root_cause_analysis', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({\n            pqeOwner: this.pqeOwner,\n            startDate: startDateStr,\n            endDate: endDateStr,\n            selectedGroup: this.rootCauseSelectedGroup === 'all' ? null : this.rootCauseSelectedGroup\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch root cause data: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success') {\n          // Transform the API data to chart format\n          this.rootCauseChartData = this.transformRootCauseData(data.categoryData);\n          console.log('Root cause chart data loaded:', data.categoryData);\n          console.log('Chart data length:', this.rootCauseChartData.length);\n          console.log('Sample data point:', this.rootCauseChartData[0]);\n          console.log('Breakout groups:', data.breakoutGroups);\n          console.log('Part numbers:', data.partNumbers);\n\n          console.log('Chart data loaded, watcher should handle update');\n        } else {\n          console.error('Failed to load root cause data:', data.sql_error_msg || data.message);\n          // Fall back to mock data\n          this.rootCauseChartData = this.generateMockRootCauseData();\n          console.log('Using mock data:', this.rootCauseChartData.length);\n        }\n      } catch (error) {\n        console.error('Error loading root cause data:', error);\n        // Fall back to mock data\n        this.rootCauseChartData = this.generateMockRootCauseData();\n      } finally {\n        this.isRootCauseDataLoading = false;\n      }\n    },\n\n    transformRootCauseData(categoryData) {\n      // Transform the API response into chart format\n      const chartData = [];\n\n      // categoryData structure: { \"category\": { \"2024-03\": { defects: 5, volume: 1000, failRate: 0.5 }, ... }, ... }\n      for (const [category, monthData] of Object.entries(categoryData)) {\n        // Clean up category name (trim whitespace)\n        const cleanCategory = category.trim();\n        console.log(\"trim cat\", cleanCategory)\n\n        for (const [month, data] of Object.entries(monthData)) {\n          chartData.push({\n            group: cleanCategory,\n            key: month,\n            value: parseFloat(data.failRate.toFixed(2))\n          });\n        }\n      }\n\n      console.log('Transformed root cause data:', chartData);\n      console.log('Unique groups in data:', [...new Set(chartData.map(d => d.group))]);\n      console.log('Unique keys in data:', [...new Set(chartData.map(d => d.key))]);\n      console.log('Value range:', Math.min(...chartData.map(d => d.value)), 'to', Math.max(...chartData.map(d => d.value)));\n      return chartData;\n    },\n\n    generateMockRootCauseData() {\n      // Use the actual breakout groups for this PQE owner instead of generic categories\n      let categories = [];\n\n      if (this.breakoutGroups && this.breakoutGroups.length > 0) {\n        // Use the actual breakout groups for this PQE owner\n        categories = this.breakoutGroups.map(group => group.name);\n\n        // If a specific group is selected, filter to only that group\n        if (this.rootCauseSelectedGroup !== 'all') {\n          categories = categories.filter(category => category === this.rootCauseSelectedGroup);\n        }\n      } else {\n        // Fallback to sample data if breakout groups aren't loaded yet\n        if (this.pqeOwner === 'Albert G.') {\n          categories = ['Fan Themis', 'Victoria Crypto', 'Quantum Nexus'];\n        } else if (this.pqeOwner === 'Sarah L.') {\n          categories = ['Stellar Core', 'Nebula Drive'];\n        } else {\n          categories = ['Sample Group 1', 'Sample Group 2'];\n        }\n\n        // If a specific group is selected, filter to only that group\n        if (this.rootCauseSelectedGroup !== 'all') {\n          categories = categories.filter(category => category === this.rootCauseSelectedGroup);\n        }\n      }\n\n      // Generate months based on selected time range\n      const now = new Date();\n      const months = [];\n      const monthsToGenerate = this.rootCauseTimeRange === '3month' ? 3 : 6;\n\n      for (let i = monthsToGenerate - 1; i >= 0; i--) {\n        const date = new Date(now.getFullYear(), now.getMonth() - i, 1);\n        const year = date.getFullYear();\n        const month = String(date.getMonth() + 1).padStart(2, '0');\n        months.push(`${year}-${month}`);\n      }\n\n      const data = [];\n\n      months.forEach(month => {\n        categories.forEach(category => {\n          // Generate realistic fail rate data (0-5% range)\n          const baseRate = Math.random() * 3 + 0.5; // 0.5% to 3.5%\n          const variation = (Math.random() - 0.5) * 1; // ±0.5%\n          const failRate = Math.max(0.1, baseRate + variation);\n\n          data.push({\n            group: category,\n            key: month,\n            value: parseFloat(failRate.toFixed(2))\n          });\n        });\n      });\n\n      return data;\n    },\n\n    handleRootCauseBarClick(event) {\n      if (event && event.data) {\n        const clickedData = event.data;\n        console.log(`Root cause bar clicked for category: ${clickedData.group}, month: ${clickedData.key}`);\n\n        // You can implement additional functionality here, such as showing more details\n        // or navigating to the MetisXFactors Group tab with this specific category\n        alert(`Clicked on ${clickedData.group} for ${clickedData.key}\\nFail Rate: ${clickedData.value}%`);\n      }\n    },\n\n    handleRootCauseTimeRangeChange() {\n      console.log(`Root cause time range changed to: ${this.rootCauseTimeRange}`);\n      this.loadRootCauseData();\n    },\n\n    handleRootCauseViewByChange() {\n      console.log(`Root cause time range changed to: ${this.rootCauseTimeRange}`);\n      this.loadRootCauseData();\n    },\n\n    handleRootCauseGroupChange() {\n      console.log(`Root cause group changed to: ${this.rootCauseSelectedGroup}`);\n      console.log('Current chart data length before reload:', this.rootCauseChartData.length);\n      this.loadRootCauseData();\n    },\n\n    getAuthConfig() {\n      // Get authentication token from localStorage or Vuex store\n      const token = localStorage.getItem('token') || this.$store.state.token;\n\n      return {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      };\n    },\n\n    // Navigate to Action Tracker\n    goToActionTracker() {\n      console.log('Navigating to Action Tracker...');\n      this.$router.push('/action-tracker');\n    },\n\n    // Navigate to Validation Page\n    goToValidationPage() {\n      console.log('Navigating to Validation Page...');\n      this.$router.push('/defect-validations');\n    },\n\n    // Navigate to Heatmap tab in Metis XFactors\n    goToHeatmap() {\n      console.log('Navigating to Metis XFactors Heatmap...');\n      this.$router.push('/metis-xfactors?tab=heatmap');\n    },\n\n    // Scroll to alerts section (action tracker alerts section)\n    scrollToAlerts() {\n      console.log('Scrolling to action tracker alerts section...');\n      // Scroll to the Action Tracker Alerts section\n      this.$nextTick(() => {\n        const actionTrackerSection = document.querySelector('.section-card:first-child'); // First section card (Action Tracker Alerts)\n        if (actionTrackerSection) {\n          actionTrackerSection.scrollIntoView({\n            behavior: 'smooth',\n            block: 'start'\n          });\n\n          // Add a brief highlight effect\n          actionTrackerSection.style.transition = 'box-shadow 0.3s ease';\n          actionTrackerSection.style.boxShadow = '0 0 20px rgba(0, 98, 255, 0.5)';\n\n          setTimeout(() => {\n            actionTrackerSection.style.boxShadow = '0 2px 6px rgba(0, 0, 0, 0.2)';\n          }, 2000);\n        }\n      });\n    },\n\n    // Toggle Action Tracker Alerts section\n    toggleActionTrackerAlertsExpanded() {\n      this.isActionTrackerAlertsExpanded = !this.isActionTrackerAlertsExpanded;\n      console.log('Action Tracker Alerts expanded:', this.isActionTrackerAlertsExpanded);\n    },\n\n    // Toggle In-Progress Alerts subsection\n    toggleInProgressAlertsExpanded() {\n      this.isInProgressAlertsExpanded = !this.isInProgressAlertsExpanded;\n      console.log('In-Progress Alerts expanded:', this.isInProgressAlertsExpanded);\n    },\n\n    // Toggle Outstanding Alerts subsection\n    toggleOutstandingAlertsExpanded() {\n      this.isOutstandingAlertsExpanded = !this.isOutstandingAlertsExpanded;\n      console.log('Outstanding Alerts expanded:', this.isOutstandingAlertsExpanded);\n    },\n\n    // Toggle Resolved Alerts subsection\n    toggleResolvedAlertsExpanded() {\n      this.isResolvedAlertsExpanded = !this.isResolvedAlertsExpanded;\n      console.log('Resolved Alerts expanded:', this.isResolvedAlertsExpanded);\n    },\n\n    // View Action Tracker Alert - opens modal with New Alerts tab\n    async viewActionTrackerAlert(alert) {\n      console.log('Viewing action tracker alert:', alert);\n      this.selectedAlert = alert;\n\n      // Load alert history and AI insight\n      await this.loadAlertHistory(alert);\n      await this.loadAiInsight(alert);\n\n      this.showActionTrackerAlertModal = true;\n    },\n\n    // Load alert history for the selected alert\n    async loadAlertHistory(alert) {\n      try {\n        const response = await fetch('/api-statit2/get_pqe_alert_history', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...this.getAuthConfig().headers\n          },\n          body: JSON.stringify({\n            actionTrackerId: alert.id,\n            pqeOwner: this.pqeOwner\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch alert history: ${response.status}`);\n        }\n\n        const data = await response.json();\n        if (data.status_res === 'success') {\n          this.alertHistory = data.alert_history || [];\n        } else {\n          console.error('Failed to load alert history:', data.message);\n          this.alertHistory = [];\n        }\n      } catch (error) {\n        console.error('Error loading alert history:', error);\n        this.alertHistory = [];\n      }\n    },\n\n    // Load AI insight for the selected alert\n    async loadAiInsight(alert) {\n      this.isLoadingAiInsight = true;\n      try {\n        const response = await fetch('/api-statit2/get_pqe_alert_ai_insight', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...this.getAuthConfig().headers\n          },\n          body: JSON.stringify({\n            actionTrackerId: alert.id,\n            alertData: alert,\n            pqeOwner: this.pqeOwner\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch AI insight: ${response.status}`);\n        }\n\n        const data = await response.json();\n        if (data.status_res === 'success') {\n          this.aiInsight = data.ai_insight || 'No insight available';\n        } else {\n          console.error('Failed to load AI insight:', data.message);\n          this.aiInsight = 'Unable to generate insight at this time';\n        }\n      } catch (error) {\n        console.error('Error loading AI insight:', error);\n        this.aiInsight = 'Unable to generate insight at this time';\n      } finally {\n        this.isLoadingAiInsight = false;\n      }\n    },\n\n    // Add alert update\n    async addAlertUpdate() {\n      if (!this.newAlertUpdate.trim()) {\n        alert('Please enter an update');\n        return;\n      }\n\n      try {\n        const response = await fetch('/api-statit2/add_pqe_alert_update', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...this.getAuthConfig().headers\n          },\n          body: JSON.stringify({\n            actionTrackerId: this.selectedAlert.id,\n            update: this.newAlertUpdate,\n            updatedBy: this.pqeOwner,\n            alertType: 'PQE Update'\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to add alert update: ${response.status}`);\n        }\n\n        const data = await response.json();\n        if (data.status_res === 'success') {\n          // Add the new alert to the history\n          this.alertHistory.push(data.alert);\n          this.newAlertUpdate = '';\n          console.log('Alert update added successfully');\n        } else {\n          throw new Error(data.message || 'Failed to add alert update');\n        }\n      } catch (error) {\n        console.error('Error adding alert update:', error);\n        alert('Failed to add alert update: ' + error.message);\n      }\n    },\n\n    // Add critical issue to action tracker\n    async addCriticalIssueToActionTracker(issue) {\n      console.log('Adding critical issue to action tracker:', issue);\n\n      try {\n        const response = await fetch('/api-statit2/update_pqe_action', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...this.getAuthConfig().headers\n          },\n          body: JSON.stringify({\n            issueId: issue.id,\n            category: issue.category,\n            comment: issue.comment || `Critical issue: ${issue.aiDescription}`,\n            severity: issue.severity,\n            pqeOwner: this.pqeOwner,\n            month: issue.month,\n            analysisType: issue.analysisType\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to add to action tracker: ${response.status}`);\n        }\n\n        const data = await response.json();\n        if (data.status_res === 'success') {\n          alert(`Critical issue \"${issue.category}\" has been added to the action tracker with ID: ${data.actionId}`);\n        } else {\n          throw new Error(data.message || 'Failed to add to action tracker');\n        }\n      } catch (error) {\n        console.error('Error adding critical issue to action tracker:', error);\n        alert('Failed to add critical issue to action tracker: ' + error.message);\n      }\n    },\n\n    // Close action tracker alert modal\n    closeActionTrackerAlertModal() {\n      this.showActionTrackerAlertModal = false;\n      this.selectedAlert = null;\n      this.alertHistory = [];\n      this.newAlertUpdate = '';\n      this.aiInsight = '';\n    }\n  }\n};\n</script>\n\n<style scoped>\n.pqe-owner-dashboard-container {\n  color: #f4f4f4;\n}\n\n.content-wrapper {\n  padding: 1rem;\n}\n\n.dashboard-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1.5rem;\n}\n\n.dashboard-header h3 {\n  margin: 0;\n  font-size: 1.5rem;\n  font-weight: 400;\n}\n\n.last-updated-text {\n  font-size: 0.875rem;\n  color: #8d8d8d;\n}\n\n.key-metrics-section {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1rem;\n  margin-bottom: 1.5rem;\n}\n\n.metric-card {\n  background-color: #262626;\n  border-radius: 8px;\n  padding: 1.25rem;\n  display: flex;\n  align-items: center;\n  border: 1px solid #333333;\n  transition: transform 0.2s ease, box-shadow 0.2s ease;\n}\n\n.metric-card.clickable {\n  cursor: pointer;\n\n  &:hover {\n    transform: translateY(-3px);\n    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);\n    background-color: #353535; /* Darker gray on hover */\n  }\n\n  &:active {\n    transform: translateY(-1px);\n    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);\n  }\n}\n\n.metric-icon {\n  margin-right: 1rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 48px;\n  height: 48px;\n  border-radius: 50%;\n}\n\n.metric-icon.alerts {\n  background-color: rgba(250, 77, 86, 0.1);\n  color: #fa4d56;\n}\n\n.metric-icon.in-progress {\n  background-color: rgba(255, 131, 43, 0.1);\n  color: #ff832b;\n}\n\n.metric-icon.validated {\n  background-color: rgba(36, 161, 72, 0.1);\n  color: #24a148;\n}\n\n.metric-icon.new-issues {\n  background-color: rgba(15, 98, 254, 0.1);\n  color: #0f62fe;\n}\n\n.metric-icon.critical-issues {\n  background-color: rgba(250, 77, 86, 0.1);\n  color: #fa4d56;\n}\n\n.metric-icon.unvalidated {\n  background-color: rgba(255, 131, 43, 0.1);\n  color: #ff832b;\n}\n\n.metric-icon.groups-over-target {\n  background-color: rgba(138, 63, 252, 0.1);\n  color: #8a3ffc;\n}\n\n/* Action Tracker Alerts Styles */\n.alert-subsection {\n  margin-bottom: 1.5rem;\n  border: 1px solid #393939;\n  border-radius: 8px;\n  background-color: #2a2a2a;\n}\n\n.subsection-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 1rem 1.25rem;\n  cursor: pointer;\n  border-bottom: 1px solid #393939;\n  transition: background-color 0.2s ease;\n}\n\n.subsection-header:hover {\n  background-color: #333333;\n}\n\n.subsection-title {\n  color: #f4f4f4;\n  font-size: 1rem;\n  font-weight: 500;\n  margin: 0;\n}\n\n.subsection-controls {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n}\n\n.subsection-content {\n  padding: 1rem 1.25rem;\n}\n\n.action-tracker-alert {\n  background-color: #333333;\n  border: 1px solid #525252;\n  border-radius: 6px;\n  margin-bottom: 0.75rem;\n  transition: all 0.2s ease;\n}\n\n.action-tracker-alert:hover {\n  background-color: #393939;\n  border-color: #6f6f6f;\n}\n\n.action-tracker-alert .issue-header {\n  padding: 1rem;\n}\n\n.action-tracker-alert .issue-tags {\n  display: flex;\n  gap: 0.5rem;\n  margin-bottom: 0.5rem;\n}\n\n.action-tracker-alert .issue-title {\n  color: #f4f4f4;\n  font-size: 1rem;\n  font-weight: 500;\n  margin-bottom: 0.5rem;\n  display: block;\n}\n\n.action-tracker-alert .issue-metadata {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n}\n\n.action-tracker-alert .issue-multiplier {\n  font-size: 0.875rem;\n  font-weight: 600;\n  padding: 0.25rem 0.5rem;\n  border-radius: 4px;\n}\n\n.action-tracker-alert .issue-multiplier.medium-severity {\n  background-color: rgba(255, 131, 43, 0.2);\n  color: #ff832b;\n}\n\n.action-tracker-alert .issue-multiplier.good-performance {\n  background-color: rgba(36, 161, 72, 0.2);\n  color: #24a148;\n}\n\n.no-data-message {\n  text-align: center;\n  color: #8d8d8d;\n  font-style: italic;\n  padding: 2rem;\n}\n\n.metric-content {\n  display: flex;\n  flex-direction: column;\n}\n\n.metric-label {\n  font-size: 0.875rem;\n  color: #8d8d8d;\n  margin-bottom: 0.25rem;\n}\n\n.metric-value {\n  font-size: 1.5rem;\n  font-weight: 600;\n  margin-bottom: 0.25rem;\n}\n\n.metric-description {\n  font-size: 0.75rem;\n  color: #8d8d8d;\n}\n\n.chart-section {\n  margin-bottom: 1.5rem;\n}\n\n.chart-controls {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 1rem;\n  padding: 1rem;\n  background-color: #262626;\n  border-bottom: 1px solid #333333;\n}\n\n.control-group {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.control-label {\n  color: #8d8d8d;\n  font-size: 0.875rem;\n  white-space: nowrap;\n}\n\n.control-dropdown {\n  width: 200px;\n}\n\n.chart-container {\n  height: 500px;\n  padding: 1rem;\n  background-color: #161616;\n  border-radius: 8px;\n  margin: 1rem;\n}\n\n.section-footer {\n  padding: 0 1.25rem 1.25rem;\n}\n\n.section-description {\n  margin: 0;\n  font-size: 0.875rem;\n  color: #8d8d8d;\n  line-height: 1.4;\n}\n\n.dashboard-main-content {\n  display: grid;\n  grid-template-columns: 1fr;\n  gap: 1.5rem;\n}\n\n.dashboard-column {\n  display: flex;\n  flex-direction: column;\n  gap: 1.5rem;\n}\n\n.section-card {\n  background-color: #262626;\n  border-radius: 8px;\n  overflow: hidden;\n  border: 1px solid #333333;\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 1.25rem;\n  cursor: pointer;\n  transition: background-color 0.2s ease;\n}\n\n.section-header:hover {\n  background-color: #333333;\n}\n\n.section-title-container {\n  display: flex;\n  flex-direction: column;\n}\n\n.section-title {\n  margin: 0;\n  font-size: 1.25rem;\n  font-weight: 400;\n}\n\n.section-subtitle {\n  font-size: 0.875rem;\n  color: #8d8d8d;\n  margin-top: 0.25rem;\n}\n\n.section-controls {\n  display: flex;\n  align-items: center;\n}\n\n.status-indicator {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 24px;\n  height: 24px;\n  border-radius: 50%;\n  background-color: #fa4d56;\n  color: #ffffff;\n  font-size: 0.75rem;\n  font-weight: 600;\n  margin-right: 0.75rem;\n}\n\n.status-indicator.flashing {\n  animation: flash 2s infinite;\n}\n\n.status-indicator.resolved-indicator {\n  background-color: #24a148;\n}\n\n.status-indicator.outstanding-indicator {\n  background-color: #0f62fe;\n}\n\n@keyframes flash {\n  0%, 100% { opacity: 1; }\n  50% { opacity: 0.5; }\n}\n\n.expand-indicator {\n  transition: transform 0.2s ease;\n}\n\n.expand-indicator.expanded {\n  transform: rotate(180deg);\n}\n\n.section-content {\n  padding: 0 1.25rem 1.25rem;\n}\n\n.filter-container {\n  margin-bottom: 1.5rem;\n  background-color: #333333;\n  padding: 1rem;\n  border-radius: 8px;\n}\n\n.filter-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1rem;\n}\n\n.filter-title {\n  margin: 0;\n  font-size: 1rem;\n  font-weight: 400;\n}\n\n.filter-controls {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 1rem;\n}\n\n.filter-group {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.filter-label {\n  color: #8d8d8d;\n  font-size: 0.875rem;\n  white-space: nowrap;\n}\n\n.filter-dropdown {\n  width: 200px;\n}\n\n.issues-list {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n\n.issue-card {\n  background-color: #333333;\n  border-radius: 8px;\n  overflow: hidden;\n  transition: transform 0.2s ease, box-shadow 0.2s ease;\n  cursor: pointer;\n}\n\n.issue-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);\n}\n\n.issue-header {\n  display: flex;\n  align-items: center;\n  padding: 1rem;\n  position: relative;\n}\n\n.issue-tags {\n  display: flex;\n  gap: 0.5rem;\n  margin-right: 1rem;\n}\n\n.issue-title {\n  flex-grow: 1;\n  font-weight: 500;\n}\n\n.issue-metadata {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  margin-right: 1rem;\n}\n\n.issue-multiplier {\n  font-weight: 600;\n  padding: 0.125rem 0.375rem;\n  border-radius: 4px;\n}\n\n.issue-multiplier.high-severity {\n  background-color: rgba(250, 77, 86, 0.1);\n  color: #fa4d56;\n}\n\n.issue-multiplier.medium-severity {\n  background-color: rgba(255, 131, 43, 0.1);\n  color: #ff832b;\n}\n\n.issue-multiplier.good-performance {\n  background-color: rgba(36, 161, 72, 0.1);\n  color: #24a148;\n}\n\n.issue-multiplier.medium-performance {\n  background-color: rgba(255, 131, 43, 0.1);\n  color: #ff832b;\n}\n\n.resolved-issue-card {\n  border-left: 4px solid #24a148;\n}\n\n.outstanding-issue-card {\n  border-left: 4px solid #0f62fe;\n}\n\n.resolution-details, .acceptance-details {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 1rem;\n  font-size: 0.875rem;\n  color: #c6c6c6;\n}\n\n.issue-multiplier.low-performance {\n  background-color: rgba(36, 161, 72, 0.1);\n  color: #24a148;\n}\n\n.resolution-comment, .acceptance-comment {\n  margin: 1rem 0;\n  padding: 0.75rem;\n  background-color: #333333;\n  border-radius: 4px;\n}\n\n.comment-label {\n  font-weight: 600;\n  margin-bottom: 0.5rem;\n  color: #c6c6c6;\n}\n\n.comment-text {\n  font-size: 0.875rem;\n  white-space: pre-wrap;\n}\n\n.issue-content {\n  padding: 0 1rem 1rem;\n  border-top: 1px solid #444444;\n}\n\n.ai-description {\n  margin-bottom: 1rem;\n  padding: 1rem;\n  background-color: #262626;\n  border-radius: 8px;\n  font-size: 0.875rem;\n  line-height: 1.5;\n}\n\n.action-comment {\n  margin-bottom: 1rem;\n}\n\n.issue-actions {\n  display: flex;\n  justify-content: flex-end;\n  gap: 0.5rem;\n}\n\n.no-data-message {\n  color: #8d8d8d;\n  font-size: 1rem;\n  text-align: center;\n  padding: 2rem;\n}\n\n@media (max-width: 768px) {\n  .key-metrics-section {\n    grid-template-columns: 1fr;\n  }\n\n  .dashboard-main-content {\n    grid-template-columns: 1fr;\n  }\n\n  .filter-controls {\n    flex-direction: column;\n  }\n\n  .filter-group {\n    width: 100%;\n  }\n}\n\n/* Action Tracker Alert Modal Styles */\n.action-tracker-alert-modal .cv-modal-container {\n  max-width: 90vw;\n  width: 800px;\n}\n\n.modal-content {\n  padding: 1rem;\n  color: #f4f4f4;\n}\n\n.modal-section {\n  margin-bottom: 2rem;\n  background-color: #333333;\n  border-radius: 8px;\n  padding: 1.5rem;\n  border: 1px solid #444444;\n}\n\n.section-title {\n  color: #f4f4f4;\n  font-size: 1.125rem;\n  font-weight: 600;\n  margin: 0 0 1rem 0;\n  border-bottom: 1px solid #444444;\n  padding-bottom: 0.5rem;\n}\n\n.alert-info-grid {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 1rem;\n}\n\n.info-item {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.info-label {\n  font-weight: 600;\n  color: #c6c6c6;\n  min-width: 80px;\n}\n\n.info-value {\n  color: #f4f4f4;\n}\n\n.ai-insight-content {\n  background-color: #262626;\n  border-radius: 6px;\n  padding: 1rem;\n  border: 1px solid #525252;\n  font-size: 0.875rem;\n  line-height: 1.5;\n  color: #f4f4f4;\n}\n\n.loading-message {\n  text-align: center;\n  color: #8d8d8d;\n  font-style: italic;\n  padding: 1rem;\n}\n\n.add-update-form {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n\n.update-form-actions {\n  display: flex;\n  justify-content: flex-end;\n}\n\n.alert-history-table {\n  background-color: #262626;\n  border-radius: 6px;\n  overflow: hidden;\n  border: 1px solid #525252;\n}\n\n.history-header {\n  display: grid;\n  grid-template-columns: 120px 1fr 150px;\n  background-color: #393939;\n  border-bottom: 1px solid #525252;\n}\n\n.history-column {\n  padding: 0.75rem;\n  font-weight: 600;\n  color: #f4f4f4;\n  border-right: 1px solid #525252;\n}\n\n.history-column:last-child {\n  border-right: none;\n}\n\n.history-row {\n  display: grid;\n  grid-template-columns: 120px 1fr 150px;\n  border-bottom: 1px solid #525252;\n}\n\n.history-row:last-child {\n  border-bottom: none;\n}\n\n.history-cell {\n  padding: 0.75rem;\n  color: #f4f4f4;\n  border-right: 1px solid #525252;\n  font-size: 0.875rem;\n}\n\n.history-cell:last-child {\n  border-right: none;\n}\n\n.no-history-message {\n  text-align: center;\n  color: #8d8d8d;\n  font-style: italic;\n  padding: 2rem;\n}\n\n.modal-actions {\n  display: flex;\n  justify-content: flex-end;\n  gap: 0.5rem;\n  padding-top: 1rem;\n  border-top: 1px solid #444444;\n}\n\n/* Issue actions styling */\n.issue-actions {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 0.5rem;\n  margin-top: 1rem;\n  padding-top: 1rem;\n  border-top: 1px solid #444444;\n}\n\n.issue-actions .cv-button {\n  flex: 0 0 auto;\n}\n\n@media (max-width: 768px) {\n  .action-tracker-alert-modal .cv-modal-container {\n    max-width: 95vw;\n    width: 95vw;\n  }\n\n  .alert-info-grid {\n    grid-template-columns: 1fr;\n  }\n\n  .history-header,\n  .history-row {\n    grid-template-columns: 1fr;\n  }\n\n  .history-column,\n  .history-cell {\n    border-right: none;\n    border-bottom: 1px solid #525252;\n  }\n\n  .issue-actions {\n    flex-direction: column;\n  }\n\n  .issue-actions .cv-button {\n    width: 100%;\n  }\n}\n</style>\n"]}]}