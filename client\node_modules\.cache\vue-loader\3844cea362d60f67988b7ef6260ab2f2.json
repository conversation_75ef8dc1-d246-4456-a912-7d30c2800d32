{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\PQEDashboard\\PQEOwnerDashboard.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\PQEDashboard\\PQEOwnerDashboard.vue", "mtime": 1748988380528}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmltcG9ydCB7CiAgQ3ZCdXR0b24sCiAgQ3ZUYWcsCiAgQ3ZUZXh0QXJlYSwKICBDdkRyb3Bkb3duLAogIEN2RHJvcGRvd25JdGVtLAogIEN2TW9kYWwsCiAgQ3ZUYWJzLAogIEN2VGFiLAogIEN2Q2hlY2tib3gsCiAgQ3ZEYXRhVGFibGUsCiAgQ3ZEYXRhVGFibGVSb3csCiAgQ3ZEYXRhVGFibGVDZWxsCn0gZnJvbSAnQGNhcmJvbi92dWUnOwoKaW1wb3J0IFJvb3RDYXVzZUNoYXJ0IGZyb20gJ0AvY29tcG9uZW50cy9Sb290Q2F1c2VDaGFydC9Sb290Q2F1c2VDaGFydCc7CmltcG9ydCBQZXJmb3JtYW5jZUNoYXJ0IGZyb20gJ0AvY29tcG9uZW50cy9QZXJmb3JtYW5jZUNoYXJ0L1BlcmZvcm1hbmNlQ2hhcnQudnVlJzsKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnUFFFT3duZXJEYXNoYm9hcmQnLAogIGNvbXBvbmVudHM6IHsKICAgIEN2QnV0dG9uLAogICAgQ3ZUYWcsCiAgICBDdlRleHRBcmVhLAogICAgQ3ZEcm9wZG93biwKICAgIEN2RHJvcGRvd25JdGVtLAogICAgQ3ZNb2RhbCwKICAgIEN2VGFicywKICAgIEN2VGFiLAogICAgQ3ZDaGVja2JveCwKICAgIEN2RGF0YVRhYmxlLAogICAgQ3ZEYXRhVGFibGVSb3csCiAgICBDdkRhdGFUYWJsZUNlbGwsCiAgICBSb290Q2F1c2VDaGFydCwKICAgIFBlcmZvcm1hbmNlQ2hhcnQKICB9LAogIHByb3BzOiB7CiAgICBwcWVPd25lcjogewogICAgICB0eXBlOiBTdHJpbmcsCiAgICAgIHJlcXVpcmVkOiB0cnVlCiAgICB9CiAgfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgLy8gQ3JpdGljYWwgSXNzdWVzIERhdGEKICAgICAgbmV3Q3JpdGljYWxJc3N1ZXM6IFtdLAogICAgICB1bnJlc29sdmVkQ3JpdGljYWxJc3N1ZXM6IFtdLAogICAgICBleHBhbmRlZElzc3VlSWRzOiBbXSwgLy8gVHJhY2sgd2hpY2ggaXNzdWVzIGFyZSBleHBhbmRlZAogICAgICBpc0NyaXRpY2FsSXNzdWVzRXhwYW5kZWQ6IGZhbHNlLCAvLyBUcmFjayBpZiBjcml0aWNhbCBpc3N1ZXMgc2VjdGlvbiBpcyBleHBhbmRlZAoKICAgICAgLy8gQWN0aW9uIFRyYWNrZXIgQWxlcnRzIERhdGEKICAgICAgaXNBY3Rpb25UcmFja2VyQWxlcnRzRXhwYW5kZWQ6IGZhbHNlLAogICAgICBpc0luUHJvZ3Jlc3NBbGVydHNFeHBhbmRlZDogZmFsc2UsCiAgICAgIGlzT3V0c3RhbmRpbmdBbGVydHNFeHBhbmRlZDogZmFsc2UsCiAgICAgIGlzUmVzb2x2ZWRBbGVydHNFeHBhbmRlZDogZmFsc2UsCgogICAgICAvLyBBY3Rpb24gVHJhY2tlciBBbGVydCBEYXRhCiAgICAgIGluUHJvZ3Jlc3NBbGVydHM6IFtdLAogICAgICBvdXRzdGFuZGluZ0FsZXJ0czogW10sCiAgICAgIHJlc29sdmVkQWxlcnRzOiBbXSwKCiAgICAgIC8vIEZpbHRlcmluZyBmb3IgQ3JpdGljYWwgSXNzdWVzCiAgICAgIHNlbGVjdGVkRmlsdGVyczogewogICAgICAgIHNldmVyaXR5OiBbXSwKICAgICAgICBhbmFseXNpc1R5cGU6IFtdCiAgICAgIH0sCiAgICAgIHNldmVyaXR5RmlsdGVyOiAnYWxsJywKICAgICAgYW5hbHlzaXNUeXBlRmlsdGVyOiAnYWxsJywKCiAgICAgIC8vIFZhbGlkYXRpb24gRGF0YQogICAgICB2YWxpZGF0ZWRDb3VudDogMCwKICAgICAgdW52YWxpZGF0ZWRDb3VudDogMCwKCiAgICAgIC8vIEJyZWFrb3V0IEdyb3VwcyBEYXRhCiAgICAgIGJyZWFrb3V0R3JvdXBzOiBbXSwKCiAgICAgIC8vIFJvb3QgQ2F1c2UgQ2hhcnQgRGF0YQogICAgICByb290Q2F1c2VDaGFydERhdGE6IFtdLAogICAgICBpc1Jvb3RDYXVzZURhdGFMb2FkaW5nOiB0cnVlLAoKICAgICAgLy8gUm9vdCBDYXVzZSBDaGFydCBDb250cm9scwogICAgICByb290Q2F1c2VWaWV3Qnk6ICdyb290Q2F1c2UnLAogICAgICByb290Q2F1c2VUaW1lUmFuZ2U6ICc2bW9udGgnLAogICAgICByb290Q2F1c2VTZWxlY3RlZEdyb3VwOiAnYWxsJywKCiAgICAgIC8vIExvYWRpbmcgU3RhdGVzCiAgICAgIGlzTG9hZGluZzogZmFsc2UsCgogICAgICAvLyBBY3Rpb24gVHJhY2tlciBNb2RhbCBEYXRhCiAgICAgIHRyYWNraW5nTW9kYWxWaXNpYmxlOiBmYWxzZSwKICAgICAgc2VsZWN0ZWRUcmFja2luZ0l0ZW06IG51bGwsCiAgICAgIGFsZXJ0VXBkYXRlczogW10sCiAgICAgIG5ld0FsZXJ0VXBkYXRlOiAnJywKICAgICAgYWlJbnNpZ2h0OiAnJywKICAgICAgaXNMb2FkaW5nQWlJbnNpZ2h0OiBmYWxzZSwKICAgICAgYWxlcnRIaXN0b3J5RGF0YTogW10sCiAgICAgIGFsZXJ0SGlzdG9yeUNvbHVtbnM6IFsnTW9udGgnLCAnWWVhcicsICdTdGF0dXMnLCAnQWN0dWFsIFJhdGUnLCAnVGFyZ2V0IFJhdGUnLCAnWC1GYWN0b3InLCAnVm9sdW1lJywgJ0RlZmVjdHMnLCAnTm90ZXMnXQogICAgfTsKICB9LAogIGNvbXB1dGVkOiB7CiAgICAvLyBGaWx0ZXJlZCBjcml0aWNhbCBpc3N1ZXMgYmFzZWQgb24gc2VsZWN0ZWQgZmlsdGVycwogICAgZmlsdGVyZWRDcml0aWNhbElzc3VlcygpIHsKICAgICAgLy8gSWYgbm8gZmlsdGVycyBhcmUgc2VsZWN0ZWQsIHJldHVybiBhbGwgaXNzdWVzCiAgICAgIGlmICh0aGlzLnNlbGVjdGVkRmlsdGVycy5zZXZlcml0eS5sZW5ndGggPT09IDAgJiYgdGhpcy5zZWxlY3RlZEZpbHRlcnMuYW5hbHlzaXNUeXBlLmxlbmd0aCA9PT0gMCkgewogICAgICAgIHJldHVybiB0aGlzLnVucmVzb2x2ZWRDcml0aWNhbElzc3VlczsKICAgICAgfQoKICAgICAgLy8gQXBwbHkgZmlsdGVycwogICAgICByZXR1cm4gdGhpcy51bnJlc29sdmVkQ3JpdGljYWxJc3N1ZXMuZmlsdGVyKGlzc3VlID0+IHsKICAgICAgICAvLyBDaGVjayBpZiBpc3N1ZSBwYXNzZXMgc2V2ZXJpdHkgZmlsdGVyCiAgICAgICAgY29uc3QgcGFzc2VzU2V2ZXJpdHlGaWx0ZXIgPSB0aGlzLnNlbGVjdGVkRmlsdGVycy5zZXZlcml0eS5sZW5ndGggPT09IDAgfHwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5zZWxlY3RlZEZpbHRlcnMuc2V2ZXJpdHkuaW5jbHVkZXMoaXNzdWUuc2V2ZXJpdHkpOwoKICAgICAgICAvLyBDaGVjayBpZiBpc3N1ZSBwYXNzZXMgYW5hbHlzaXMgdHlwZSBmaWx0ZXIKICAgICAgICBjb25zdCBwYXNzZXNBbmFseXNpc0ZpbHRlciA9IHRoaXMuc2VsZWN0ZWRGaWx0ZXJzLmFuYWx5c2lzVHlwZS5sZW5ndGggPT09IDAgfHwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5zZWxlY3RlZEZpbHRlcnMuYW5hbHlzaXNUeXBlLmluY2x1ZGVzKGlzc3VlLmFuYWx5c2lzVHlwZSk7CgogICAgICAgIC8vIElzc3VlIG11c3QgcGFzcyBib3RoIGZpbHRlcnMKICAgICAgICByZXR1cm4gcGFzc2VzU2V2ZXJpdHlGaWx0ZXIgJiYgcGFzc2VzQW5hbHlzaXNGaWx0ZXI7CiAgICAgIH0pOwogICAgfSwKCiAgICAvLyBDaGVjayBpZiBhbnkgZmlsdGVycyBhcmUgYWN0aXZlCiAgICBpc0ZpbHRlcnNBY3RpdmUoKSB7CiAgICAgIHJldHVybiB0aGlzLnNlbGVjdGVkRmlsdGVycy5zZXZlcml0eS5sZW5ndGggPiAwIHx8IHRoaXMuc2VsZWN0ZWRGaWx0ZXJzLmFuYWx5c2lzVHlwZS5sZW5ndGggPiAwOwogICAgfSwKCgoKICAgIC8vIENhbGN1bGF0ZSB0b3RhbCBhbGVydHMgKGNyaXRpY2FsIGJyZWFrb3V0IGdyb3VwcykKICAgIHRvdGFsQWxlcnRzKCkgewogICAgICByZXR1cm4gdGhpcy5icmVha291dEdyb3Vwcy5maWx0ZXIoZ3JvdXAgPT4gZ3JvdXAueEZhY3RvciA+PSAxLjUpLmxlbmd0aDsKICAgIH0sCgogICAgLy8gQ2FsY3VsYXRlIHRvdGFsIGluLXByb2dyZXNzIGlzc3VlcyBmcm9tIGFjdGlvbiB0cmFja2VyCiAgICBpblByb2dyZXNzSXNzdWVzQ291bnQoKSB7CiAgICAgIC8vIFRoaXMgd291bGQgbm9ybWFsbHkgZmV0Y2ggZnJvbSBhY3Rpb24gdHJhY2tlciBBUEkKICAgICAgLy8gRm9yIG5vdywgcmV0dXJuIGEgY2FsY3VsYXRlZCB2YWx1ZSBiYXNlZCBvbiBhY3Rpb24gdHJhY2tlciBhbGVydHMKICAgICAgcmV0dXJuIHRoaXMuaW5Qcm9ncmVzc0FsZXJ0cy5sZW5ndGg7CiAgICB9LAoKICAgIC8vIENhbGN1bGF0ZSB0b3RhbCBmYWlscyAodmFsaWRhdGVkICsgdW52YWxpZGF0ZWQpCiAgICB0b3RhbEZhaWxzKCkgewogICAgICByZXR1cm4gdGhpcy52YWxpZGF0ZWRDb3VudCArIHRoaXMudW52YWxpZGF0ZWRDb3VudDsKICAgIH0sCgogICAgLy8gQ2FsY3VsYXRlIGdyb3VwcyBvdmVyIHRhcmdldCAoeEZhY3RvciA+IDEuMCkKICAgIGdyb3Vwc092ZXJUYXJnZXRDb3VudCgpIHsKICAgICAgcmV0dXJuIHRoaXMuYnJlYWtvdXRHcm91cHMuZmlsdGVyKGdyb3VwID0+IGdyb3VwLnhGYWN0b3IgPiAxLjApLmxlbmd0aDsKICAgIH0sCgogICAgLy8gQ2FsY3VsYXRlIHRvdGFsIGFjdGlvbiB0cmFja2VyIGFsZXJ0cwogICAgdG90YWxBY3Rpb25UcmFja2VyQWxlcnRzKCkgewogICAgICByZXR1cm4gdGhpcy5pblByb2dyZXNzQWxlcnRzLmxlbmd0aCArIHRoaXMub3V0c3RhbmRpbmdBbGVydHMubGVuZ3RoICsgdGhpcy5yZXNvbHZlZEFsZXJ0cy5sZW5ndGg7CiAgICB9CiAgfSwKICB3YXRjaDogewogICAgcHFlT3duZXI6IHsKICAgICAgaW1tZWRpYXRlOiB0cnVlLAogICAgICBoYW5kbGVyKG5ld1ZhbHVlLCBvbGRWYWx1ZSkgewogICAgICAgIGNvbnNvbGUubG9nKGBQUUUgT3duZXIgY2hhbmdlZCBmcm9tICR7b2xkVmFsdWV9IHRvICR7bmV3VmFsdWV9YCk7CiAgICAgICAgaWYgKG5ld1ZhbHVlKSB7CiAgICAgICAgICAvLyBSZXNldCB0aGUgZ3JvdXAgc2VsZWN0aW9uIHdoZW4gUFFFIG93bmVyIGNoYW5nZXMKICAgICAgICAgIHRoaXMucm9vdENhdXNlU2VsZWN0ZWRHcm91cCA9ICdhbGwnOwogICAgICAgICAgdGhpcy5sb2FkRGFzaGJvYXJkRGF0YSgpOwogICAgICAgIH0KICAgICAgfQogICAgfQogIH0sCiAgbWV0aG9kczogewogICAgbG9hZERhc2hib2FyZERhdGEoKSB7CiAgICAgIHRoaXMubG9hZENyaXRpY2FsSXNzdWVzKCk7CiAgICAgIHRoaXMubG9hZFZhbGlkYXRpb25Db3VudHMoKTsKICAgICAgdGhpcy5sb2FkQWN0aW9uVHJhY2tlckFsZXJ0cygpOwogICAgICB0aGlzLmxvYWRSb290Q2F1c2VEYXRhKCk7CiAgICB9LAoKCgoKCiAgICBhc3luYyBsb2FkQ3JpdGljYWxJc3N1ZXMoKSB7CiAgICAgIGNvbnNvbGUubG9nKGBMb2FkaW5nIGNyaXRpY2FsIGlzc3VlcyBmb3IgUFFFIG93bmVyOiAke3RoaXMucHFlT3duZXJ9YCk7CiAgICAgIHRoaXMuaXNMb2FkaW5nID0gdHJ1ZTsKCiAgICAgIHRyeSB7CiAgICAgICAgLy8gR2V0IGF1dGhlbnRpY2F0aW9uIGNvbmZpZwogICAgICAgIGNvbnN0IGNvbmZpZyA9IHRoaXMuZ2V0QXV0aENvbmZpZygpOwoKICAgICAgICAvLyBGaXJzdCwgbG9hZCB0aGUgYnJlYWtvdXQgZ3JvdXBzIGZvciB0aGlzIFBRRSBvd25lcgogICAgICAgIGF3YWl0IHRoaXMubG9hZEJyZWFrb3V0R3JvdXBzKCk7CgogICAgICAgIC8vIEZldGNoIGNyaXRpY2FsIGlzc3VlcyBmcm9tIHRoZSBBUEkKICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpLXN0YXRpdDIvZ2V0X3BxZV9jcml0aWNhbF9pc3N1ZXMnLCB7CiAgICAgICAgICBtZXRob2Q6ICdQT1NUJywKICAgICAgICAgIGhlYWRlcnM6IHsKICAgICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJywKICAgICAgICAgICAgLi4uY29uZmlnLmhlYWRlcnMKICAgICAgICAgIH0sCiAgICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7CiAgICAgICAgICAgIHBxZU93bmVyOiB0aGlzLnBxZU93bmVyCiAgICAgICAgICB9KQogICAgICAgIH0pOwoKICAgICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7CiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byBmZXRjaCBjcml0aWNhbCBpc3N1ZXM6ICR7cmVzcG9uc2Uuc3RhdHVzfSAke3Jlc3BvbnNlLnN0YXR1c1RleHR9YCk7CiAgICAgICAgfQoKICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpOwoKICAgICAgICBpZiAoZGF0YS5zdGF0dXNfcmVzID09PSAnc3VjY2VzcycpIHsKICAgICAgICAgIC8vIEZpbHRlciBjcml0aWNhbCBpc3N1ZXMgdG8gb25seSBpbmNsdWRlIHRob3NlIHJlbGF0ZWQgdG8gdGhpcyBQUUUncyBicmVha291dCBncm91cHMKICAgICAgICAgIGNvbnN0IGFsbElzc3VlcyA9IGRhdGEuY3JpdGljYWxfaXNzdWVzIHx8IFtdOwoKICAgICAgICAgIC8vIElmIHdlIGhhdmUgYnJlYWtvdXQgZ3JvdXBzLCBmaWx0ZXIgaXNzdWVzIHRvIG9ubHkgaW5jbHVkZSB0aG9zZSBmb3IgdGhpcyBQUUUncyBncm91cHMKICAgICAgICAgIGlmICh0aGlzLmJyZWFrb3V0R3JvdXBzLmxlbmd0aCA+IDApIHsKICAgICAgICAgICAgdGhpcy51bnJlc29sdmVkQ3JpdGljYWxJc3N1ZXMgPSBhbGxJc3N1ZXMuZmlsdGVyKGlzc3VlID0+IHsKICAgICAgICAgICAgICAvLyBDaGVjayBpZiB0aGUgaXNzdWUncyBjYXRlZ29yeSBtYXRjaGVzIGFueSBvZiB0aGUgUFFFJ3MgYnJlYWtvdXQgZ3JvdXBzCiAgICAgICAgICAgICAgcmV0dXJuIHRoaXMuYnJlYWtvdXRHcm91cHMuc29tZShncm91cCA9PgogICAgICAgICAgICAgICAgaXNzdWUuY2F0ZWdvcnkuaW5jbHVkZXMoZ3JvdXAubmFtZSkgfHwgZ3JvdXAubmFtZS5pbmNsdWRlcyhpc3N1ZS5jYXRlZ29yeSkKICAgICAgICAgICAgICApOwogICAgICAgICAgICB9KTsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIC8vIElmIHdlIGRvbid0IGhhdmUgYnJlYWtvdXQgZ3JvdXBzIHlldCwgdXNlIGFsbCBpc3N1ZXMKICAgICAgICAgICAgdGhpcy51bnJlc29sdmVkQ3JpdGljYWxJc3N1ZXMgPSBhbGxJc3N1ZXM7CiAgICAgICAgICB9CgogICAgICAgICAgY29uc29sZS5sb2coYExvYWRlZCAke3RoaXMudW5yZXNvbHZlZENyaXRpY2FsSXNzdWVzLmxlbmd0aH0gY3JpdGljYWwgaXNzdWVzIGZvciAke3RoaXMucHFlT3duZXJ9J3MgZ3JvdXBzYCk7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBsb2FkIGNyaXRpY2FsIGlzc3VlczonLCBkYXRhLm1lc3NhZ2UpOwogICAgICAgICAgLy8gVXNlIHNhbXBsZSBkYXRhIGZvciBkZXZlbG9wbWVudAogICAgICAgICAgdGhpcy5sb2FkU2FtcGxlQ3JpdGljYWxJc3N1ZXMoKTsKICAgICAgICB9CiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgbG9hZGluZyBjcml0aWNhbCBpc3N1ZXM6JywgZXJyb3IpOwogICAgICAgIC8vIFVzZSBzYW1wbGUgZGF0YSBmb3IgZGV2ZWxvcG1lbnQKICAgICAgICB0aGlzLmxvYWRTYW1wbGVDcml0aWNhbElzc3VlcygpOwogICAgICB9IGZpbmFsbHkgewogICAgICAgIHRoaXMuaXNMb2FkaW5nID0gZmFsc2U7CiAgICAgIH0KICAgIH0sCgogICAgYXN5bmMgbG9hZEJyZWFrb3V0R3JvdXBzKCkgewogICAgICBjb25zb2xlLmxvZyhgTG9hZGluZyBicmVha291dCBncm91cHMgZm9yIFBRRSBvd25lcjogJHt0aGlzLnBxZU93bmVyfWApOwoKICAgICAgdHJ5IHsKICAgICAgICAvLyBHZXQgYXV0aGVudGljYXRpb24gY29uZmlnCiAgICAgICAgY29uc3QgY29uZmlnID0gdGhpcy5nZXRBdXRoQ29uZmlnKCk7CgogICAgICAgIC8vIEZldGNoIGJyZWFrb3V0IGdyb3VwcyBmcm9tIHRoZSBBUEkKICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpLXN0YXRpdDIvZ2V0X3BxZV9icmVha291dF9ncm91cHMnLCB7CiAgICAgICAgICBtZXRob2Q6ICdQT1NUJywKICAgICAgICAgIGhlYWRlcnM6IHsKICAgICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJywKICAgICAgICAgICAgLi4uY29uZmlnLmhlYWRlcnMKICAgICAgICAgIH0sCiAgICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7CiAgICAgICAgICAgIHBxZU93bmVyOiB0aGlzLnBxZU93bmVyCiAgICAgICAgICB9KQogICAgICAgIH0pOwoKICAgICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7CiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byBmZXRjaCBicmVha291dCBncm91cHM6ICR7cmVzcG9uc2Uuc3RhdHVzfSAke3Jlc3BvbnNlLnN0YXR1c1RleHR9YCk7CiAgICAgICAgfQoKICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpOwoKICAgICAgICBpZiAoZGF0YS5zdGF0dXNfcmVzID09PSAnc3VjY2VzcycpIHsKICAgICAgICAgIHRoaXMuYnJlYWtvdXRHcm91cHMgPSBkYXRhLmJyZWFrb3V0X2dyb3VwcyB8fCBbXTsKICAgICAgICAgIGNvbnNvbGUubG9nKGBMb2FkZWQgJHt0aGlzLmJyZWFrb3V0R3JvdXBzLmxlbmd0aH0gYnJlYWtvdXQgZ3JvdXBzIGZvciAke3RoaXMucHFlT3duZXJ9YCk7CgogICAgICAgICAgLy8gUmVsb2FkIHJvb3QgY2F1c2UgY2hhcnQgZGF0YSBub3cgdGhhdCB3ZSBoYXZlIHRoZSBjb3JyZWN0IGJyZWFrb3V0IGdyb3VwcwogICAgICAgICAgYXdhaXQgdGhpcy5sb2FkUm9vdENhdXNlRGF0YSgpOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gbG9hZCBicmVha291dCBncm91cHM6JywgZGF0YS5tZXNzYWdlKTsKICAgICAgICAgIC8vIFVzZSBzYW1wbGUgZGF0YSBmb3IgZGV2ZWxvcG1lbnQKICAgICAgICAgIHRoaXMubG9hZFNhbXBsZUJyZWFrb3V0R3JvdXBzKCk7CiAgICAgICAgICAvLyBSZWxvYWQgcm9vdCBjYXVzZSBjaGFydCBkYXRhIHdpdGggc2FtcGxlIGdyb3VwcwogICAgICAgICAgYXdhaXQgdGhpcy5sb2FkUm9vdENhdXNlRGF0YSgpOwogICAgICAgIH0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBsb2FkaW5nIGJyZWFrb3V0IGdyb3VwczonLCBlcnJvcik7CiAgICAgICAgLy8gVXNlIHNhbXBsZSBkYXRhIGZvciBkZXZlbG9wbWVudAogICAgICAgIHRoaXMubG9hZFNhbXBsZUJyZWFrb3V0R3JvdXBzKCk7CiAgICAgICAgLy8gUmVsb2FkIHJvb3QgY2F1c2UgY2hhcnQgZGF0YSB3aXRoIHNhbXBsZSBncm91cHMKICAgICAgICBhd2FpdCB0aGlzLmxvYWRSb290Q2F1c2VEYXRhKCk7CiAgICAgIH0KICAgIH0sCgogICAgYXN5bmMgbG9hZFNhbXBsZUJyZWFrb3V0R3JvdXBzKCkgewogICAgICAvLyBTYW1wbGUgZGF0YSBmb3IgZGV2ZWxvcG1lbnQKICAgICAgaWYgKHRoaXMucHFlT3duZXIgPT09ICdBbGJlcnQgRy4nKSB7CiAgICAgICAgdGhpcy5icmVha291dEdyb3VwcyA9IFsKICAgICAgICAgIHsgbmFtZTogJ0ZhbiBUaGVtaXMnLCBzdGF0dXM6ICdTaG9ydC1UZXJtIFNwaWtlJywgeEZhY3RvcjogMy4yIH0sCiAgICAgICAgICB7IG5hbWU6ICdWaWN0b3JpYSBDcnlwdG8nLCBzdGF0dXM6ICdTdXN0YWluZWQgUHJvYmxlbScsIHhGYWN0b3I6IDEuOCB9LAogICAgICAgICAgeyBuYW1lOiAnUXVhbnR1bSBOZXh1cycsIHN0YXR1czogJ1N1c3RhaW5lZCBQcm9ibGVtJywgeEZhY3RvcjogMS42IH0KICAgICAgICBdOwogICAgICB9IGVsc2UgaWYgKHRoaXMucHFlT3duZXIgPT09ICdTYXJhaCBMLicpIHsKICAgICAgICB0aGlzLmJyZWFrb3V0R3JvdXBzID0gWwogICAgICAgICAgeyBuYW1lOiAnU3RlbGxhciBDb3JlJywgc3RhdHVzOiAnU2hvcnQtVGVybSBTcGlrZScsIHhGYWN0b3I6IDIuNSB9LAogICAgICAgICAgeyBuYW1lOiAnTmVidWxhIERyaXZlJywgc3RhdHVzOiAnU3VzdGFpbmVkIFByb2JsZW0nLCB4RmFjdG9yOiAxLjcgfQogICAgICAgIF07CiAgICAgIH0gZWxzZSB7CiAgICAgICAgLy8gRGVmYXVsdCBzYW1wbGUgZGF0YQogICAgICAgIHRoaXMuYnJlYWtvdXRHcm91cHMgPSBbCiAgICAgICAgICB7IG5hbWU6ICdTYW1wbGUgR3JvdXAgMScsIHN0YXR1czogJ05vcm1hbCcsIHhGYWN0b3I6IDAuOSB9LAogICAgICAgICAgeyBuYW1lOiAnU2FtcGxlIEdyb3VwIDInLCBzdGF0dXM6ICdOb3JtYWwnLCB4RmFjdG9yOiAwLjggfQogICAgICAgIF07CiAgICAgIH0KCiAgICAgIC8vIFJlbG9hZCByb290IGNhdXNlIGNoYXJ0IGRhdGEgd2l0aCB0aGUgc2FtcGxlIGdyb3VwcwogICAgICBhd2FpdCB0aGlzLmxvYWRSb290Q2F1c2VEYXRhKCk7CiAgICB9LAoKICAgIGxvYWRTYW1wbGVDcml0aWNhbElzc3VlcygpIHsKICAgICAgLy8gU2FtcGxlIGRhdGEgZm9yIGRldmVsb3BtZW50CiAgICAgIHRoaXMudW5yZXNvbHZlZENyaXRpY2FsSXNzdWVzID0gWwogICAgICAgIHsKICAgICAgICAgIGlkOiAnY2kxJywKICAgICAgICAgIGNhdGVnb3J5OiAnRmFuIFRoZW1pcycsCiAgICAgICAgICBtb250aDogJzIwMjQtMDYnLAogICAgICAgICAgc2V2ZXJpdHk6ICdoaWdoJywKICAgICAgICAgIGluY3JlYXNlTXVsdGlwbGllcjogJzMuMicsCiAgICAgICAgICBhaURlc2NyaXB0aW9uOiAnRmFuIFRoZW1pcyBzaG93aW5nIDMuMnggc3Bpa2UgaW4gSnVuZSAyMDI0IHdpdGggMTIgZmFpbHMgdnMgNCBleHBlY3RlZC4gUm9vdCBjYXVzZSBhcHBlYXJzIHRvIGJlIGFpcmZsb3cgaXNzdWVzIGluIHRoZSBuZXcgZGVzaWduLicsCiAgICAgICAgICBjb21tZW50OiAnJywKICAgICAgICAgIGFuYWx5c2lzVHlwZTogJ1Jvb3QgQ2F1c2UnCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBpZDogJ2NpMicsCiAgICAgICAgICBjYXRlZ29yeTogJ1ZpY3RvcmlhIENyeXB0bycsCiAgICAgICAgICBtb250aDogJzIwMjQtMDYnLAogICAgICAgICAgc2V2ZXJpdHk6ICdtZWRpdW0nLAogICAgICAgICAgaW5jcmVhc2VNdWx0aXBsaWVyOiAnMS44JywKICAgICAgICAgIGFpRGVzY3JpcHRpb246ICdWaWN0b3JpYSBDcnlwdG8gaGFzIHN1c3RhaW5lZCBwcm9ibGVtIHdpdGggMS44eCB0YXJnZXQgcmF0ZSBmb3IgMyBjb25zZWN1dGl2ZSBtb250aHMuIENvbnNpc3RlbnQgcGF0dGVybiBvZiBwb3dlciBkZWxpdmVyeSBpc3N1ZXMuJywKICAgICAgICAgIGNvbW1lbnQ6ICcnLAogICAgICAgICAgYW5hbHlzaXNUeXBlOiAnUm9vdCBDYXVzZScKICAgICAgICB9LAoKICAgICAgXTsKCiAgICAgIC8vIFNldCBuZXcgY3JpdGljYWwgaXNzdWVzIHRvIGVtcHR5IGFycmF5IGZvciBub3cKICAgICAgdGhpcy5uZXdDcml0aWNhbElzc3VlcyA9IFtdOwogICAgfSwKCiAgICBhc3luYyBsb2FkVmFsaWRhdGlvbkNvdW50cygpIHsKICAgICAgY29uc29sZS5sb2coYExvYWRpbmcgdmFsaWRhdGlvbiBjb3VudHMgZm9yIFBRRSBvd25lcjogJHt0aGlzLnBxZU93bmVyfWApOwoKICAgICAgdHJ5IHsKICAgICAgICAvLyBHZXQgYXV0aGVudGljYXRpb24gY29uZmlnCiAgICAgICAgY29uc3QgY29uZmlnID0gdGhpcy5nZXRBdXRoQ29uZmlnKCk7CgogICAgICAgIC8vIEZldGNoIHZhbGlkYXRpb24gY291bnRzIGZyb20gdGhlIEFQSQogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGktc3RhdGl0Mi9nZXRfdmFsaWRhdGlvbl9jb3VudHMnLCB7CiAgICAgICAgICBtZXRob2Q6ICdQT1NUJywKICAgICAgICAgIGhlYWRlcnM6IHsKICAgICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJywKICAgICAgICAgICAgLi4uY29uZmlnLmhlYWRlcnMKICAgICAgICAgIH0sCiAgICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7CiAgICAgICAgICAgIHBxZU93bmVyOiB0aGlzLnBxZU93bmVyCiAgICAgICAgICB9KQogICAgICAgIH0pOwoKICAgICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7CiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byBmZXRjaCB2YWxpZGF0aW9uIGNvdW50czogJHtyZXNwb25zZS5zdGF0dXN9ICR7cmVzcG9uc2Uuc3RhdHVzVGV4dH1gKTsKICAgICAgICB9CgogICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7CgogICAgICAgIGlmIChkYXRhLnN0YXR1c19yZXMgPT09ICdzdWNjZXNzJykgewogICAgICAgICAgdGhpcy52YWxpZGF0ZWRDb3VudCA9IGRhdGEudmFsaWRhdGVkX2NvdW50IHx8IDA7CiAgICAgICAgICB0aGlzLnVudmFsaWRhdGVkQ291bnQgPSBkYXRhLnVudmFsaWRhdGVkX2NvdW50IHx8IDA7CiAgICAgICAgICBjb25zb2xlLmxvZyhgTG9hZGVkIHZhbGlkYXRpb24gY291bnRzOiAke3RoaXMudmFsaWRhdGVkQ291bnR9IHZhbGlkYXRlZCwgJHt0aGlzLnVudmFsaWRhdGVkQ291bnR9IHVudmFsaWRhdGVkYCk7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBsb2FkIHZhbGlkYXRpb24gY291bnRzOicsIGRhdGEubWVzc2FnZSk7CiAgICAgICAgICAvLyBVc2Ugc2FtcGxlIGRhdGEgZm9yIGRldmVsb3BtZW50CiAgICAgICAgICB0aGlzLnZhbGlkYXRlZENvdW50ID0gMTI1OwogICAgICAgICAgdGhpcy51bnZhbGlkYXRlZENvdW50ID0gMzc7CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGxvYWRpbmcgdmFsaWRhdGlvbiBjb3VudHM6JywgZXJyb3IpOwogICAgICAgIC8vIFVzZSBzYW1wbGUgZGF0YSBmb3IgZGV2ZWxvcG1lbnQKICAgICAgICB0aGlzLnZhbGlkYXRlZENvdW50ID0gMTI1OwogICAgICAgIHRoaXMudW52YWxpZGF0ZWRDb3VudCA9IDM3OwogICAgICB9CiAgICB9LAoKICAgIC8vIExvYWQgQWN0aW9uIFRyYWNrZXIgQWxlcnRzIGRhdGEKICAgIGxvYWRBY3Rpb25UcmFja2VyQWxlcnRzKCkgewogICAgICBjb25zb2xlLmxvZyhgTG9hZGluZyBBY3Rpb24gVHJhY2tlciBBbGVydHMgZm9yIFBRRSBvd25lcjogJHt0aGlzLnBxZU93bmVyfWApOwoKICAgICAgLy8gR2VuZXJhdGUgc2FtcGxlIGRhdGEgZm9yIGRlbW9uc3RyYXRpb24KICAgICAgLy8gSW4gYSByZWFsIGltcGxlbWVudGF0aW9uLCB0aGlzIHdvdWxkIGZldGNoIGZyb20gdGhlIGFjdGlvbiB0cmFja2VyIEFQSQogICAgICB0aGlzLmluUHJvZ3Jlc3NBbGVydHMgPSBbCiAgICAgICAgewogICAgICAgICAgaWQ6ICdpcC0xJywKICAgICAgICAgIGNhdGVnb3J5OiAnVGhlcm1hbCBNYW5hZ2VtZW50JywKICAgICAgICAgIHNldmVyaXR5OiAnSGlnaCcsCiAgICAgICAgICB4RmFjdG9yOiAnMi4xJywKICAgICAgICAgIHN0YXR1czogJ0luLVByb2dyZXNzJwogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgaWQ6ICdpcC0yJywKICAgICAgICAgIGNhdGVnb3J5OiAnUG93ZXIgRGVsaXZlcnknLAogICAgICAgICAgc2V2ZXJpdHk6ICdNZWRpdW0nLAogICAgICAgICAgeEZhY3RvcjogJzEuOCcsCiAgICAgICAgICBzdGF0dXM6ICdJbi1Qcm9ncmVzcycKICAgICAgICB9CiAgICAgIF07CgogICAgICB0aGlzLm91dHN0YW5kaW5nQWxlcnRzID0gWwogICAgICAgIHsKICAgICAgICAgIGlkOiAnb3V0LTEnLAogICAgICAgICAgY2F0ZWdvcnk6ICdTaWduYWwgSW50ZWdyaXR5JywKICAgICAgICAgIHNldmVyaXR5OiAnTWVkaXVtJywKICAgICAgICAgIHhGYWN0b3I6ICcxLjYnLAogICAgICAgICAgc3RhdHVzOiAnT3V0c3RhbmRpbmcnCiAgICAgICAgfQogICAgICBdOwoKICAgICAgdGhpcy5yZXNvbHZlZEFsZXJ0cyA9IFsKICAgICAgICB7CiAgICAgICAgICBpZDogJ3Jlcy0xJywKICAgICAgICAgIGNhdGVnb3J5OiAnTWFudWZhY3R1cmluZyBEZWZlY3QnLAogICAgICAgICAgc2V2ZXJpdHk6ICdIaWdoJywKICAgICAgICAgIHhGYWN0b3I6ICcwLjgnLAogICAgICAgICAgc3RhdHVzOiAnUmVzb2x2ZWQnCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBpZDogJ3Jlcy0yJywKICAgICAgICAgIGNhdGVnb3J5OiAnQ29tcG9uZW50IFF1YWxpdHknLAogICAgICAgICAgc2V2ZXJpdHk6ICdNZWRpdW0nLAogICAgICAgICAgeEZhY3RvcjogJzAuOScsCiAgICAgICAgICBzdGF0dXM6ICdSZXNvbHZlZCcKICAgICAgICB9CiAgICAgIF07CgogICAgICBjb25zb2xlLmxvZyhgTG9hZGVkICR7dGhpcy50b3RhbEFjdGlvblRyYWNrZXJBbGVydHN9IEFjdGlvbiBUcmFja2VyIEFsZXJ0c2ApOwogICAgfSwKCiAgICB0b2dnbGVDcml0aWNhbElzc3Vlc0V4cGFuZGVkKCkgewogICAgICB0aGlzLmlzQ3JpdGljYWxJc3N1ZXNFeHBhbmRlZCA9ICF0aGlzLmlzQ3JpdGljYWxJc3N1ZXNFeHBhbmRlZDsKICAgIH0sCgogICAgdG9nZ2xlUmVzb2x2ZWRJc3N1ZXNFeHBhbmRlZCgpIHsKICAgICAgdGhpcy5pc1Jlc29sdmVkSXNzdWVzRXhwYW5kZWQgPSAhdGhpcy5pc1Jlc29sdmVkSXNzdWVzRXhwYW5kZWQ7CiAgICB9LAoKICAgIHRvZ2dsZU91dHN0YW5kaW5nSXNzdWVzRXhwYW5kZWQoKSB7CiAgICAgIHRoaXMuaXNPdXRzdGFuZGluZ0lzc3Vlc0V4cGFuZGVkID0gIXRoaXMuaXNPdXRzdGFuZGluZ0lzc3Vlc0V4cGFuZGVkOwogICAgfSwKCiAgICB0b2dnbGVJc3N1ZUV4cGFuZGVkKGlzc3VlKSB7CiAgICAgIGNvbnN0IGlzc3VlSWQgPSBpc3N1ZS5pZDsKICAgICAgY29uc3QgaW5kZXggPSB0aGlzLmV4cGFuZGVkSXNzdWVJZHMuaW5kZXhPZihpc3N1ZUlkKTsKCiAgICAgIGlmIChpbmRleCA9PT0gLTEpIHsKICAgICAgICAvLyBJc3N1ZSBpcyBub3QgZXhwYW5kZWQsIHNvIGV4cGFuZCBpdAogICAgICAgIHRoaXMuZXhwYW5kZWRJc3N1ZUlkcy5wdXNoKGlzc3VlSWQpOwogICAgICB9IGVsc2UgewogICAgICAgIC8vIElzc3VlIGlzIGV4cGFuZGVkLCBzbyBjb2xsYXBzZSBpdAogICAgICAgIHRoaXMuZXhwYW5kZWRJc3N1ZUlkcy5zcGxpY2UoaW5kZXgsIDEpOwogICAgICB9CiAgICB9LAoKICAgIHRvZ2dsZVJlc29sdmVkSXNzdWVFeHBhbmRlZChpc3N1ZSkgewogICAgICBjb25zdCBpc3N1ZUlkID0gaXNzdWUuaWQ7CiAgICAgIGNvbnN0IGluZGV4ID0gdGhpcy5leHBhbmRlZFJlc29sdmVkSXNzdWVJZHMuaW5kZXhPZihpc3N1ZUlkKTsKCiAgICAgIGlmIChpbmRleCA9PT0gLTEpIHsKICAgICAgICAvLyBJc3N1ZSBpcyBub3QgZXhwYW5kZWQsIHNvIGV4cGFuZCBpdAogICAgICAgIHRoaXMuZXhwYW5kZWRSZXNvbHZlZElzc3VlSWRzLnB1c2goaXNzdWVJZCk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgLy8gSXNzdWUgaXMgZXhwYW5kZWQsIHNvIGNvbGxhcHNlIGl0CiAgICAgICAgdGhpcy5leHBhbmRlZFJlc29sdmVkSXNzdWVJZHMuc3BsaWNlKGluZGV4LCAxKTsKICAgICAgfQogICAgfSwKCiAgICB0b2dnbGVPdXRzdGFuZGluZ0lzc3VlRXhwYW5kZWQoaXNzdWUpIHsKICAgICAgY29uc3QgaXNzdWVJZCA9IGlzc3VlLmlkOwogICAgICBjb25zdCBpbmRleCA9IHRoaXMuZXhwYW5kZWRPdXRzdGFuZGluZ0lzc3VlSWRzLmluZGV4T2YoaXNzdWVJZCk7CgogICAgICBpZiAoaW5kZXggPT09IC0xKSB7CiAgICAgICAgLy8gSXNzdWUgaXMgbm90IGV4cGFuZGVkLCBzbyBleHBhbmQgaXQKICAgICAgICB0aGlzLmV4cGFuZGVkT3V0c3RhbmRpbmdJc3N1ZUlkcy5wdXNoKGlzc3VlSWQpOwogICAgICB9IGVsc2UgewogICAgICAgIC8vIElzc3VlIGlzIGV4cGFuZGVkLCBzbyBjb2xsYXBzZSBpdAogICAgICAgIHRoaXMuZXhwYW5kZWRPdXRzdGFuZGluZ0lzc3VlSWRzLnNwbGljZShpbmRleCwgMSk7CiAgICAgIH0KICAgIH0sCgogICAgaXNJc3N1ZUV4cGFuZGVkKGlzc3VlSWQpIHsKICAgICAgcmV0dXJuIHRoaXMuZXhwYW5kZWRJc3N1ZUlkcy5pbmNsdWRlcyhpc3N1ZUlkKTsKICAgIH0sCgogICAgaXNSZXNvbHZlZElzc3VlRXhwYW5kZWQoaXNzdWVJZCkgewogICAgICByZXR1cm4gdGhpcy5leHBhbmRlZFJlc29sdmVkSXNzdWVJZHMuaW5jbHVkZXMoaXNzdWVJZCk7CiAgICB9LAoKICAgIGlzT3V0c3RhbmRpbmdJc3N1ZUV4cGFuZGVkKGlzc3VlSWQpIHsKICAgICAgcmV0dXJuIHRoaXMuZXhwYW5kZWRPdXRzdGFuZGluZ0lzc3VlSWRzLmluY2x1ZGVzKGlzc3VlSWQpOwogICAgfSwKCiAgICBtYXJrSXNzdWVBc1Jlc29sdmVkKGlzc3VlKSB7CiAgICAgIGNvbnNvbGUubG9nKCdNYXJrIGlzc3VlIGFzIHJlc29sdmVkOicsIGlzc3VlKTsKCiAgICAgIC8vIEluIGEgcmVhbCBpbXBsZW1lbnRhdGlvbiwgdGhpcyB3b3VsZCBjYWxsIGFuIEFQSSB0byB1cGRhdGUgdGhlIGlzc3VlIHN0YXR1cwogICAgICAvLyBGb3Igbm93LCB3ZSdsbCBqdXN0IG1vdmUgdGhlIGlzc3VlIGZyb20gdW5yZXNvbHZlZCB0byByZXNvbHZlZAoKICAgICAgLy8gQ3JlYXRlIGEgcmVzb2x2ZWQgaXNzdWUgb2JqZWN0IHdpdGggYWRkaXRpb25hbCBmaWVsZHMKICAgICAgY29uc3QgcmVzb2x2ZWRJc3N1ZSA9IHsKICAgICAgICAuLi5pc3N1ZSwKICAgICAgICByZXNvbHV0aW9uRGF0ZTogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLnNwbGl0KCdUJylbMF0sIC8vIFRvZGF5J3MgZGF0ZSBpbiBZWVlZLU1NLUREIGZvcm1hdAogICAgICAgIGN1cnJlbnRQZXJmb3JtYW5jZTogJzEuMHgnIC8vIEluaXRpYWwgcGVyZm9ybWFuY2UgYWZ0ZXIgcmVzb2x1dGlvbgogICAgICB9OwoKICAgICAgLy8gQWRkIHRvIHJlc29sdmVkIGlzc3VlcwogICAgICB0aGlzLnJlc29sdmVkSXNzdWVzLnB1c2gocmVzb2x2ZWRJc3N1ZSk7CgogICAgICAvLyBSZW1vdmUgZnJvbSB1bnJlc29sdmVkIGlzc3VlcwogICAgICBjb25zdCBpbmRleCA9IHRoaXMudW5yZXNvbHZlZENyaXRpY2FsSXNzdWVzLmZpbmRJbmRleChpID0+IGkuaWQgPT09IGlzc3VlLmlkKTsKICAgICAgaWYgKGluZGV4ICE9PSAtMSkgewogICAgICAgIHRoaXMudW5yZXNvbHZlZENyaXRpY2FsSXNzdWVzLnNwbGljZShpbmRleCwgMSk7CiAgICAgIH0KCiAgICAgIC8vIFNob3cgc3VjY2VzcyBtZXNzYWdlCiAgICAgIGFsZXJ0KGBJc3N1ZSBtYXJrZWQgYXMgcmVzb2x2ZWQ6ICR7aXNzdWUuY2F0ZWdvcnl9YCk7CiAgICB9LAoKICAgIG1hcmtJc3N1ZUFzT3V0c3RhbmRpbmcoaXNzdWUpIHsKICAgICAgY29uc29sZS5sb2coJ01hcmsgaXNzdWUgYXMgb3V0c3RhbmRpbmc6JywgaXNzdWUpOwoKICAgICAgLy8gSW4gYSByZWFsIGltcGxlbWVudGF0aW9uLCB0aGlzIHdvdWxkIGNhbGwgYW4gQVBJIHRvIHVwZGF0ZSB0aGUgaXNzdWUgc3RhdHVzCiAgICAgIC8vIEZvciBub3csIHdlJ2xsIGp1c3QgbW92ZSB0aGUgaXNzdWUgZnJvbSB1bnJlc29sdmVkIHRvIG91dHN0YW5kaW5nCgogICAgICAvLyBDcmVhdGUgYW4gb3V0c3RhbmRpbmcgaXNzdWUgb2JqZWN0IHdpdGggYWRkaXRpb25hbCBmaWVsZHMKICAgICAgY29uc3Qgb3V0c3RhbmRpbmdJc3N1ZSA9IHsKICAgICAgICAuLi5pc3N1ZSwKICAgICAgICBhY2NlcHRhbmNlRGF0ZTogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLnNwbGl0KCdUJylbMF0sIC8vIFRvZGF5J3MgZGF0ZSBpbiBZWVlZLU1NLUREIGZvcm1hdAogICAgICAgIGN1cnJlbnRQZXJmb3JtYW5jZTogaXNzdWUuaW5jcmVhc2VNdWx0aXBsaWVyLCAvLyBJbml0aWFsIHBlcmZvcm1hbmNlIGlzIHRoZSBzYW1lIGFzIHRoZSBpc3N1ZSBtdWx0aXBsaWVyCiAgICAgICAgYWNjZXB0ZWRCeTogJ0VuZ2luZWVyaW5nIFRlYW0nIC8vIERlZmF1bHQgdmFsdWUKICAgICAgfTsKCiAgICAgIC8vIEFkZCB0byBvdXRzdGFuZGluZyBpc3N1ZXMKICAgICAgdGhpcy5vdXRzdGFuZGluZ0lzc3Vlcy5wdXNoKG91dHN0YW5kaW5nSXNzdWUpOwoKICAgICAgLy8gUmVtb3ZlIGZyb20gdW5yZXNvbHZlZCBpc3N1ZXMKICAgICAgY29uc3QgaW5kZXggPSB0aGlzLnVucmVzb2x2ZWRDcml0aWNhbElzc3Vlcy5maW5kSW5kZXgoaSA9PiBpLmlkID09PSBpc3N1ZS5pZCk7CiAgICAgIGlmIChpbmRleCAhPT0gLTEpIHsKICAgICAgICB0aGlzLnVucmVzb2x2ZWRDcml0aWNhbElzc3Vlcy5zcGxpY2UoaW5kZXgsIDEpOwogICAgICB9CgogICAgICAvLyBTaG93IHN1Y2Nlc3MgbWVzc2FnZQogICAgICBhbGVydChgSXNzdWUgbWFya2VkIGFzIG91dHN0YW5kaW5nOiAke2lzc3VlLmNhdGVnb3J5fWApOwogICAgfSwKCiAgICBoYW5kbGVTZXZlcml0eUZpbHRlckNoYW5nZSgpIHsKICAgICAgLy8gVXBkYXRlIHRoZSBzZWxlY3RlZCBmaWx0ZXJzIGJhc2VkIG9uIHRoZSBzZXZlcml0eSBkcm9wZG93bgogICAgICBpZiAodGhpcy5zZXZlcml0eUZpbHRlciA9PT0gJ2FsbCcpIHsKICAgICAgICB0aGlzLnNlbGVjdGVkRmlsdGVycy5zZXZlcml0eSA9IFtdOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuc2VsZWN0ZWRGaWx0ZXJzLnNldmVyaXR5ID0gW3RoaXMuc2V2ZXJpdHlGaWx0ZXJdOwogICAgICB9CiAgICB9LAoKICAgIGhhbmRsZUFuYWx5c2lzRmlsdGVyQ2hhbmdlKCkgewogICAgICAvLyBVcGRhdGUgdGhlIHNlbGVjdGVkIGZpbHRlcnMgYmFzZWQgb24gdGhlIGFuYWx5c2lzIHR5cGUgZHJvcGRvd24KICAgICAgaWYgKHRoaXMuYW5hbHlzaXNUeXBlRmlsdGVyID09PSAnYWxsJykgewogICAgICAgIHRoaXMuc2VsZWN0ZWRGaWx0ZXJzLmFuYWx5c2lzVHlwZSA9IFtdOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuc2VsZWN0ZWRGaWx0ZXJzLmFuYWx5c2lzVHlwZSA9IFt0aGlzLmFuYWx5c2lzVHlwZUZpbHRlcl07CiAgICAgIH0KICAgIH0sCgogICAgY2xlYXJGaWx0ZXJzKCkgewogICAgICB0aGlzLnNlbGVjdGVkRmlsdGVycy5zZXZlcml0eSA9IFtdOwogICAgICB0aGlzLnNlbGVjdGVkRmlsdGVycy5hbmFseXNpc1R5cGUgPSBbXTsKICAgICAgdGhpcy5zZXZlcml0eUZpbHRlciA9ICdhbGwnOwogICAgICB0aGlzLmFuYWx5c2lzVHlwZUZpbHRlciA9ICdhbGwnOwogICAgfSwKCiAgICBoYW5kbGVSZXNvbHZlZENhdGVnb3J5RmlsdGVyQ2hhbmdlKCkgewogICAgICAvLyBVcGRhdGUgdGhlIHNlbGVjdGVkIGZpbHRlcnMgYmFzZWQgb24gdGhlIGNhdGVnb3J5IGRyb3Bkb3duCiAgICAgIGlmICh0aGlzLnJlc29sdmVkQ2F0ZWdvcnlGaWx0ZXIgPT09ICdhbGwnKSB7CiAgICAgICAgdGhpcy5yZXNvbHZlZEZpbHRlcnMuY2F0ZWdvcnkgPSBbXTsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLnJlc29sdmVkRmlsdGVycy5jYXRlZ29yeSA9IFt0aGlzLnJlc29sdmVkQ2F0ZWdvcnlGaWx0ZXJdOwogICAgICB9CiAgICB9LAoKICAgIGhhbmRsZVJlc29sdmVkQW5hbHlzaXNGaWx0ZXJDaGFuZ2UoKSB7CiAgICAgIC8vIFVwZGF0ZSB0aGUgc2VsZWN0ZWQgZmlsdGVycyBiYXNlZCBvbiB0aGUgYW5hbHlzaXMgdHlwZSBkcm9wZG93bgogICAgICBpZiAodGhpcy5yZXNvbHZlZEFuYWx5c2lzVHlwZUZpbHRlciA9PT0gJ2FsbCcpIHsKICAgICAgICB0aGlzLnJlc29sdmVkRmlsdGVycy5hbmFseXNpc1R5cGUgPSBbXTsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLnJlc29sdmVkRmlsdGVycy5hbmFseXNpc1R5cGUgPSBbdGhpcy5yZXNvbHZlZEFuYWx5c2lzVHlwZUZpbHRlcl07CiAgICAgIH0KICAgIH0sCgogICAgY2xlYXJSZXNvbHZlZEZpbHRlcnMoKSB7CiAgICAgIHRoaXMucmVzb2x2ZWRGaWx0ZXJzLmNhdGVnb3J5ID0gW107CiAgICAgIHRoaXMucmVzb2x2ZWRGaWx0ZXJzLmFuYWx5c2lzVHlwZSA9IFtdOwogICAgICB0aGlzLnJlc29sdmVkQ2F0ZWdvcnlGaWx0ZXIgPSAnYWxsJzsKICAgICAgdGhpcy5yZXNvbHZlZEFuYWx5c2lzVHlwZUZpbHRlciA9ICdhbGwnOwogICAgfSwKCiAgICBoYW5kbGVPdXRzdGFuZGluZ0NhdGVnb3J5RmlsdGVyQ2hhbmdlKCkgewogICAgICAvLyBVcGRhdGUgdGhlIHNlbGVjdGVkIGZpbHRlcnMgYmFzZWQgb24gdGhlIGNhdGVnb3J5IGRyb3Bkb3duCiAgICAgIGlmICh0aGlzLm91dHN0YW5kaW5nQ2F0ZWdvcnlGaWx0ZXIgPT09ICdhbGwnKSB7CiAgICAgICAgdGhpcy5vdXRzdGFuZGluZ0ZpbHRlcnMuY2F0ZWdvcnkgPSBbXTsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLm91dHN0YW5kaW5nRmlsdGVycy5jYXRlZ29yeSA9IFt0aGlzLm91dHN0YW5kaW5nQ2F0ZWdvcnlGaWx0ZXJdOwogICAgICB9CiAgICB9LAoKICAgIGhhbmRsZU91dHN0YW5kaW5nQW5hbHlzaXNGaWx0ZXJDaGFuZ2UoKSB7CiAgICAgIC8vIFVwZGF0ZSB0aGUgc2VsZWN0ZWQgZmlsdGVycyBiYXNlZCBvbiB0aGUgYW5hbHlzaXMgdHlwZSBkcm9wZG93bgogICAgICBpZiAodGhpcy5vdXRzdGFuZGluZ0FuYWx5c2lzVHlwZUZpbHRlciA9PT0gJ2FsbCcpIHsKICAgICAgICB0aGlzLm91dHN0YW5kaW5nRmlsdGVycy5hbmFseXNpc1R5cGUgPSBbXTsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLm91dHN0YW5kaW5nRmlsdGVycy5hbmFseXNpc1R5cGUgPSBbdGhpcy5vdXRzdGFuZGluZ0FuYWx5c2lzVHlwZUZpbHRlcl07CiAgICAgIH0KICAgIH0sCgogICAgY2xlYXJPdXRzdGFuZGluZ0ZpbHRlcnMoKSB7CiAgICAgIHRoaXMub3V0c3RhbmRpbmdGaWx0ZXJzLmNhdGVnb3J5ID0gW107CiAgICAgIHRoaXMub3V0c3RhbmRpbmdGaWx0ZXJzLmFuYWx5c2lzVHlwZSA9IFtdOwogICAgICB0aGlzLm91dHN0YW5kaW5nQ2F0ZWdvcnlGaWx0ZXIgPSAnYWxsJzsKICAgICAgdGhpcy5vdXRzdGFuZGluZ0FuYWx5c2lzVHlwZUZpbHRlciA9ICdhbGwnOwogICAgfSwKCiAgICB1cGRhdGVJc3N1ZShpc3N1ZSwgbWFya0FzUmVzb2x2ZWQgPSBmYWxzZSwgbWFya0FzT3V0c3RhbmRpbmcgPSBmYWxzZSkgewogICAgICBjb25zb2xlLmxvZygnVXBkYXRlIGlzc3VlOicsIGlzc3VlLCAnTWFyayBhcyByZXNvbHZlZDonLCBtYXJrQXNSZXNvbHZlZCwgJ01hcmsgYXMgb3V0c3RhbmRpbmc6JywgbWFya0FzT3V0c3RhbmRpbmcpOwoKICAgICAgLy8gRW1pdCBldmVudCB0byB1cGRhdGUgYWN0aW9uIHRyYWNrZXIKICAgICAgdGhpcy4kZW1pdCgndXBkYXRlLWFjdGlvbi10cmFja2VyJywgewogICAgICAgIGlzc3VlSWQ6IGlzc3VlLmlkLAogICAgICAgIGNhdGVnb3J5OiBpc3N1ZS5jYXRlZ29yeSwKICAgICAgICBjb21tZW50OiBpc3N1ZS5jb21tZW50LAogICAgICAgIHNldmVyaXR5OiBpc3N1ZS5zZXZlcml0eSwKICAgICAgICBwcWVPd25lcjogdGhpcy5wcWVPd25lciwKICAgICAgICBtb250aDogaXNzdWUubW9udGgsCiAgICAgICAgYW5hbHlzaXNUeXBlOiBpc3N1ZS5hbmFseXNpc1R5cGUsCiAgICAgICAgcmVzb2x2ZWQ6IG1hcmtBc1Jlc29sdmVkLAogICAgICAgIG91dHN0YW5kaW5nOiBtYXJrQXNPdXRzdGFuZGluZwogICAgICB9KTsKCiAgICAgIC8vIElmIG1hcmtpbmcgYXMgcmVzb2x2ZWQsIG1vdmUgdGhlIGlzc3VlIHRvIHJlc29sdmVkIGlzc3VlcwogICAgICBpZiAobWFya0FzUmVzb2x2ZWQpIHsKICAgICAgICB0aGlzLm1hcmtJc3N1ZUFzUmVzb2x2ZWQoaXNzdWUpOwogICAgICB9CiAgICAgIC8vIElmIG1hcmtpbmcgYXMgb3V0c3RhbmRpbmcsIG1vdmUgdGhlIGlzc3VlIHRvIG91dHN0YW5kaW5nIGlzc3VlcwogICAgICBlbHNlIGlmIChtYXJrQXNPdXRzdGFuZGluZykgewogICAgICAgIHRoaXMubWFya0lzc3VlQXNPdXRzdGFuZGluZyhpc3N1ZSk7CiAgICAgIH0KICAgICAgZWxzZSB7CiAgICAgICAgLy8gU2hvdyBzdWNjZXNzIG1lc3NhZ2UgZm9yIHJlZ3VsYXIgdXBkYXRlCiAgICAgICAgYWxlcnQoYEFjdGlvbiB0cmFja2VyIHVwZGF0ZWQgZm9yIGlzc3VlOiAke2lzc3VlLmNhdGVnb3J5fWApOwogICAgICB9CiAgICB9LAoKICAgIHZpZXdQZXJmb3JtYW5jZURhdGEoaXNzdWUpIHsKICAgICAgY29uc29sZS5sb2coJ1ZpZXcgcGVyZm9ybWFuY2UgZGF0YSBmb3I6JywgaXNzdWUuY2F0ZWdvcnkpOwoKICAgICAgLy8gSW4gYSByZWFsIGltcGxlbWVudGF0aW9uLCB0aGlzIHdvdWxkIHNob3cgYSBtb2RhbCBvciBjaGFydCB3aXRoIHBlcmZvcm1hbmNlIGRhdGEKICAgICAgLy8gRm9yIG5vdywgd2UnbGwganVzdCBzaG93IGFuIGFsZXJ0IHdpdGggdGhlIGRhdGEKCiAgICAgIGNvbnN0IHBlcmZvcm1hbmNlRGF0YSA9IHRoaXMucGVyZm9ybWFuY2VEYXRhW2lzc3VlLmNhdGVnb3J5XTsKICAgICAgaWYgKHBlcmZvcm1hbmNlRGF0YSAmJiBwZXJmb3JtYW5jZURhdGEubGVuZ3RoID4gMCkgewogICAgICAgIGNvbnN0IHBlcmZvcm1hbmNlVGV4dCA9IHBlcmZvcm1hbmNlRGF0YQogICAgICAgICAgLm1hcChkYXRhID0+IGAke2RhdGEubW9udGh9OiAke2RhdGEueEZhY3Rvcn14YCkKICAgICAgICAgIC5qb2luKCdcbicpOwoKICAgICAgICBhbGVydChgUGVyZm9ybWFuY2UgZGF0YSBmb3IgJHtpc3N1ZS5jYXRlZ29yeX06XG4ke3BlcmZvcm1hbmNlVGV4dH1gKTsKICAgICAgfSBlbHNlIHsKICAgICAgICBhbGVydChgTm8gcGVyZm9ybWFuY2UgZGF0YSBhdmFpbGFibGUgZm9yICR7aXNzdWUuY2F0ZWdvcnl9YCk7CiAgICAgIH0KICAgIH0sCgogICAgdmlld0lzc3VlRGV0YWlscyhpc3N1ZSkgewogICAgICBjb25zb2xlLmxvZygnVmlldyBpc3N1ZSBkZXRhaWxzIGZvcjonLCBpc3N1ZS5jYXRlZ29yeSk7CiAgICAgIC8vIEluIGEgcmVhbCBpbXBsZW1lbnRhdGlvbiwgdGhpcyB3b3VsZCBzaG93IGEgbW9kYWwgb3IgbmF2aWdhdGUgdG8gYSBkZXRhaWxlZCB2aWV3CiAgICAgIGFsZXJ0KGBWaWV3aW5nIGRldGFpbHMgZm9yIGlzc3VlOiAke2lzc3VlLmNhdGVnb3J5fVxuTW9udGg6ICR7aXNzdWUubW9udGh9XG5TZXZlcml0eTogJHtpc3N1ZS5zZXZlcml0eX1cbk11bHRpcGxpZXI6ICR7aXNzdWUuaW5jcmVhc2VNdWx0aXBsaWVyfXhgKTsKICAgIH0sCgogICAgLy8gUm9vdCBDYXVzZSBDaGFydCBtZXRob2RzCiAgICBhc3luYyBsb2FkUm9vdENhdXNlRGF0YSgpIHsKICAgICAgY29uc29sZS5sb2coJ0xvYWRpbmcgcm9vdCBjYXVzZSBjaGFydCBkYXRhIGZvciBQUUUgT3duZXIgRGFzaGJvYXJkJyk7CiAgICAgIHRoaXMuaXNSb290Q2F1c2VEYXRhTG9hZGluZyA9IHRydWU7CgogICAgICB0cnkgewogICAgICAgIC8vIEdldCBhdXRoZW50aWNhdGlvbiBjb25maWcKICAgICAgICBjb25zdCBjb25maWcgPSB0aGlzLmdldEF1dGhDb25maWcoKTsKCiAgICAgICAgLy8gQ2FsY3VsYXRlIGRhdGUgcmFuZ2UgYmFzZWQgb24gc2VsZWN0ZWQgdGltZSByYW5nZQogICAgICAgIGNvbnN0IGVuZERhdGUgPSBuZXcgRGF0ZSgpOwogICAgICAgIGNvbnN0IHN0YXJ0RGF0ZSA9IG5ldyBEYXRlKCk7CiAgICAgICAgY29uc3QgbW9udGhzVG9GZXRjaCA9IHRoaXMucm9vdENhdXNlVGltZVJhbmdlID09PSAnM21vbnRoJyA/IDIgOiA1OyAvLyAzIG9yIDYgbW9udGhzIGluY2x1ZGluZyBjdXJyZW50CiAgICAgICAgc3RhcnREYXRlLnNldE1vbnRoKGVuZERhdGUuZ2V0TW9udGgoKSAtIG1vbnRoc1RvRmV0Y2gpOwoKICAgICAgICBjb25zdCBzdGFydERhdGVTdHIgPSBgJHtzdGFydERhdGUuZ2V0RnVsbFllYXIoKX0tJHtTdHJpbmcoc3RhcnREYXRlLmdldE1vbnRoKCkgKyAxKS5wYWRTdGFydCgyLCAnMCcpfWA7CiAgICAgICAgY29uc3QgZW5kRGF0ZVN0ciA9IGAke2VuZERhdGUuZ2V0RnVsbFllYXIoKX0tJHtTdHJpbmcoZW5kRGF0ZS5nZXRNb250aCgpICsgMSkucGFkU3RhcnQoMiwgJzAnKX1gOwoKICAgICAgICBjb25zb2xlLmxvZyhgRmV0Y2hpbmcgcm9vdCBjYXVzZSBkYXRhIGZvciAke3RoaXMucHFlT3duZXJ9IGZyb20gJHtzdGFydERhdGVTdHJ9IHRvICR7ZW5kRGF0ZVN0cn0sIEdyb3VwOiAke3RoaXMucm9vdENhdXNlU2VsZWN0ZWRHcm91cH1gKTsKCiAgICAgICAgLy8gQ2FsbCB0aGUgbmV3IEFQSSBlbmRwb2ludAogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGktc3RhdGl0Mi9nZXRfcHFlX3Jvb3RfY2F1c2VfYW5hbHlzaXMnLCB7CiAgICAgICAgICBtZXRob2Q6ICdQT1NUJywKICAgICAgICAgIGhlYWRlcnM6IHsKICAgICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJywKICAgICAgICAgICAgLi4uY29uZmlnLmhlYWRlcnMKICAgICAgICAgIH0sCiAgICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7CiAgICAgICAgICAgIHBxZU93bmVyOiB0aGlzLnBxZU93bmVyLAogICAgICAgICAgICBzdGFydERhdGU6IHN0YXJ0RGF0ZVN0ciwKICAgICAgICAgICAgZW5kRGF0ZTogZW5kRGF0ZVN0ciwKICAgICAgICAgICAgc2VsZWN0ZWRHcm91cDogdGhpcy5yb290Q2F1c2VTZWxlY3RlZEdyb3VwID09PSAnYWxsJyA/IG51bGwgOiB0aGlzLnJvb3RDYXVzZVNlbGVjdGVkR3JvdXAKICAgICAgICAgIH0pCiAgICAgICAgfSk7CgogICAgICAgIGlmICghcmVzcG9uc2Uub2spIHsKICAgICAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGZldGNoIHJvb3QgY2F1c2UgZGF0YTogJHtyZXNwb25zZS5zdGF0dXN9ICR7cmVzcG9uc2Uuc3RhdHVzVGV4dH1gKTsKICAgICAgICB9CgogICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7CgogICAgICAgIGlmIChkYXRhLnN0YXR1c19yZXMgPT09ICdzdWNjZXNzJykgewogICAgICAgICAgLy8gVHJhbnNmb3JtIHRoZSBBUEkgZGF0YSB0byBjaGFydCBmb3JtYXQKICAgICAgICAgIHRoaXMucm9vdENhdXNlQ2hhcnREYXRhID0gdGhpcy50cmFuc2Zvcm1Sb290Q2F1c2VEYXRhKGRhdGEuY2F0ZWdvcnlEYXRhKTsKICAgICAgICAgIGNvbnNvbGUubG9nKCdSb290IGNhdXNlIGNoYXJ0IGRhdGEgbG9hZGVkOicsIGRhdGEuY2F0ZWdvcnlEYXRhKTsKICAgICAgICAgIGNvbnNvbGUubG9nKCdDaGFydCBkYXRhIGxlbmd0aDonLCB0aGlzLnJvb3RDYXVzZUNoYXJ0RGF0YS5sZW5ndGgpOwogICAgICAgICAgY29uc29sZS5sb2coJ1NhbXBsZSBkYXRhIHBvaW50OicsIHRoaXMucm9vdENhdXNlQ2hhcnREYXRhWzBdKTsKICAgICAgICAgIGNvbnNvbGUubG9nKCdCcmVha291dCBncm91cHM6JywgZGF0YS5icmVha291dEdyb3Vwcyk7CiAgICAgICAgICBjb25zb2xlLmxvZygnUGFydCBudW1iZXJzOicsIGRhdGEucGFydE51bWJlcnMpOwoKICAgICAgICAgIGNvbnNvbGUubG9nKCdDaGFydCBkYXRhIGxvYWRlZCwgd2F0Y2hlciBzaG91bGQgaGFuZGxlIHVwZGF0ZScpOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gbG9hZCByb290IGNhdXNlIGRhdGE6JywgZGF0YS5zcWxfZXJyb3JfbXNnIHx8IGRhdGEubWVzc2FnZSk7CiAgICAgICAgICAvLyBGYWxsIGJhY2sgdG8gbW9jayBkYXRhCiAgICAgICAgICB0aGlzLnJvb3RDYXVzZUNoYXJ0RGF0YSA9IHRoaXMuZ2VuZXJhdGVNb2NrUm9vdENhdXNlRGF0YSgpOwogICAgICAgICAgY29uc29sZS5sb2coJ1VzaW5nIG1vY2sgZGF0YTonLCB0aGlzLnJvb3RDYXVzZUNoYXJ0RGF0YS5sZW5ndGgpOwogICAgICAgIH0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBsb2FkaW5nIHJvb3QgY2F1c2UgZGF0YTonLCBlcnJvcik7CiAgICAgICAgLy8gRmFsbCBiYWNrIHRvIG1vY2sgZGF0YQogICAgICAgIHRoaXMucm9vdENhdXNlQ2hhcnREYXRhID0gdGhpcy5nZW5lcmF0ZU1vY2tSb290Q2F1c2VEYXRhKCk7CiAgICAgIH0gZmluYWxseSB7CiAgICAgICAgdGhpcy5pc1Jvb3RDYXVzZURhdGFMb2FkaW5nID0gZmFsc2U7CiAgICAgIH0KICAgIH0sCgogICAgdHJhbnNmb3JtUm9vdENhdXNlRGF0YShjYXRlZ29yeURhdGEpIHsKICAgICAgLy8gVHJhbnNmb3JtIHRoZSBBUEkgcmVzcG9uc2UgaW50byBjaGFydCBmb3JtYXQKICAgICAgY29uc3QgY2hhcnREYXRhID0gW107CgogICAgICAvLyBjYXRlZ29yeURhdGEgc3RydWN0dXJlOiB7ICJjYXRlZ29yeSI6IHsgIjIwMjQtMDMiOiB7IGRlZmVjdHM6IDUsIHZvbHVtZTogMTAwMCwgZmFpbFJhdGU6IDAuNSB9LCAuLi4gfSwgLi4uIH0KICAgICAgZm9yIChjb25zdCBbY2F0ZWdvcnksIG1vbnRoRGF0YV0gb2YgT2JqZWN0LmVudHJpZXMoY2F0ZWdvcnlEYXRhKSkgewogICAgICAgIC8vIENsZWFuIHVwIGNhdGVnb3J5IG5hbWUgKHRyaW0gd2hpdGVzcGFjZSkKICAgICAgICBjb25zdCBjbGVhbkNhdGVnb3J5ID0gY2F0ZWdvcnkudHJpbSgpOwogICAgICAgIGNvbnNvbGUubG9nKCJ0cmltIGNhdCIsIGNsZWFuQ2F0ZWdvcnkpCgogICAgICAgIGZvciAoY29uc3QgW21vbnRoLCBkYXRhXSBvZiBPYmplY3QuZW50cmllcyhtb250aERhdGEpKSB7CiAgICAgICAgICBjaGFydERhdGEucHVzaCh7CiAgICAgICAgICAgIGdyb3VwOiBjbGVhbkNhdGVnb3J5LAogICAgICAgICAgICBrZXk6IG1vbnRoLAogICAgICAgICAgICB2YWx1ZTogcGFyc2VGbG9hdChkYXRhLmZhaWxSYXRlLnRvRml4ZWQoMikpCiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0KCiAgICAgIGNvbnNvbGUubG9nKCdUcmFuc2Zvcm1lZCByb290IGNhdXNlIGRhdGE6JywgY2hhcnREYXRhKTsKICAgICAgY29uc29sZS5sb2coJ1VuaXF1ZSBncm91cHMgaW4gZGF0YTonLCBbLi4ubmV3IFNldChjaGFydERhdGEubWFwKGQgPT4gZC5ncm91cCkpXSk7CiAgICAgIGNvbnNvbGUubG9nKCdVbmlxdWUga2V5cyBpbiBkYXRhOicsIFsuLi5uZXcgU2V0KGNoYXJ0RGF0YS5tYXAoZCA9PiBkLmtleSkpXSk7CiAgICAgIGNvbnNvbGUubG9nKCdWYWx1ZSByYW5nZTonLCBNYXRoLm1pbiguLi5jaGFydERhdGEubWFwKGQgPT4gZC52YWx1ZSkpLCAndG8nLCBNYXRoLm1heCguLi5jaGFydERhdGEubWFwKGQgPT4gZC52YWx1ZSkpKTsKICAgICAgcmV0dXJuIGNoYXJ0RGF0YTsKICAgIH0sCgogICAgZ2VuZXJhdGVNb2NrUm9vdENhdXNlRGF0YSgpIHsKICAgICAgLy8gVXNlIHRoZSBhY3R1YWwgYnJlYWtvdXQgZ3JvdXBzIGZvciB0aGlzIFBRRSBvd25lciBpbnN0ZWFkIG9mIGdlbmVyaWMgY2F0ZWdvcmllcwogICAgICBsZXQgY2F0ZWdvcmllcyA9IFtdOwoKICAgICAgaWYgKHRoaXMuYnJlYWtvdXRHcm91cHMgJiYgdGhpcy5icmVha291dEdyb3Vwcy5sZW5ndGggPiAwKSB7CiAgICAgICAgLy8gVXNlIHRoZSBhY3R1YWwgYnJlYWtvdXQgZ3JvdXBzIGZvciB0aGlzIFBRRSBvd25lcgogICAgICAgIGNhdGVnb3JpZXMgPSB0aGlzLmJyZWFrb3V0R3JvdXBzLm1hcChncm91cCA9PiBncm91cC5uYW1lKTsKCiAgICAgICAgLy8gSWYgYSBzcGVjaWZpYyBncm91cCBpcyBzZWxlY3RlZCwgZmlsdGVyIHRvIG9ubHkgdGhhdCBncm91cAogICAgICAgIGlmICh0aGlzLnJvb3RDYXVzZVNlbGVjdGVkR3JvdXAgIT09ICdhbGwnKSB7CiAgICAgICAgICBjYXRlZ29yaWVzID0gY2F0ZWdvcmllcy5maWx0ZXIoY2F0ZWdvcnkgPT4gY2F0ZWdvcnkgPT09IHRoaXMucm9vdENhdXNlU2VsZWN0ZWRHcm91cCk7CiAgICAgICAgfQogICAgICB9IGVsc2UgewogICAgICAgIC8vIEZhbGxiYWNrIHRvIHNhbXBsZSBkYXRhIGlmIGJyZWFrb3V0IGdyb3VwcyBhcmVuJ3QgbG9hZGVkIHlldAogICAgICAgIGlmICh0aGlzLnBxZU93bmVyID09PSAnQWxiZXJ0IEcuJykgewogICAgICAgICAgY2F0ZWdvcmllcyA9IFsnRmFuIFRoZW1pcycsICdWaWN0b3JpYSBDcnlwdG8nLCAnUXVhbnR1bSBOZXh1cyddOwogICAgICAgIH0gZWxzZSBpZiAodGhpcy5wcWVPd25lciA9PT0gJ1NhcmFoIEwuJykgewogICAgICAgICAgY2F0ZWdvcmllcyA9IFsnU3RlbGxhciBDb3JlJywgJ05lYnVsYSBEcml2ZSddOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICBjYXRlZ29yaWVzID0gWydTYW1wbGUgR3JvdXAgMScsICdTYW1wbGUgR3JvdXAgMiddOwogICAgICAgIH0KCiAgICAgICAgLy8gSWYgYSBzcGVjaWZpYyBncm91cCBpcyBzZWxlY3RlZCwgZmlsdGVyIHRvIG9ubHkgdGhhdCBncm91cAogICAgICAgIGlmICh0aGlzLnJvb3RDYXVzZVNlbGVjdGVkR3JvdXAgIT09ICdhbGwnKSB7CiAgICAgICAgICBjYXRlZ29yaWVzID0gY2F0ZWdvcmllcy5maWx0ZXIoY2F0ZWdvcnkgPT4gY2F0ZWdvcnkgPT09IHRoaXMucm9vdENhdXNlU2VsZWN0ZWRHcm91cCk7CiAgICAgICAgfQogICAgICB9CgogICAgICAvLyBHZW5lcmF0ZSBtb250aHMgYmFzZWQgb24gc2VsZWN0ZWQgdGltZSByYW5nZQogICAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpOwogICAgICBjb25zdCBtb250aHMgPSBbXTsKICAgICAgY29uc3QgbW9udGhzVG9HZW5lcmF0ZSA9IHRoaXMucm9vdENhdXNlVGltZVJhbmdlID09PSAnM21vbnRoJyA/IDMgOiA2OwoKICAgICAgZm9yIChsZXQgaSA9IG1vbnRoc1RvR2VuZXJhdGUgLSAxOyBpID49IDA7IGktLSkgewogICAgICAgIGNvbnN0IGRhdGUgPSBuZXcgRGF0ZShub3cuZ2V0RnVsbFllYXIoKSwgbm93LmdldE1vbnRoKCkgLSBpLCAxKTsKICAgICAgICBjb25zdCB5ZWFyID0gZGF0ZS5nZXRGdWxsWWVhcigpOwogICAgICAgIGNvbnN0IG1vbnRoID0gU3RyaW5nKGRhdGUuZ2V0TW9udGgoKSArIDEpLnBhZFN0YXJ0KDIsICcwJyk7CiAgICAgICAgbW9udGhzLnB1c2goYCR7eWVhcn0tJHttb250aH1gKTsKICAgICAgfQoKICAgICAgY29uc3QgZGF0YSA9IFtdOwoKICAgICAgbW9udGhzLmZvckVhY2gobW9udGggPT4gewogICAgICAgIGNhdGVnb3JpZXMuZm9yRWFjaChjYXRlZ29yeSA9PiB7CiAgICAgICAgICAvLyBHZW5lcmF0ZSByZWFsaXN0aWMgZmFpbCByYXRlIGRhdGEgKDAtNSUgcmFuZ2UpCiAgICAgICAgICBjb25zdCBiYXNlUmF0ZSA9IE1hdGgucmFuZG9tKCkgKiAzICsgMC41OyAvLyAwLjUlIHRvIDMuNSUKICAgICAgICAgIGNvbnN0IHZhcmlhdGlvbiA9IChNYXRoLnJhbmRvbSgpIC0gMC41KSAqIDE7IC8vIMKxMC41JQogICAgICAgICAgY29uc3QgZmFpbFJhdGUgPSBNYXRoLm1heCgwLjEsIGJhc2VSYXRlICsgdmFyaWF0aW9uKTsKCiAgICAgICAgICBkYXRhLnB1c2goewogICAgICAgICAgICBncm91cDogY2F0ZWdvcnksCiAgICAgICAgICAgIGtleTogbW9udGgsCiAgICAgICAgICAgIHZhbHVlOiBwYXJzZUZsb2F0KGZhaWxSYXRlLnRvRml4ZWQoMikpCiAgICAgICAgICB9KTsKICAgICAgICB9KTsKICAgICAgfSk7CgogICAgICByZXR1cm4gZGF0YTsKICAgIH0sCgogICAgaGFuZGxlUm9vdENhdXNlQmFyQ2xpY2soZXZlbnQpIHsKICAgICAgaWYgKGV2ZW50ICYmIGV2ZW50LmRhdGEpIHsKICAgICAgICBjb25zdCBjbGlja2VkRGF0YSA9IGV2ZW50LmRhdGE7CiAgICAgICAgY29uc29sZS5sb2coYFJvb3QgY2F1c2UgYmFyIGNsaWNrZWQgZm9yIGNhdGVnb3J5OiAke2NsaWNrZWREYXRhLmdyb3VwfSwgbW9udGg6ICR7Y2xpY2tlZERhdGEua2V5fWApOwoKICAgICAgICAvLyBZb3UgY2FuIGltcGxlbWVudCBhZGRpdGlvbmFsIGZ1bmN0aW9uYWxpdHkgaGVyZSwgc3VjaCBhcyBzaG93aW5nIG1vcmUgZGV0YWlscwogICAgICAgIC8vIG9yIG5hdmlnYXRpbmcgdG8gdGhlIE1ldGlzWEZhY3RvcnMgR3JvdXAgdGFiIHdpdGggdGhpcyBzcGVjaWZpYyBjYXRlZ29yeQogICAgICAgIGFsZXJ0KGBDbGlja2VkIG9uICR7Y2xpY2tlZERhdGEuZ3JvdXB9IGZvciAke2NsaWNrZWREYXRhLmtleX1cbkZhaWwgUmF0ZTogJHtjbGlja2VkRGF0YS52YWx1ZX0lYCk7CiAgICAgIH0KICAgIH0sCgogICAgaGFuZGxlUm9vdENhdXNlVGltZVJhbmdlQ2hhbmdlKCkgewogICAgICBjb25zb2xlLmxvZyhgUm9vdCBjYXVzZSB0aW1lIHJhbmdlIGNoYW5nZWQgdG86ICR7dGhpcy5yb290Q2F1c2VUaW1lUmFuZ2V9YCk7CiAgICAgIHRoaXMubG9hZFJvb3RDYXVzZURhdGEoKTsKICAgIH0sCgogICAgaGFuZGxlUm9vdENhdXNlVmlld0J5Q2hhbmdlKCkgewogICAgICBjb25zb2xlLmxvZyhgUm9vdCBjYXVzZSB0aW1lIHJhbmdlIGNoYW5nZWQgdG86ICR7dGhpcy5yb290Q2F1c2VUaW1lUmFuZ2V9YCk7CiAgICAgIHRoaXMubG9hZFJvb3RDYXVzZURhdGEoKTsKICAgIH0sCgogICAgaGFuZGxlUm9vdENhdXNlR3JvdXBDaGFuZ2UoKSB7CiAgICAgIGNvbnNvbGUubG9nKGBSb290IGNhdXNlIGdyb3VwIGNoYW5nZWQgdG86ICR7dGhpcy5yb290Q2F1c2VTZWxlY3RlZEdyb3VwfWApOwogICAgICBjb25zb2xlLmxvZygnQ3VycmVudCBjaGFydCBkYXRhIGxlbmd0aCBiZWZvcmUgcmVsb2FkOicsIHRoaXMucm9vdENhdXNlQ2hhcnREYXRhLmxlbmd0aCk7CiAgICAgIHRoaXMubG9hZFJvb3RDYXVzZURhdGEoKTsKICAgIH0sCgogICAgZ2V0QXV0aENvbmZpZygpIHsKICAgICAgLy8gR2V0IGF1dGhlbnRpY2F0aW9uIHRva2VuIGZyb20gbG9jYWxTdG9yYWdlIG9yIFZ1ZXggc3RvcmUKICAgICAgY29uc3QgdG9rZW4gPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgndG9rZW4nKSB8fCB0aGlzLiRzdG9yZS5zdGF0ZS50b2tlbjsKCiAgICAgIHJldHVybiB7CiAgICAgICAgaGVhZGVyczogewogICAgICAgICAgJ0F1dGhvcml6YXRpb24nOiBgQmVhcmVyICR7dG9rZW59YAogICAgICAgIH0KICAgICAgfTsKICAgIH0sCgogICAgLy8gTmF2aWdhdGUgdG8gQWN0aW9uIFRyYWNrZXIKICAgIGdvVG9BY3Rpb25UcmFja2VyKCkgewogICAgICBjb25zb2xlLmxvZygnTmF2aWdhdGluZyB0byBBY3Rpb24gVHJhY2tlci4uLicpOwogICAgICB0aGlzLiRyb3V0ZXIucHVzaCgnL2FjdGlvbi10cmFja2VyJyk7CiAgICB9LAoKICAgIC8vIE5hdmlnYXRlIHRvIFZhbGlkYXRpb24gUGFnZQogICAgZ29Ub1ZhbGlkYXRpb25QYWdlKCkgewogICAgICBjb25zb2xlLmxvZygnTmF2aWdhdGluZyB0byBWYWxpZGF0aW9uIFBhZ2UuLi4nKTsKICAgICAgdGhpcy4kcm91dGVyLnB1c2goJy9kZWZlY3QtdmFsaWRhdGlvbnMnKTsKICAgIH0sCgogICAgLy8gTmF2aWdhdGUgdG8gSGVhdG1hcCB0YWIgaW4gTWV0aXMgWEZhY3RvcnMKICAgIGdvVG9IZWF0bWFwKCkgewogICAgICBjb25zb2xlLmxvZygnTmF2aWdhdGluZyB0byBNZXRpcyBYRmFjdG9ycyBIZWF0bWFwLi4uJyk7CiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKCcvbWV0aXMteGZhY3RvcnM/dGFiPWhlYXRtYXAnKTsKICAgIH0sCgogICAgLy8gU2Nyb2xsIHRvIGFsZXJ0cyBzZWN0aW9uIChhY3Rpb24gdHJhY2tlciBhbGVydHMgc2VjdGlvbikKICAgIHNjcm9sbFRvQWxlcnRzKCkgewogICAgICBjb25zb2xlLmxvZygnU2Nyb2xsaW5nIHRvIGFjdGlvbiB0cmFja2VyIGFsZXJ0cyBzZWN0aW9uLi4uJyk7CiAgICAgIC8vIFNjcm9sbCB0byB0aGUgQWN0aW9uIFRyYWNrZXIgQWxlcnRzIHNlY3Rpb24KICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICAgIGNvbnN0IGFjdGlvblRyYWNrZXJTZWN0aW9uID0gZG9jdW1lbnQucXVlcnlTZWxlY3RvcignLnNlY3Rpb24tY2FyZDpmaXJzdC1jaGlsZCcpOyAvLyBGaXJzdCBzZWN0aW9uIGNhcmQgKEFjdGlvbiBUcmFja2VyIEFsZXJ0cykKICAgICAgICBpZiAoYWN0aW9uVHJhY2tlclNlY3Rpb24pIHsKICAgICAgICAgIGFjdGlvblRyYWNrZXJTZWN0aW9uLnNjcm9sbEludG9WaWV3KHsKICAgICAgICAgICAgYmVoYXZpb3I6ICdzbW9vdGgnLAogICAgICAgICAgICBibG9jazogJ3N0YXJ0JwogICAgICAgICAgfSk7CgogICAgICAgICAgLy8gQWRkIGEgYnJpZWYgaGlnaGxpZ2h0IGVmZmVjdAogICAgICAgICAgYWN0aW9uVHJhY2tlclNlY3Rpb24uc3R5bGUudHJhbnNpdGlvbiA9ICdib3gtc2hhZG93IDAuM3MgZWFzZSc7CiAgICAgICAgICBhY3Rpb25UcmFja2VyU2VjdGlvbi5zdHlsZS5ib3hTaGFkb3cgPSAnMCAwIDIwcHggcmdiYSgwLCA5OCwgMjU1LCAwLjUpJzsKCiAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHsKICAgICAgICAgICAgYWN0aW9uVHJhY2tlclNlY3Rpb24uc3R5bGUuYm94U2hhZG93ID0gJzAgMnB4IDZweCByZ2JhKDAsIDAsIDAsIDAuMiknOwogICAgICAgICAgfSwgMjAwMCk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCgogICAgLy8gVG9nZ2xlIEFjdGlvbiBUcmFja2VyIEFsZXJ0cyBzZWN0aW9uCiAgICB0b2dnbGVBY3Rpb25UcmFja2VyQWxlcnRzRXhwYW5kZWQoKSB7CiAgICAgIHRoaXMuaXNBY3Rpb25UcmFja2VyQWxlcnRzRXhwYW5kZWQgPSAhdGhpcy5pc0FjdGlvblRyYWNrZXJBbGVydHNFeHBhbmRlZDsKICAgICAgY29uc29sZS5sb2coJ0FjdGlvbiBUcmFja2VyIEFsZXJ0cyBleHBhbmRlZDonLCB0aGlzLmlzQWN0aW9uVHJhY2tlckFsZXJ0c0V4cGFuZGVkKTsKICAgIH0sCgogICAgLy8gVG9nZ2xlIEluLVByb2dyZXNzIEFsZXJ0cyBzdWJzZWN0aW9uCiAgICB0b2dnbGVJblByb2dyZXNzQWxlcnRzRXhwYW5kZWQoKSB7CiAgICAgIHRoaXMuaXNJblByb2dyZXNzQWxlcnRzRXhwYW5kZWQgPSAhdGhpcy5pc0luUHJvZ3Jlc3NBbGVydHNFeHBhbmRlZDsKICAgICAgY29uc29sZS5sb2coJ0luLVByb2dyZXNzIEFsZXJ0cyBleHBhbmRlZDonLCB0aGlzLmlzSW5Qcm9ncmVzc0FsZXJ0c0V4cGFuZGVkKTsKICAgIH0sCgogICAgLy8gVG9nZ2xlIE91dHN0YW5kaW5nIEFsZXJ0cyBzdWJzZWN0aW9uCiAgICB0b2dnbGVPdXRzdGFuZGluZ0FsZXJ0c0V4cGFuZGVkKCkgewogICAgICB0aGlzLmlzT3V0c3RhbmRpbmdBbGVydHNFeHBhbmRlZCA9ICF0aGlzLmlzT3V0c3RhbmRpbmdBbGVydHNFeHBhbmRlZDsKICAgICAgY29uc29sZS5sb2coJ091dHN0YW5kaW5nIEFsZXJ0cyBleHBhbmRlZDonLCB0aGlzLmlzT3V0c3RhbmRpbmdBbGVydHNFeHBhbmRlZCk7CiAgICB9LAoKICAgIC8vIFRvZ2dsZSBSZXNvbHZlZCBBbGVydHMgc3Vic2VjdGlvbgogICAgdG9nZ2xlUmVzb2x2ZWRBbGVydHNFeHBhbmRlZCgpIHsKICAgICAgdGhpcy5pc1Jlc29sdmVkQWxlcnRzRXhwYW5kZWQgPSAhdGhpcy5pc1Jlc29sdmVkQWxlcnRzRXhwYW5kZWQ7CiAgICAgIGNvbnNvbGUubG9nKCdSZXNvbHZlZCBBbGVydHMgZXhwYW5kZWQ6JywgdGhpcy5pc1Jlc29sdmVkQWxlcnRzRXhwYW5kZWQpOwogICAgfSwKCiAgICAvLyBWaWV3IEFjdGlvbiBUcmFja2VyIEFsZXJ0IC0gb3BlbnMgdHJhY2tpbmcgbW9kYWwKICAgIGFzeW5jIHZpZXdBY3Rpb25UcmFja2VyQWxlcnQoYWxlcnQpIHsKICAgICAgY29uc29sZS5sb2coJ09wZW5pbmcgdHJhY2tpbmcgbW9kYWwgZm9yIGFsZXJ0OicsIGFsZXJ0KTsKCiAgICAgIC8vIENyZWF0ZSBhIG1vY2sgYWN0aW9uIHRyYWNrZXIgaXRlbSBmcm9tIHRoZSBhbGVydCBkYXRhCiAgICAgIHRoaXMuc2VsZWN0ZWRUcmFja2luZ0l0ZW0gPSB7CiAgICAgICAgaWQ6IGFsZXJ0LmFjdGlvblRyYWNrZXJJZCB8fCBhbGVydC5pZCwKICAgICAgICBwbjogYWxlcnQuY2F0ZWdvcnkgfHwgJ1Vua25vd24nLAogICAgICAgIGdyb3VwOiBhbGVydC5jYXRlZ29yeSB8fCAnVW5rbm93bicsCiAgICAgICAgc3RhdHVzOiBhbGVydC5zdGF0dXMgfHwgJ1Vua25vd24nLAogICAgICAgIHByaW9yaXR5OiBhbGVydC5zZXZlcml0eSB8fCAnTWVkaXVtJywKICAgICAgICBwcm9ncmVzczogMCwKICAgICAgICBjb21tb2RpdHk6ICdVbmtub3duJywKICAgICAgICBhc3NpZ25lZTogJ1Vua25vd24nLAogICAgICAgIGFjdGlvbkl0ZW1zOiBbXQogICAgICB9OwoKICAgICAgLy8gTG9hZCBhbGVydCBkYXRhCiAgICAgIGF3YWl0IHRoaXMubG9hZEFsZXJ0VXBkYXRlcyh0aGlzLnNlbGVjdGVkVHJhY2tpbmdJdGVtKTsKICAgICAgYXdhaXQgdGhpcy5sb2FkQWlJbnNpZ2h0KHRoaXMuc2VsZWN0ZWRUcmFja2luZ0l0ZW0sIGFsZXJ0KTsKICAgICAgYXdhaXQgdGhpcy5sb2FkQWxlcnRIaXN0b3J5RGF0YSh0aGlzLnNlbGVjdGVkVHJhY2tpbmdJdGVtKTsKCiAgICAgIC8vIE9wZW4gdGhlIG1vZGFsCiAgICAgIHRoaXMudHJhY2tpbmdNb2RhbFZpc2libGUgPSB0cnVlOwogICAgfSwKCiAgICAvLyBBZGQgY3JpdGljYWwgaXNzdWUgdG8gYWN0aW9uIHRyYWNrZXIKICAgIGFzeW5jIGFkZENyaXRpY2FsSXNzdWVUb0FjdGlvblRyYWNrZXIoaXNzdWUpIHsKICAgICAgY29uc29sZS5sb2coJ0FkZGluZyBjcml0aWNhbCBpc3N1ZSB0byBhY3Rpb24gdHJhY2tlcjonLCBpc3N1ZSk7CgogICAgICB0cnkgewogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGktc3RhdGl0Mi91cGRhdGVfcHFlX2FjdGlvbicsIHsKICAgICAgICAgIG1ldGhvZDogJ1BPU1QnLAogICAgICAgICAgaGVhZGVyczogewogICAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLAogICAgICAgICAgICAuLi50aGlzLmdldEF1dGhDb25maWcoKS5oZWFkZXJzCiAgICAgICAgICB9LAogICAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoewogICAgICAgICAgICBpc3N1ZUlkOiBpc3N1ZS5pZCwKICAgICAgICAgICAgY2F0ZWdvcnk6IGlzc3VlLmNhdGVnb3J5LAogICAgICAgICAgICBjb21tZW50OiBpc3N1ZS5jb21tZW50IHx8IGBDcml0aWNhbCBpc3N1ZTogJHtpc3N1ZS5haURlc2NyaXB0aW9ufWAsCiAgICAgICAgICAgIHNldmVyaXR5OiBpc3N1ZS5zZXZlcml0eSwKICAgICAgICAgICAgcHFlT3duZXI6IHRoaXMucHFlT3duZXIsCiAgICAgICAgICAgIG1vbnRoOiBpc3N1ZS5tb250aCwKICAgICAgICAgICAgYW5hbHlzaXNUeXBlOiBpc3N1ZS5hbmFseXNpc1R5cGUKICAgICAgICAgIH0pCiAgICAgICAgfSk7CgogICAgICAgIGlmICghcmVzcG9uc2Uub2spIHsKICAgICAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGFkZCB0byBhY3Rpb24gdHJhY2tlcjogJHtyZXNwb25zZS5zdGF0dXN9YCk7CiAgICAgICAgfQoKICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpOwogICAgICAgIGlmIChkYXRhLnN0YXR1c19yZXMgPT09ICdzdWNjZXNzJykgewogICAgICAgICAgYWxlcnQoYENyaXRpY2FsIGlzc3VlICIke2lzc3VlLmNhdGVnb3J5fSIgaGFzIGJlZW4gYWRkZWQgdG8gdGhlIGFjdGlvbiB0cmFja2VyIHdpdGggSUQ6ICR7ZGF0YS5hY3Rpb25JZH1gKTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhyb3cgbmV3IEVycm9yKGRhdGEubWVzc2FnZSB8fCAnRmFpbGVkIHRvIGFkZCB0byBhY3Rpb24gdHJhY2tlcicpOwogICAgICAgIH0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBhZGRpbmcgY3JpdGljYWwgaXNzdWUgdG8gYWN0aW9uIHRyYWNrZXI6JywgZXJyb3IpOwogICAgICAgIGFsZXJ0KCdGYWlsZWQgdG8gYWRkIGNyaXRpY2FsIGlzc3VlIHRvIGFjdGlvbiB0cmFja2VyOiAnICsgZXJyb3IubWVzc2FnZSk7CiAgICAgIH0KICAgIH0sCgogICAgLy8gTG9hZCBhbGVydCB1cGRhdGVzIGZvciB0aGUgc2VsZWN0ZWQgaXRlbQogICAgYXN5bmMgbG9hZEFsZXJ0VXBkYXRlcyhpdGVtKSB7CiAgICAgIHRyeSB7CiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS1zdGF0aXQyL2dldF9wcWVfYWxlcnRfaGlzdG9yeScsIHsKICAgICAgICAgIG1ldGhvZDogJ1BPU1QnLAogICAgICAgICAgaGVhZGVyczogewogICAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLAogICAgICAgICAgICAuLi50aGlzLmdldEF1dGhDb25maWcoKS5oZWFkZXJzCiAgICAgICAgICB9LAogICAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoewogICAgICAgICAgICBhY3Rpb25UcmFja2VySWQ6IGl0ZW0uaWQsCiAgICAgICAgICAgIHBxZU93bmVyOiB0aGlzLnBxZU93bmVyCiAgICAgICAgICB9KQogICAgICAgIH0pOwoKICAgICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7CiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byBmZXRjaCBhbGVydCBoaXN0b3J5OiAke3Jlc3BvbnNlLnN0YXR1c31gKTsKICAgICAgICB9CgogICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7CiAgICAgICAgaWYgKGRhdGEuc3RhdHVzX3JlcyA9PT0gJ3N1Y2Nlc3MnKSB7CiAgICAgICAgICB0aGlzLmFsZXJ0VXBkYXRlcyA9IGRhdGEuYWxlcnRfaGlzdG9yeSB8fCBbXTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGxvYWQgYWxlcnQgdXBkYXRlczonLCBkYXRhLm1lc3NhZ2UpOwogICAgICAgICAgdGhpcy5hbGVydFVwZGF0ZXMgPSBbXTsKICAgICAgICB9CiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgbG9hZGluZyBhbGVydCB1cGRhdGVzOicsIGVycm9yKTsKICAgICAgICB0aGlzLmFsZXJ0VXBkYXRlcyA9IFtdOwogICAgICB9CiAgICB9LAoKICAgIC8vIExvYWQgQUkgaW5zaWdodCBmb3IgdGhlIHNlbGVjdGVkIGl0ZW0KICAgIGFzeW5jIGxvYWRBaUluc2lnaHQoaXRlbSwgYWxlcnREYXRhID0gbnVsbCkgewogICAgICB0aGlzLmlzTG9hZGluZ0FpSW5zaWdodCA9IHRydWU7CiAgICAgIHRyeSB7CiAgICAgICAgY29uc3QgYWxlcnRJbmZvID0gYWxlcnREYXRhIHx8IHsKICAgICAgICAgIGNhdGVnb3J5OiBpdGVtLmdyb3VwIHx8ICdVbmtub3duJywKICAgICAgICAgIHNldmVyaXR5OiBpdGVtLnByaW9yaXR5IHx8ICdNZWRpdW0nLAogICAgICAgICAgeEZhY3RvcjogJzEuNScsCiAgICAgICAgICBzdGF0dXM6IGl0ZW0uc3RhdHVzIHx8ICdVbmtub3duJwogICAgICAgIH07CgogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGktc3RhdGl0Mi9nZXRfcHFlX2FsZXJ0X2FpX2luc2lnaHQnLCB7CiAgICAgICAgICBtZXRob2Q6ICdQT1NUJywKICAgICAgICAgIGhlYWRlcnM6IHsKICAgICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJywKICAgICAgICAgICAgLi4udGhpcy5nZXRBdXRoQ29uZmlnKCkuaGVhZGVycwogICAgICAgICAgfSwKICAgICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHsKICAgICAgICAgICAgYWN0aW9uVHJhY2tlcklkOiBpdGVtLmlkLAogICAgICAgICAgICBhbGVydERhdGE6IGFsZXJ0SW5mbywKICAgICAgICAgICAgcHFlT3duZXI6IHRoaXMucHFlT3duZXIKICAgICAgICAgIH0pCiAgICAgICAgfSk7CgogICAgICAgIGlmICghcmVzcG9uc2Uub2spIHsKICAgICAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGZldGNoIEFJIGluc2lnaHQ6ICR7cmVzcG9uc2Uuc3RhdHVzfWApOwogICAgICAgIH0KCiAgICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTsKICAgICAgICBpZiAoZGF0YS5zdGF0dXNfcmVzID09PSAnc3VjY2VzcycpIHsKICAgICAgICAgIHRoaXMuYWlJbnNpZ2h0ID0gZGF0YS5haV9pbnNpZ2h0IHx8ICdObyBpbnNpZ2h0IGF2YWlsYWJsZSc7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBsb2FkIEFJIGluc2lnaHQ6JywgZGF0YS5tZXNzYWdlKTsKICAgICAgICAgIHRoaXMuYWlJbnNpZ2h0ID0gJ1VuYWJsZSB0byBnZW5lcmF0ZSBpbnNpZ2h0IGF0IHRoaXMgdGltZSc7CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGxvYWRpbmcgQUkgaW5zaWdodDonLCBlcnJvcik7CiAgICAgICAgdGhpcy5haUluc2lnaHQgPSAnVW5hYmxlIHRvIGdlbmVyYXRlIGluc2lnaHQgYXQgdGhpcyB0aW1lJzsKICAgICAgfSBmaW5hbGx5IHsKICAgICAgICB0aGlzLmlzTG9hZGluZ0FpSW5zaWdodCA9IGZhbHNlOwogICAgICB9CiAgICB9LAoKICAgIC8vIEFkZCBhbGVydCB1cGRhdGUKICAgIGFzeW5jIGFkZEFsZXJ0VXBkYXRlKCkgewogICAgICBpZiAoIXRoaXMubmV3QWxlcnRVcGRhdGUudHJpbSgpKSB7CiAgICAgICAgYWxlcnQoJ1BsZWFzZSBlbnRlciBhbiB1cGRhdGUnKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KCiAgICAgIHRyeSB7CiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS1zdGF0aXQyL2FkZF9wcWVfYWxlcnRfdXBkYXRlJywgewogICAgICAgICAgbWV0aG9kOiAnUE9TVCcsCiAgICAgICAgICBoZWFkZXJzOiB7CiAgICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsCiAgICAgICAgICAgIC4uLnRoaXMuZ2V0QXV0aENvbmZpZygpLmhlYWRlcnMKICAgICAgICAgIH0sCiAgICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7CiAgICAgICAgICAgIGFjdGlvblRyYWNrZXJJZDogdGhpcy5zZWxlY3RlZFRyYWNraW5nSXRlbS5pZCwKICAgICAgICAgICAgdXBkYXRlOiB0aGlzLm5ld0FsZXJ0VXBkYXRlLAogICAgICAgICAgICB1cGRhdGVkQnk6IHRoaXMucHFlT3duZXIsCiAgICAgICAgICAgIGFsZXJ0VHlwZTogJ1BRRSBVcGRhdGUnCiAgICAgICAgICB9KQogICAgICAgIH0pOwoKICAgICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7CiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byBhZGQgYWxlcnQgdXBkYXRlOiAke3Jlc3BvbnNlLnN0YXR1c31gKTsKICAgICAgICB9CgogICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7CiAgICAgICAgaWYgKGRhdGEuc3RhdHVzX3JlcyA9PT0gJ3N1Y2Nlc3MnKSB7CiAgICAgICAgICAvLyBBZGQgdGhlIG5ldyBhbGVydCB0byB0aGUgaGlzdG9yeQogICAgICAgICAgdGhpcy5hbGVydFVwZGF0ZXMucHVzaChkYXRhLmFsZXJ0KTsKICAgICAgICAgIHRoaXMubmV3QWxlcnRVcGRhdGUgPSAnJzsKICAgICAgICAgIGNvbnNvbGUubG9nKCdBbGVydCB1cGRhdGUgYWRkZWQgc3VjY2Vzc2Z1bGx5Jyk7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRocm93IG5ldyBFcnJvcihkYXRhLm1lc3NhZ2UgfHwgJ0ZhaWxlZCB0byBhZGQgYWxlcnQgdXBkYXRlJyk7CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGFkZGluZyBhbGVydCB1cGRhdGU6JywgZXJyb3IpOwogICAgICAgIGFsZXJ0KCdGYWlsZWQgdG8gYWRkIGFsZXJ0IHVwZGF0ZTogJyArIGVycm9yLm1lc3NhZ2UpOwogICAgICB9CiAgICB9LAoKICAgIC8vIExvYWQgYWxlcnQgaGlzdG9yeSBkYXRhIGZvciBwZXJmb3JtYW5jZSB0YWJsZQogICAgYXN5bmMgbG9hZEFsZXJ0SGlzdG9yeURhdGEoaXRlbSkgewogICAgICB0cnkgewogICAgICAgIC8vIE1vY2sgZGF0YSBmb3Igbm93IC0gdGhpcyB3b3VsZCBjb21lIGZyb20geW91ciBBUEkKICAgICAgICB0aGlzLmFsZXJ0SGlzdG9yeURhdGEgPSBbCiAgICAgICAgICB7CiAgICAgICAgICAgIG1vbnRoOiAnSmFuJywKICAgICAgICAgICAgeWVhcjogJzIwMjQnLAogICAgICAgICAgICBzdGF0dXM6ICdOb3JtYWwnLAogICAgICAgICAgICBhY3R1YWxSYXRlOiAnMC41JywKICAgICAgICAgICAgdGFyZ2V0UmF0ZTogJzEuMCcsCiAgICAgICAgICAgIHhGYWN0b3I6ICcwLjUnLAogICAgICAgICAgICB2b2x1bWU6ICcxMDAwJywKICAgICAgICAgICAgZGVmZWN0czogJzUnLAogICAgICAgICAgICBub3RlczogJ1dpdGhpbiB0YXJnZXQnCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICBtb250aDogJ0ZlYicsCiAgICAgICAgICAgIHllYXI6ICcyMDI0JywKICAgICAgICAgICAgc3RhdHVzOiAnQWxlcnQnLAogICAgICAgICAgICBhY3R1YWxSYXRlOiAnMi4xJywKICAgICAgICAgICAgdGFyZ2V0UmF0ZTogJzEuMCcsCiAgICAgICAgICAgIHhGYWN0b3I6ICcyLjEnLAogICAgICAgICAgICB2b2x1bWU6ICcxMjAwJywKICAgICAgICAgICAgZGVmZWN0czogJzI1JywKICAgICAgICAgICAgbm90ZXM6ICdBYm92ZSB0YXJnZXQgLSBpbnZlc3RpZ2F0aW5nJwogICAgICAgICAgfQogICAgICAgIF07CiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgbG9hZGluZyBhbGVydCBoaXN0b3J5IGRhdGE6JywgZXJyb3IpOwogICAgICAgIHRoaXMuYWxlcnRIaXN0b3J5RGF0YSA9IFtdOwogICAgICB9CiAgICB9LAoKICAgIC8vIEdldCBhbGVydCByb3cgY2xhc3MgZm9yIHN0eWxpbmcKICAgIGdldEFsZXJ0Um93Q2xhc3MocmVjb3JkKSB7CiAgICAgIGlmIChyZWNvcmQuc3RhdHVzID09PSAnQWxlcnQnKSByZXR1cm4gJ2FsZXJ0LXJvdyc7CiAgICAgIGlmIChyZWNvcmQuc3RhdHVzID09PSAnTm9ybWFsJykgcmV0dXJuICdub3JtYWwtcm93JzsKICAgICAgcmV0dXJuICcnOwogICAgfSwKCiAgICAvLyBGb3JtYXQgZGF0ZSBoZWxwZXIKICAgIGZvcm1hdERhdGUoZGF0ZVN0cmluZykgewogICAgICBpZiAoIWRhdGVTdHJpbmcpIHJldHVybiAnJzsKICAgICAgcmV0dXJuIG5ldyBEYXRlKGRhdGVTdHJpbmcpLnRvTG9jYWxlRGF0ZVN0cmluZygpOwogICAgfSwKCiAgICAvLyBVcGRhdGUgYWN0aW9uIGl0ZW0gY29tcGxldGlvbgogICAgdXBkYXRlQWN0aW9uSXRlbUNvbXBsZXRpb24oYWN0aW9uSXRlbSwgaW5kZXgpIHsKICAgICAgaWYgKGFjdGlvbkl0ZW0uY29tcGxldGVkKSB7CiAgICAgICAgYWN0aW9uSXRlbS5jb21wbGV0ZWREYXRlID0gbmV3IERhdGUoKS50b0lTT1N0cmluZygpOwogICAgICB9IGVsc2UgewogICAgICAgIGFjdGlvbkl0ZW0uY29tcGxldGVkRGF0ZSA9IG51bGw7CiAgICAgIH0KICAgICAgYWN0aW9uSXRlbS5sYXN0VXBkYXRlZCA9IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKTsKICAgIH0sCgogICAgLy8gQWRkIGRlZmF1bHQgYWN0aW9uIGl0ZW1zCiAgICBhZGREZWZhdWx0QWN0aW9uSXRlbXMoKSB7CiAgICAgIGlmICghdGhpcy5zZWxlY3RlZFRyYWNraW5nSXRlbS5hY3Rpb25JdGVtcykgewogICAgICAgIHRoaXMuc2VsZWN0ZWRUcmFja2luZ0l0ZW0uYWN0aW9uSXRlbXMgPSBbXTsKICAgICAgfQoKICAgICAgY29uc3QgZGVmYXVsdEl0ZW1zID0gWwogICAgICAgIHsKICAgICAgICAgIHRpdGxlOiAnSW52ZXN0aWdhdGUgcm9vdCBjYXVzZScsCiAgICAgICAgICBkZXNjcmlwdGlvbjogJ0FuYWx5emUgZGF0YSB0byBpZGVudGlmeSBwb3RlbnRpYWwgcm9vdCBjYXVzZXMnLAogICAgICAgICAgY29tcGxldGVkOiBmYWxzZSwKICAgICAgICAgIGxhc3RVcGRhdGVkOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCkKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIHRpdGxlOiAnSW1wbGVtZW50IGNvcnJlY3RpdmUgYWN0aW9uJywKICAgICAgICAgIGRlc2NyaXB0aW9uOiAnRXhlY3V0ZSBwbGFuIHRvIGFkZHJlc3MgaWRlbnRpZmllZCBpc3N1ZXMnLAogICAgICAgICAgY29tcGxldGVkOiBmYWxzZSwKICAgICAgICAgIGxhc3RVcGRhdGVkOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCkKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIHRpdGxlOiAnTW9uaXRvciByZXN1bHRzJywKICAgICAgICAgIGRlc2NyaXB0aW9uOiAnVHJhY2sgcGVyZm9ybWFuY2UgdG8gdmVyaWZ5IGVmZmVjdGl2ZW5lc3MnLAogICAgICAgICAgY29tcGxldGVkOiBmYWxzZSwKICAgICAgICAgIGxhc3RVcGRhdGVkOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCkKICAgICAgICB9CiAgICAgIF07CgogICAgICB0aGlzLnNlbGVjdGVkVHJhY2tpbmdJdGVtLmFjdGlvbkl0ZW1zLnB1c2goLi4uZGVmYXVsdEl0ZW1zKTsKICAgIH0KICB9Cn07Cg=="}, {"version": 3, "sources": ["PQEOwnerDashboard.vue"], "names": [], "mappings": ";AAosBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;AAMA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "PQEOwnerDashboard.vue", "sourceRoot": "src/views/PQEDashboard", "sourcesContent": ["<template>\n  <div class=\"pqe-owner-dashboard-container\">\n    <div class=\"content-wrapper\">\n      <!-- Header Section with Critical Issues Summary -->\n      <div class=\"dashboard-header\">\n        <h3>{{ pqeOwner }}'s Dashboard</h3>\n        <div class=\"last-updated-text\">\n          Last Updated: {{ new Date().toLocaleString() }}\n        </div>\n      </div>\n\n      <!-- Key Metrics Section -->\n      <div class=\"key-metrics-section\">\n        <div class=\"metric-card clickable\" @click=\"scrollToAlerts\" title=\"Click to go to alerts section\">\n          <div class=\"metric-icon alerts\">\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n              <path d=\"M16,2A14,14,0,1,0,30,16,14,14,0,0,0,16,2Zm0,26A12,12,0,1,1,28,16,12,12,0,0,1,16,28Z\"></path>\n              <path d=\"M15,8h2v11H15Zm1,14a1.5,1.5,0,1,1-1.5,1.5A1.5,1.5,0,0,1,16,22Z\"></path>\n            </svg>\n          </div>\n          <div class=\"metric-content\">\n            <div class=\"metric-label\"># Alerts</div>\n            <div class=\"metric-value\">{{ totalAlerts }}</div>\n            <div class=\"metric-description\">Current Month</div>\n          </div>\n        </div>\n\n        <div class=\"metric-card clickable\" @click=\"goToActionTracker\" title=\"Click to go to Action Tracker\">\n          <div class=\"metric-icon in-progress\">\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n              <path d=\"M16,2A14,14,0,1,0,30,16,14,14,0,0,0,16,2Zm0,26A12,12,0,1,1,28,16,12,12,0,0,1,16,28Z\"></path>\n              <path d=\"M20.59,22,15,16.41V7h2v8.58l5,5.01L20.59,22z\"></path>\n            </svg>\n          </div>\n          <div class=\"metric-content\">\n            <div class=\"metric-label\"># In Progress Issues</div>\n            <div class=\"metric-value\">{{ inProgressIssuesCount }}</div>\n            <div class=\"metric-description\">From Action Tracker</div>\n          </div>\n        </div>\n\n        <div class=\"metric-card clickable\" @click=\"goToValidationPage\" title=\"Click to go to Validation page\">\n          <div class=\"metric-icon validated\">\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n              <path d=\"M26,4H6A2,2,0,0,0,4,6V26a2,2,0,0,0,2,2H26a2,2,0,0,0,2-2V6A2,2,0,0,0,26,4ZM14,21.5l-5-5,1.59-1.5L14,18.35,21.41,11,23,12.58Z\"></path>\n            </svg>\n          </div>\n          <div class=\"metric-content\">\n            <div class=\"metric-label\">Validated Fails</div>\n            <div class=\"metric-value\">{{ validatedCount }}/{{ totalFails }}</div>\n            <div class=\"metric-description\">This Month</div>\n          </div>\n        </div>\n\n        <div class=\"metric-card clickable\" @click=\"goToHeatmap\" title=\"Click to go to Heatmap\">\n          <div class=\"metric-icon groups-over-target\">\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n              <path d=\"M4,20V12a2,2,0,0,1,2-2H8a2,2,0,0,1,2,2v8a2,2,0,0,1-2,2H6A2,2,0,0,1,4,20Z\"></path>\n              <path d=\"M12,20V6a2,2,0,0,1,2-2h2a2,2,0,0,1,2,2V20a2,2,0,0,1-2,2H14A2,2,0,0,1,12,20Z\"></path>\n              <path d=\"M20,20V16a2,2,0,0,1,2-2h2a2,2,0,0,1,2,2v4a2,2,0,0,1-2,2H22A2,2,0,0,1,20,20Z\"></path>\n            </svg>\n          </div>\n          <div class=\"metric-content\">\n            <div class=\"metric-label\"># Groups Over Target</div>\n            <div class=\"metric-value\">{{ groupsOverTargetCount }}</div>\n            <div class=\"metric-description\">Current Month</div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Main Content Layout -->\n      <div class=\"dashboard-main-content\">\n        <!-- Left Column -->\n        <div class=\"dashboard-column\">\n          <!-- Action Tracker Alerts Section -->\n          <div class=\"section-card\">\n            <div class=\"section-header\" @click=\"toggleActionTrackerAlertsExpanded\">\n              <div class=\"section-title-container\">\n                <h4 class=\"section-title\">Action Tracker Alerts</h4>\n                <div class=\"section-subtitle\">\n                  Issues from action tracker above target for the month\n                </div>\n              </div>\n              <div class=\"section-controls\">\n                <div class=\"status-indicator flashing\" v-if=\"totalActionTrackerAlerts > 0\">\n                  {{ totalActionTrackerAlerts }}\n                </div>\n                <div class=\"expand-indicator\" :class=\"{ expanded: isActionTrackerAlertsExpanded }\">\n                  <svg width=\"16\" height=\"16\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                    <path d=\"M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z\"></path>\n                  </svg>\n                </div>\n              </div>\n            </div>\n\n            <div v-if=\"isActionTrackerAlertsExpanded\" class=\"section-content\">\n              <!-- In-Progress Issue Alerts -->\n              <div class=\"alert-subsection\">\n                <div class=\"subsection-header\" @click=\"toggleInProgressAlertsExpanded\">\n                  <h5 class=\"subsection-title\">In-Progress Issue Alerts</h5>\n                  <div class=\"subsection-controls\">\n                    <div class=\"status-indicator\" v-if=\"inProgressAlerts.length > 0\" style=\"background-color: #ff832b;\">\n                      {{ inProgressAlerts.length }}\n                    </div>\n                    <div class=\"expand-indicator\" :class=\"{ expanded: isInProgressAlertsExpanded }\">\n                      <svg width=\"12\" height=\"12\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                        <path d=\"M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z\"></path>\n                      </svg>\n                    </div>\n                  </div>\n                </div>\n\n                <div v-if=\"isInProgressAlertsExpanded\" class=\"subsection-content\">\n                  <div v-if=\"inProgressAlerts.length > 0\" class=\"issues-list\">\n                    <div v-for=\"alert in inProgressAlerts\" :key=\"alert.id\" class=\"issue-card action-tracker-alert\">\n                      <div class=\"issue-header\">\n                        <div class=\"issue-tags\">\n                          <cv-tag kind=\"orange\" label=\"In-Progress\" />\n                          <cv-tag kind=\"red\" :label=\"alert.severity\" />\n                        </div>\n                        <div class=\"issue-title\">{{ alert.category }}</div>\n                        <div class=\"issue-metadata\">\n                          <span class=\"issue-multiplier medium-severity\">{{ alert.xFactor }}x</span>\n                        </div>\n                      </div>\n                      <div class=\"issue-actions\">\n                        <cv-button\n                          kind=\"primary\"\n                          size=\"small\"\n                          @click=\"viewActionTrackerAlert(alert)\"\n                        >\n                          View\n                        </cv-button>\n                      </div>\n                    </div>\n                  </div>\n                  <div v-else class=\"no-data-message\">\n                    No in-progress issues above target this month.\n                  </div>\n                </div>\n              </div>\n\n              <!-- Outstanding Issue Alerts -->\n              <div class=\"alert-subsection\">\n                <div class=\"subsection-header\" @click=\"toggleOutstandingAlertsExpanded\">\n                  <h5 class=\"subsection-title\">Outstanding Issue Alerts</h5>\n                  <div class=\"subsection-controls\">\n                    <div class=\"status-indicator\" v-if=\"outstandingAlerts.length > 0\" style=\"background-color: #0f62fe;\">\n                      {{ outstandingAlerts.length }}\n                    </div>\n                    <div class=\"expand-indicator\" :class=\"{ expanded: isOutstandingAlertsExpanded }\">\n                      <svg width=\"12\" height=\"12\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                        <path d=\"M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z\"></path>\n                      </svg>\n                    </div>\n                  </div>\n                </div>\n\n                <div v-if=\"isOutstandingAlertsExpanded\" class=\"subsection-content\">\n                  <div v-if=\"outstandingAlerts.length > 0\" class=\"issues-list\">\n                    <div v-for=\"alert in outstandingAlerts\" :key=\"alert.id\" class=\"issue-card action-tracker-alert\">\n                      <div class=\"issue-header\">\n                        <div class=\"issue-tags\">\n                          <cv-tag kind=\"blue\" label=\"Outstanding\" />\n                          <cv-tag kind=\"red\" :label=\"alert.severity\" />\n                        </div>\n                        <div class=\"issue-title\">{{ alert.category }}</div>\n                        <div class=\"issue-metadata\">\n                          <span class=\"issue-multiplier medium-severity\">{{ alert.xFactor }}x</span>\n                        </div>\n                      </div>\n                      <div class=\"issue-actions\">\n                        <cv-button\n                          kind=\"primary\"\n                          size=\"small\"\n                          @click=\"viewActionTrackerAlert(alert)\"\n                        >\n                          View\n                        </cv-button>\n                      </div>\n                    </div>\n                  </div>\n                  <div v-else class=\"no-data-message\">\n                    No outstanding issues above target this month.\n                  </div>\n                </div>\n              </div>\n\n              <!-- Resolved Issue Alerts -->\n              <div class=\"alert-subsection\">\n                <div class=\"subsection-header\" @click=\"toggleResolvedAlertsExpanded\">\n                  <h5 class=\"subsection-title\">Resolved Issue Alerts</h5>\n                  <div class=\"subsection-controls\">\n                    <div class=\"status-indicator\" v-if=\"resolvedAlerts.length > 0\" style=\"background-color: #24a148;\">\n                      {{ resolvedAlerts.length }}\n                    </div>\n                    <div class=\"expand-indicator\" :class=\"{ expanded: isResolvedAlertsExpanded }\">\n                      <svg width=\"12\" height=\"12\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                        <path d=\"M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z\"></path>\n                      </svg>\n                    </div>\n                  </div>\n                </div>\n\n                <div v-if=\"isResolvedAlertsExpanded\" class=\"subsection-content\">\n                  <div v-if=\"resolvedAlerts.length > 0\" class=\"issues-list\">\n                    <div v-for=\"alert in resolvedAlerts\" :key=\"alert.id\" class=\"issue-card action-tracker-alert\">\n                      <div class=\"issue-header\">\n                        <div class=\"issue-tags\">\n                          <cv-tag kind=\"green\" label=\"Resolved\" />\n                          <cv-tag kind=\"red\" :label=\"alert.severity\" />\n                        </div>\n                        <div class=\"issue-title\">{{ alert.category }}</div>\n                        <div class=\"issue-metadata\">\n                          <span class=\"issue-multiplier good-performance\">{{ alert.xFactor }}x</span>\n                        </div>\n                      </div>\n                      <div class=\"issue-actions\">\n                        <cv-button\n                          kind=\"primary\"\n                          size=\"small\"\n                          @click=\"viewActionTrackerAlert(alert)\"\n                        >\n                          View\n                        </cv-button>\n                      </div>\n                    </div>\n                  </div>\n                  <div v-else class=\"no-data-message\">\n                    No resolved issues above target this month.\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Critical Issues Section -->\n          <div class=\"section-card\">\n            <div class=\"section-header critical-issues-header\" @click=\"toggleCriticalIssuesExpanded\">\n              <div class=\"section-title-container\">\n                <h4 class=\"section-title\">Critical Issues</h4>\n                <div class=\"section-subtitle\">New/unknown issues requiring immediate attention this month</div>\n              </div>\n              <div class=\"section-controls\">\n                <div class=\"status-indicator\" :class=\"{ 'flashing': !isCriticalIssuesExpanded && unresolvedCriticalIssues.length > 0 }\">\n                  {{ unresolvedCriticalIssues.length }}\n                </div>\n                <div class=\"expand-indicator\" :class=\"{ 'expanded': isCriticalIssuesExpanded }\">\n                  <svg width=\"16\" height=\"16\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                    <path d=\"M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z\"></path>\n                  </svg>\n                </div>\n              </div>\n            </div>\n\n            <div v-if=\"isCriticalIssuesExpanded\" class=\"section-content\">\n              <!-- Filter Controls in a Single Row -->\n              <div class=\"filter-container\">\n                <div class=\"filter-header\">\n                  <h5 class=\"filter-title\">Filter Issues</h5>\n                  <cv-button\n                    kind=\"ghost\"\n                    size=\"small\"\n                    class=\"clear-filters-button\"\n                    @click=\"clearFilters\"\n                    v-if=\"isFiltersActive\"\n                  >\n                    Clear Filters\n                  </cv-button>\n                </div>\n\n                <div class=\"filter-controls\">\n                  <div class=\"filter-group\">\n                    <label for=\"severity-dropdown\" class=\"filter-label\">Severity:</label>\n                    <cv-dropdown\n                      id=\"severity-dropdown\"\n                      v-model=\"severityFilter\"\n                      @change=\"handleSeverityFilterChange\"\n                      class=\"filter-dropdown\"\n                    >\n                      <cv-dropdown-item value=\"all\">All Severities</cv-dropdown-item>\n                      <cv-dropdown-item value=\"high\">High</cv-dropdown-item>\n                      <cv-dropdown-item value=\"medium\">Medium</cv-dropdown-item>\n                    </cv-dropdown>\n                  </div>\n\n                  <div class=\"filter-group\">\n                    <label for=\"analysis-dropdown\" class=\"filter-label\">Analysis Type:</label>\n                    <cv-dropdown\n                      id=\"analysis-dropdown\"\n                      v-model=\"analysisTypeFilter\"\n                      @change=\"handleAnalysisFilterChange\"\n                      class=\"filter-dropdown\"\n                    >\n                      <cv-dropdown-item value=\"all\">All Analysis Types</cv-dropdown-item>\n                      <cv-dropdown-item value=\"Root Cause\">Root Cause</cv-dropdown-item>\n                    </cv-dropdown>\n                  </div>\n                </div>\n              </div>\n\n              <!-- Critical Issues List -->\n              <div v-if=\"filteredCriticalIssues.length > 0\" class=\"issues-list\">\n                <div\n                  v-for=\"issue in filteredCriticalIssues\"\n                  :key=\"issue.id\"\n                  class=\"issue-card\"\n                  @click=\"toggleIssueExpanded(issue)\"\n                  :class=\"{ 'expanded': isIssueExpanded(issue.id) }\"\n                >\n                  <div class=\"issue-header\">\n                    <div class=\"issue-tags\">\n                      <cv-tag\n                        :kind=\"issue.severity === 'high' ? 'red' : 'magenta'\"\n                        :label=\"issue.severity === 'high' ? 'High' : 'Medium'\"\n                      />\n                      <cv-tag\n                        kind=\"purple\"\n                        class=\"analysis-tag\"\n                        :label=\"issue.analysisType\"\n                      />\n                    </div>\n                    <span class=\"issue-title\">{{ issue.category }}</span>\n                    <div class=\"issue-metadata\">\n                      <cv-tag\n                        kind=\"cool-gray\"\n                        class=\"month-tag\"\n                        :label=\"issue.month\"\n                      />\n                      <span class=\"issue-multiplier\" :class=\"issue.severity === 'high' ? 'high-severity' : 'medium-severity'\">\n                        {{ issue.increaseMultiplier === '(new)' ? '(new)' : `${issue.increaseMultiplier}x` }}\n                      </span>\n                    </div>\n                    <div class=\"expand-indicator\">\n                      <svg width=\"16\" height=\"16\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                        <path d=\"M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z\"></path>\n                      </svg>\n                    </div>\n                  </div>\n\n                  <div class=\"issue-content\" v-if=\"isIssueExpanded(issue.id)\">\n                    <div class=\"ai-description\">\n                      <p>{{ issue.aiDescription || 'Loading AI description...' }}</p>\n                    </div>\n\n                    <!-- Action Comment Text Box -->\n                    <div class=\"action-comment\">\n                      <cv-text-area\n                        v-model=\"issue.comment\"\n                        label=\"Action Comments\"\n                        placeholder=\"Add your comments or action plan here...\"\n                        :helper-text=\"issue.comment ? `${issue.comment.length} characters` : 'This will be added to the Action Tracker'\"\n                      ></cv-text-area>\n                    </div>\n\n                    <div class=\"issue-actions\">\n                      <cv-button\n                        kind=\"tertiary\"\n                        size=\"small\"\n                        @click.stop=\"viewIssueDetails(issue)\"\n                      >\n                        View Data\n                      </cv-button>\n                      <cv-button\n                        kind=\"tertiary\"\n                        size=\"small\"\n                        @click.stop=\"updateIssue(issue)\"\n                      >\n                        Update\n                      </cv-button>\n                      <cv-button\n                        kind=\"secondary\"\n                        size=\"small\"\n                        @click.stop=\"updateIssue(issue, false, true)\"\n                      >\n                        Mark Outstanding\n                      </cv-button>\n                      <cv-button\n                        kind=\"primary\"\n                        size=\"small\"\n                        @click.stop=\"updateIssue(issue, true, false)\"\n                      >\n                        Mark Resolved\n                      </cv-button>\n                      <cv-button\n                        kind=\"ghost\"\n                        size=\"small\"\n                        @click.stop=\"addCriticalIssueToActionTracker(issue)\"\n                      >\n                        Add item to action tracker\n                      </cv-button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <div v-else class=\"no-data-message\">\n                No critical issues found matching the selected filters.\n              </div>\n            </div>\n          </div>\n\n          <!-- Root Cause Analysis Section -->\n          <div class=\"section-card chart-section\">\n            <div class=\"section-header\">\n              <div class=\"section-title-container\">\n                <h4 class=\"section-title\">Analysis</h4>\n                <div class=\"section-subtitle\">Defect categories and fail rates over time</div>\n              </div>\n            </div>\n\n            <div class=\"chart-controls\">\n              <div class=\"control-group\">\n                <label class=\"control-label\">View By:</label>\n                <cv-dropdown\n                  v-model=\"rootCauseViewBy\"\n                  @change=\"handleRootCauseViewByChange\"\n                  class=\"control-dropdown\"\n                >\n                  <cv-dropdown-item value=\"rootCause\">Root Cause</cv-dropdown-item>\n                  <cv-dropdown-item value=\"vintage\">Vintage</cv-dropdown-item>\n                  <cv-dropdown-item value=\"sector\">Sector</cv-dropdown-item>\n                  <cv-dropdown-item value=\"supplier\">Supplier</cv-dropdown-item>\n                </cv-dropdown>\n              </div>\n\n              <div class=\"control-group\">\n                <label class=\"control-label\">Time Range:</label>\n                <cv-dropdown\n                  v-model=\"rootCauseTimeRange\"\n                  @change=\"handleRootCauseTimeRangeChange\"\n                  class=\"control-dropdown\"\n                >\n                  <cv-dropdown-item value=\"3month\">3 Months</cv-dropdown-item>\n                  <cv-dropdown-item value=\"6month\">6 Months</cv-dropdown-item>\n                </cv-dropdown>\n              </div>\n\n              <div class=\"control-group\" v-if=\"breakoutGroups.length > 0\">\n                <label class=\"control-label\">Group:</label>\n                <cv-dropdown\n                  v-model=\"rootCauseSelectedGroup\"\n                  @change=\"handleRootCauseGroupChange\"\n                  class=\"control-dropdown\"\n                >\n                  <cv-dropdown-item value=\"all\">All Groups</cv-dropdown-item>\n                  <cv-dropdown-item\n                    v-for=\"group in breakoutGroups\"\n                    :key=\"group.name\"\n                    :value=\"group.name\"\n                  >\n                    {{ group.name }}\n                  </cv-dropdown-item>\n                </cv-dropdown>\n              </div>\n            </div>\n\n            <div class=\"chart-container\">\n              <div v-if=\"isRootCauseDataLoading\" >\n                        Loading Chart...\n                        <RootCauseChart :data = [] :loading=\"isRootCauseDataLoading\"/>\n                      </div>\n              <RootCauseChart\n               v-if=\"rootCauseChartData.length > 0\"\n                :data=\"rootCauseChartData\"\n                :viewBy=\"rootCauseViewBy\"\n                :timeRange=\"rootCauseTimeRange\"\n                :selectedGroup=\"rootCauseSelectedGroup\"\n                :loading=\"isRootCauseDataLoading\"\n                @bar-click=\"handleRootCauseBarClick\"\n              />\n\n              <div v-if=\"rootCauseChartData.length == 0 && !isRootCauseDataLoading\" >\n                        No data available\n                      </div>\n              <!-- <div v-else-if=\"isRootCauseDataLoading\" class=\"loading-container\">\n                <p>Loading root cause data...</p>\n              </div>\n              <div v-else class=\"no-data-message\">\n                No root cause data available for the selected criteria.\n              </div> -->\n            </div>\n\n            <!-- <div class=\"section-footer\">\n              <p class=\"section-description\">\n                Root cause analysis showing defect categories and their fail rates over time.\n                Click on bars to see detailed information.\n              </p>\n            </div> -->\n          </div>\n\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Action Tracker Modal (same as Action Tracker page) -->\n  <cv-modal\n    class=\"tracking-modal\"\n    :visible=\"trackingModalVisible\"\n    @modal-hidden=\"trackingModalVisible = false\"\n    :size=\"'xl'\"\n  >\n    <template slot=\"title\">\n      <div>Action Tracking - {{ selectedTrackingItem ? selectedTrackingItem.pn : '' }}</div>\n    </template>\n    <template slot=\"content\">\n      <div class=\"tracking-modal-content\" v-if=\"selectedTrackingItem\">\n        <!-- Tracking Tabs -->\n        <cv-tabs>\n          <cv-tab id=\"new-alerts-tab\" label=\"New Alerts\">\n            <div class=\"tracking-tab-content\">\n              <div class=\"new-alerts-section\">\n                <h3 class=\"section-title\">Alert Management</h3>\n                <p class=\"section-description\">Manage alerts and updates for {{ selectedTrackingItem.pn }}</p>\n\n                <!-- AI Insight Section -->\n                <div class=\"ai-insight-section\">\n                  <h4 class=\"subsection-title\">AI Insight</h4>\n                  <div v-if=\"isLoadingAiInsight\" class=\"loading-message\">\n                    Generating AI insight...\n                  </div>\n                  <div v-else class=\"ai-insight-content\">\n                    {{ aiInsight || 'No AI insight available for this alert.' }}\n                  </div>\n                </div>\n\n                <!-- Add New Alert Update -->\n                <div class=\"add-alert-section\">\n                  <h4 class=\"subsection-title\">Add Alert Update</h4>\n                  <div class=\"add-update-form\">\n                    <cv-text-area\n                      v-model=\"newAlertUpdate\"\n                      label=\"Update Content\"\n                      placeholder=\"Enter update details...\"\n                      rows=\"4\"\n                    ></cv-text-area>\n                    <div class=\"update-form-actions\">\n                      <cv-button\n                        kind=\"primary\"\n                        @click=\"addAlertUpdate\"\n                        :disabled=\"!newAlertUpdate.trim()\"\n                      >\n                        Add Update\n                      </cv-button>\n                    </div>\n                  </div>\n                </div>\n\n                <!-- Alert History Table -->\n                <div class=\"alert-updates-section\">\n                  <h4 class=\"subsection-title\">Alert History</h4>\n                  <div v-if=\"alertUpdates.length > 0\" class=\"alert-updates-table\">\n                    <div class=\"updates-header\">\n                      <div class=\"update-column\">Date</div>\n                      <div class=\"update-column\">Update</div>\n                      <div class=\"update-column\">Updated By</div>\n                    </div>\n                    <div\n                      v-for=\"(update, index) in alertUpdates\"\n                      :key=\"index\"\n                      class=\"update-row\"\n                    >\n                      <div class=\"update-cell\">{{ update.date }}</div>\n                      <div class=\"update-cell\">{{ update.update }}</div>\n                      <div class=\"update-cell\">{{ update.updatedBy }}</div>\n                    </div>\n                  </div>\n                  <div v-else class=\"no-updates-message\">\n                    No alert updates available.\n                  </div>\n                </div>\n              </div>\n            </div>\n          </cv-tab>\n\n          <cv-tab id=\"action-items-tab\" label=\"Action Items\">\n            <div class=\"tracking-tab-content\">\n              <div class=\"tracking-section\">\n                <h3 class=\"tracking-section-title\">Action Details</h3>\n                <div class=\"action-summary\">\n                  <div class=\"summary-item\">\n                    <span class=\"summary-label\">Part Group:</span>\n                    <span class=\"summary-value\">{{ selectedTrackingItem.group }}</span>\n                  </div>\n                  <div class=\"summary-item\">\n                    <span class=\"summary-label\">Part Number:</span>\n                    <span class=\"summary-value\">{{ selectedTrackingItem.pn }}</span>\n                  </div>\n                  <div class=\"summary-item\">\n                    <span class=\"summary-label\">Status:</span>\n                    <span class=\"summary-value\">{{ selectedTrackingItem.status }}</span>\n                  </div>\n                  <div class=\"summary-item\">\n                    <span class=\"summary-label\">Priority:</span>\n                    <span class=\"summary-value priority-badge\" :class=\"selectedTrackingItem.priority ? selectedTrackingItem.priority.toLowerCase() : 'medium'\">\n                      {{ selectedTrackingItem.priority || 'Medium' }}\n                    </span>\n                  </div>\n                  <div class=\"summary-item\">\n                    <span class=\"summary-label\">Progress:</span>\n                    <span class=\"summary-value\">{{ selectedTrackingItem.progress || 0 }}%</span>\n                  </div>\n                </div>\n              </div>\n\n              <div class=\"tracking-section\">\n                <h3 class=\"tracking-section-title\">Action Items & Progress</h3>\n                <div class=\"action-items-list\">\n                  <div\n                    v-for=\"(actionItem, index) in selectedTrackingItem.actionItems\"\n                    :key=\"index\"\n                    class=\"action-item-card\"\n                    :class=\"{ 'completed': actionItem.completed }\"\n                  >\n                    <!-- Action Item Header -->\n                    <div class=\"action-item-header\">\n                      <cv-checkbox\n                        v-model=\"actionItem.completed\"\n                        :label=\"actionItem.title\"\n                        @change=\"updateActionItemCompletion(actionItem, index)\"\n                      />\n                      <span class=\"completion-date\" v-if=\"actionItem.completed && actionItem.completedDate\">\n                        Completed on {{ formatDate(actionItem.completedDate) }}\n                      </span>\n                      <span class=\"last-updated\" v-if=\"actionItem.lastUpdated\">\n                        Last updated: {{ formatDate(actionItem.lastUpdated) }}\n                      </span>\n                    </div>\n\n                    <!-- Action Item Description -->\n                    <div class=\"action-item-description\" v-if=\"actionItem.description\">\n                      {{ actionItem.description }}\n                    </div>\n                  </div>\n\n                  <!-- No Action Items -->\n                  <div v-if=\"!selectedTrackingItem.actionItems || selectedTrackingItem.actionItems.length === 0\" class=\"no-action-items\">\n                    <p>No action items defined for this tracking item.</p>\n                    <cv-button\n                      kind=\"primary\"\n                      size=\"small\"\n                      @click=\"addDefaultActionItems\"\n                    >\n                      Create Default Action Items\n                    </cv-button>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </cv-tab>\n\n          <cv-tab id=\"performance-tab\" label=\"Performance Chart & History\">\n            <div class=\"tracking-tab-content\">\n              <!-- Performance Chart Section -->\n              <div class=\"chart-section\">\n                <h3 class=\"section-title\">Performance Chart</h3>\n                <PerformanceChart :trackingItem=\"selectedTrackingItem\" />\n              </div>\n\n              <!-- Performance History Table Section -->\n              <div class=\"performance-history-section\">\n                <h3 class=\"section-title\">Monthly Performance History</h3>\n                <p class=\"section-description\">Historical record of performance and status for {{ selectedTrackingItem.pn }}</p>\n\n                <cv-data-table\n                  :columns=\"alertHistoryColumns\"\n                  :title=\"''\"\n                  class=\"alert-history-table\"\n                >\n                  <template slot=\"data\">\n                    <cv-data-table-row\n                      v-for=\"(record, index) in alertHistoryData\"\n                      :key=\"index\"\n                      :class=\"getAlertRowClass(record)\"\n                    >\n                      <cv-data-table-cell>{{ record.month }}</cv-data-table-cell>\n                      <cv-data-table-cell>{{ record.year }}</cv-data-table-cell>\n                      <cv-data-table-cell>\n                        <div class=\"status-cell\">\n                          <span class=\"status-indicator\" :class=\"record.status.toLowerCase()\"></span>\n                          <span class=\"status-text\" :class=\"record.status.toLowerCase()\">{{ record.status }}</span>\n                        </div>\n                      </cv-data-table-cell>\n                      <cv-data-table-cell>{{ record.actualRate }}%</cv-data-table-cell>\n                      <cv-data-table-cell>{{ record.targetRate }}%</cv-data-table-cell>\n                      <cv-data-table-cell>{{ record.xFactor }}</cv-data-table-cell>\n                      <cv-data-table-cell>{{ record.volume }}</cv-data-table-cell>\n                      <cv-data-table-cell>{{ record.defects }}</cv-data-table-cell>\n                      <cv-data-table-cell>{{ record.notes || 'N/A' }}</cv-data-table-cell>\n                    </cv-data-table-row>\n                  </template>\n                </cv-data-table>\n\n                <div v-if=\"!alertHistoryData || alertHistoryData.length === 0\" class=\"no-alert-data\">\n                  <p>No performance history available for this part</p>\n                  <p class=\"note\">Performance history will be populated as data becomes available</p>\n                </div>\n              </div>\n            </div>\n          </cv-tab>\n        </cv-tabs>\n      </div>\n    </template>\n  </cv-modal>\n</template>\n\n<script>\nimport {\n  CvButton,\n  CvTag,\n  CvTextArea,\n  CvDropdown,\n  CvDropdownItem,\n  CvModal,\n  CvTabs,\n  CvTab,\n  CvCheckbox,\n  CvDataTable,\n  CvDataTableRow,\n  CvDataTableCell\n} from '@carbon/vue';\n\nimport RootCauseChart from '@/components/RootCauseChart/RootCauseChart';\nimport PerformanceChart from '@/components/PerformanceChart/PerformanceChart.vue';\n\nexport default {\n  name: 'PQEOwnerDashboard',\n  components: {\n    CvButton,\n    CvTag,\n    CvTextArea,\n    CvDropdown,\n    CvDropdownItem,\n    CvModal,\n    CvTabs,\n    CvTab,\n    CvCheckbox,\n    CvDataTable,\n    CvDataTableRow,\n    CvDataTableCell,\n    RootCauseChart,\n    PerformanceChart\n  },\n  props: {\n    pqeOwner: {\n      type: String,\n      required: true\n    }\n  },\n  data() {\n    return {\n      // Critical Issues Data\n      newCriticalIssues: [],\n      unresolvedCriticalIssues: [],\n      expandedIssueIds: [], // Track which issues are expanded\n      isCriticalIssuesExpanded: false, // Track if critical issues section is expanded\n\n      // Action Tracker Alerts Data\n      isActionTrackerAlertsExpanded: false,\n      isInProgressAlertsExpanded: false,\n      isOutstandingAlertsExpanded: false,\n      isResolvedAlertsExpanded: false,\n\n      // Action Tracker Alert Data\n      inProgressAlerts: [],\n      outstandingAlerts: [],\n      resolvedAlerts: [],\n\n      // Filtering for Critical Issues\n      selectedFilters: {\n        severity: [],\n        analysisType: []\n      },\n      severityFilter: 'all',\n      analysisTypeFilter: 'all',\n\n      // Validation Data\n      validatedCount: 0,\n      unvalidatedCount: 0,\n\n      // Breakout Groups Data\n      breakoutGroups: [],\n\n      // Root Cause Chart Data\n      rootCauseChartData: [],\n      isRootCauseDataLoading: true,\n\n      // Root Cause Chart Controls\n      rootCauseViewBy: 'rootCause',\n      rootCauseTimeRange: '6month',\n      rootCauseSelectedGroup: 'all',\n\n      // Loading States\n      isLoading: false,\n\n      // Action Tracker Modal Data\n      trackingModalVisible: false,\n      selectedTrackingItem: null,\n      alertUpdates: [],\n      newAlertUpdate: '',\n      aiInsight: '',\n      isLoadingAiInsight: false,\n      alertHistoryData: [],\n      alertHistoryColumns: ['Month', 'Year', 'Status', 'Actual Rate', 'Target Rate', 'X-Factor', 'Volume', 'Defects', 'Notes']\n    };\n  },\n  computed: {\n    // Filtered critical issues based on selected filters\n    filteredCriticalIssues() {\n      // If no filters are selected, return all issues\n      if (this.selectedFilters.severity.length === 0 && this.selectedFilters.analysisType.length === 0) {\n        return this.unresolvedCriticalIssues;\n      }\n\n      // Apply filters\n      return this.unresolvedCriticalIssues.filter(issue => {\n        // Check if issue passes severity filter\n        const passesSeverityFilter = this.selectedFilters.severity.length === 0 ||\n                                    this.selectedFilters.severity.includes(issue.severity);\n\n        // Check if issue passes analysis type filter\n        const passesAnalysisFilter = this.selectedFilters.analysisType.length === 0 ||\n                                    this.selectedFilters.analysisType.includes(issue.analysisType);\n\n        // Issue must pass both filters\n        return passesSeverityFilter && passesAnalysisFilter;\n      });\n    },\n\n    // Check if any filters are active\n    isFiltersActive() {\n      return this.selectedFilters.severity.length > 0 || this.selectedFilters.analysisType.length > 0;\n    },\n\n\n\n    // Calculate total alerts (critical breakout groups)\n    totalAlerts() {\n      return this.breakoutGroups.filter(group => group.xFactor >= 1.5).length;\n    },\n\n    // Calculate total in-progress issues from action tracker\n    inProgressIssuesCount() {\n      // This would normally fetch from action tracker API\n      // For now, return a calculated value based on action tracker alerts\n      return this.inProgressAlerts.length;\n    },\n\n    // Calculate total fails (validated + unvalidated)\n    totalFails() {\n      return this.validatedCount + this.unvalidatedCount;\n    },\n\n    // Calculate groups over target (xFactor > 1.0)\n    groupsOverTargetCount() {\n      return this.breakoutGroups.filter(group => group.xFactor > 1.0).length;\n    },\n\n    // Calculate total action tracker alerts\n    totalActionTrackerAlerts() {\n      return this.inProgressAlerts.length + this.outstandingAlerts.length + this.resolvedAlerts.length;\n    }\n  },\n  watch: {\n    pqeOwner: {\n      immediate: true,\n      handler(newValue, oldValue) {\n        console.log(`PQE Owner changed from ${oldValue} to ${newValue}`);\n        if (newValue) {\n          // Reset the group selection when PQE owner changes\n          this.rootCauseSelectedGroup = 'all';\n          this.loadDashboardData();\n        }\n      }\n    }\n  },\n  methods: {\n    loadDashboardData() {\n      this.loadCriticalIssues();\n      this.loadValidationCounts();\n      this.loadActionTrackerAlerts();\n      this.loadRootCauseData();\n    },\n\n\n\n\n\n    async loadCriticalIssues() {\n      console.log(`Loading critical issues for PQE owner: ${this.pqeOwner}`);\n      this.isLoading = true;\n\n      try {\n        // Get authentication config\n        const config = this.getAuthConfig();\n\n        // First, load the breakout groups for this PQE owner\n        await this.loadBreakoutGroups();\n\n        // Fetch critical issues from the API\n        const response = await fetch('/api-statit2/get_pqe_critical_issues', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({\n            pqeOwner: this.pqeOwner\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch critical issues: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success') {\n          // Filter critical issues to only include those related to this PQE's breakout groups\n          const allIssues = data.critical_issues || [];\n\n          // If we have breakout groups, filter issues to only include those for this PQE's groups\n          if (this.breakoutGroups.length > 0) {\n            this.unresolvedCriticalIssues = allIssues.filter(issue => {\n              // Check if the issue's category matches any of the PQE's breakout groups\n              return this.breakoutGroups.some(group =>\n                issue.category.includes(group.name) || group.name.includes(issue.category)\n              );\n            });\n          } else {\n            // If we don't have breakout groups yet, use all issues\n            this.unresolvedCriticalIssues = allIssues;\n          }\n\n          console.log(`Loaded ${this.unresolvedCriticalIssues.length} critical issues for ${this.pqeOwner}'s groups`);\n        } else {\n          console.error('Failed to load critical issues:', data.message);\n          // Use sample data for development\n          this.loadSampleCriticalIssues();\n        }\n      } catch (error) {\n        console.error('Error loading critical issues:', error);\n        // Use sample data for development\n        this.loadSampleCriticalIssues();\n      } finally {\n        this.isLoading = false;\n      }\n    },\n\n    async loadBreakoutGroups() {\n      console.log(`Loading breakout groups for PQE owner: ${this.pqeOwner}`);\n\n      try {\n        // Get authentication config\n        const config = this.getAuthConfig();\n\n        // Fetch breakout groups from the API\n        const response = await fetch('/api-statit2/get_pqe_breakout_groups', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({\n            pqeOwner: this.pqeOwner\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch breakout groups: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success') {\n          this.breakoutGroups = data.breakout_groups || [];\n          console.log(`Loaded ${this.breakoutGroups.length} breakout groups for ${this.pqeOwner}`);\n\n          // Reload root cause chart data now that we have the correct breakout groups\n          await this.loadRootCauseData();\n        } else {\n          console.error('Failed to load breakout groups:', data.message);\n          // Use sample data for development\n          this.loadSampleBreakoutGroups();\n          // Reload root cause chart data with sample groups\n          await this.loadRootCauseData();\n        }\n      } catch (error) {\n        console.error('Error loading breakout groups:', error);\n        // Use sample data for development\n        this.loadSampleBreakoutGroups();\n        // Reload root cause chart data with sample groups\n        await this.loadRootCauseData();\n      }\n    },\n\n    async loadSampleBreakoutGroups() {\n      // Sample data for development\n      if (this.pqeOwner === 'Albert G.') {\n        this.breakoutGroups = [\n          { name: 'Fan Themis', status: 'Short-Term Spike', xFactor: 3.2 },\n          { name: 'Victoria Crypto', status: 'Sustained Problem', xFactor: 1.8 },\n          { name: 'Quantum Nexus', status: 'Sustained Problem', xFactor: 1.6 }\n        ];\n      } else if (this.pqeOwner === 'Sarah L.') {\n        this.breakoutGroups = [\n          { name: 'Stellar Core', status: 'Short-Term Spike', xFactor: 2.5 },\n          { name: 'Nebula Drive', status: 'Sustained Problem', xFactor: 1.7 }\n        ];\n      } else {\n        // Default sample data\n        this.breakoutGroups = [\n          { name: 'Sample Group 1', status: 'Normal', xFactor: 0.9 },\n          { name: 'Sample Group 2', status: 'Normal', xFactor: 0.8 }\n        ];\n      }\n\n      // Reload root cause chart data with the sample groups\n      await this.loadRootCauseData();\n    },\n\n    loadSampleCriticalIssues() {\n      // Sample data for development\n      this.unresolvedCriticalIssues = [\n        {\n          id: 'ci1',\n          category: 'Fan Themis',\n          month: '2024-06',\n          severity: 'high',\n          increaseMultiplier: '3.2',\n          aiDescription: 'Fan Themis showing 3.2x spike in June 2024 with 12 fails vs 4 expected. Root cause appears to be airflow issues in the new design.',\n          comment: '',\n          analysisType: 'Root Cause'\n        },\n        {\n          id: 'ci2',\n          category: 'Victoria Crypto',\n          month: '2024-06',\n          severity: 'medium',\n          increaseMultiplier: '1.8',\n          aiDescription: 'Victoria Crypto has sustained problem with 1.8x target rate for 3 consecutive months. Consistent pattern of power delivery issues.',\n          comment: '',\n          analysisType: 'Root Cause'\n        },\n\n      ];\n\n      // Set new critical issues to empty array for now\n      this.newCriticalIssues = [];\n    },\n\n    async loadValidationCounts() {\n      console.log(`Loading validation counts for PQE owner: ${this.pqeOwner}`);\n\n      try {\n        // Get authentication config\n        const config = this.getAuthConfig();\n\n        // Fetch validation counts from the API\n        const response = await fetch('/api-statit2/get_validation_counts', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({\n            pqeOwner: this.pqeOwner\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch validation counts: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success') {\n          this.validatedCount = data.validated_count || 0;\n          this.unvalidatedCount = data.unvalidated_count || 0;\n          console.log(`Loaded validation counts: ${this.validatedCount} validated, ${this.unvalidatedCount} unvalidated`);\n        } else {\n          console.error('Failed to load validation counts:', data.message);\n          // Use sample data for development\n          this.validatedCount = 125;\n          this.unvalidatedCount = 37;\n        }\n      } catch (error) {\n        console.error('Error loading validation counts:', error);\n        // Use sample data for development\n        this.validatedCount = 125;\n        this.unvalidatedCount = 37;\n      }\n    },\n\n    // Load Action Tracker Alerts data\n    loadActionTrackerAlerts() {\n      console.log(`Loading Action Tracker Alerts for PQE owner: ${this.pqeOwner}`);\n\n      // Generate sample data for demonstration\n      // In a real implementation, this would fetch from the action tracker API\n      this.inProgressAlerts = [\n        {\n          id: 'ip-1',\n          category: 'Thermal Management',\n          severity: 'High',\n          xFactor: '2.1',\n          status: 'In-Progress'\n        },\n        {\n          id: 'ip-2',\n          category: 'Power Delivery',\n          severity: 'Medium',\n          xFactor: '1.8',\n          status: 'In-Progress'\n        }\n      ];\n\n      this.outstandingAlerts = [\n        {\n          id: 'out-1',\n          category: 'Signal Integrity',\n          severity: 'Medium',\n          xFactor: '1.6',\n          status: 'Outstanding'\n        }\n      ];\n\n      this.resolvedAlerts = [\n        {\n          id: 'res-1',\n          category: 'Manufacturing Defect',\n          severity: 'High',\n          xFactor: '0.8',\n          status: 'Resolved'\n        },\n        {\n          id: 'res-2',\n          category: 'Component Quality',\n          severity: 'Medium',\n          xFactor: '0.9',\n          status: 'Resolved'\n        }\n      ];\n\n      console.log(`Loaded ${this.totalActionTrackerAlerts} Action Tracker Alerts`);\n    },\n\n    toggleCriticalIssuesExpanded() {\n      this.isCriticalIssuesExpanded = !this.isCriticalIssuesExpanded;\n    },\n\n    toggleResolvedIssuesExpanded() {\n      this.isResolvedIssuesExpanded = !this.isResolvedIssuesExpanded;\n    },\n\n    toggleOutstandingIssuesExpanded() {\n      this.isOutstandingIssuesExpanded = !this.isOutstandingIssuesExpanded;\n    },\n\n    toggleIssueExpanded(issue) {\n      const issueId = issue.id;\n      const index = this.expandedIssueIds.indexOf(issueId);\n\n      if (index === -1) {\n        // Issue is not expanded, so expand it\n        this.expandedIssueIds.push(issueId);\n      } else {\n        // Issue is expanded, so collapse it\n        this.expandedIssueIds.splice(index, 1);\n      }\n    },\n\n    toggleResolvedIssueExpanded(issue) {\n      const issueId = issue.id;\n      const index = this.expandedResolvedIssueIds.indexOf(issueId);\n\n      if (index === -1) {\n        // Issue is not expanded, so expand it\n        this.expandedResolvedIssueIds.push(issueId);\n      } else {\n        // Issue is expanded, so collapse it\n        this.expandedResolvedIssueIds.splice(index, 1);\n      }\n    },\n\n    toggleOutstandingIssueExpanded(issue) {\n      const issueId = issue.id;\n      const index = this.expandedOutstandingIssueIds.indexOf(issueId);\n\n      if (index === -1) {\n        // Issue is not expanded, so expand it\n        this.expandedOutstandingIssueIds.push(issueId);\n      } else {\n        // Issue is expanded, so collapse it\n        this.expandedOutstandingIssueIds.splice(index, 1);\n      }\n    },\n\n    isIssueExpanded(issueId) {\n      return this.expandedIssueIds.includes(issueId);\n    },\n\n    isResolvedIssueExpanded(issueId) {\n      return this.expandedResolvedIssueIds.includes(issueId);\n    },\n\n    isOutstandingIssueExpanded(issueId) {\n      return this.expandedOutstandingIssueIds.includes(issueId);\n    },\n\n    markIssueAsResolved(issue) {\n      console.log('Mark issue as resolved:', issue);\n\n      // In a real implementation, this would call an API to update the issue status\n      // For now, we'll just move the issue from unresolved to resolved\n\n      // Create a resolved issue object with additional fields\n      const resolvedIssue = {\n        ...issue,\n        resolutionDate: new Date().toISOString().split('T')[0], // Today's date in YYYY-MM-DD format\n        currentPerformance: '1.0x' // Initial performance after resolution\n      };\n\n      // Add to resolved issues\n      this.resolvedIssues.push(resolvedIssue);\n\n      // Remove from unresolved issues\n      const index = this.unresolvedCriticalIssues.findIndex(i => i.id === issue.id);\n      if (index !== -1) {\n        this.unresolvedCriticalIssues.splice(index, 1);\n      }\n\n      // Show success message\n      alert(`Issue marked as resolved: ${issue.category}`);\n    },\n\n    markIssueAsOutstanding(issue) {\n      console.log('Mark issue as outstanding:', issue);\n\n      // In a real implementation, this would call an API to update the issue status\n      // For now, we'll just move the issue from unresolved to outstanding\n\n      // Create an outstanding issue object with additional fields\n      const outstandingIssue = {\n        ...issue,\n        acceptanceDate: new Date().toISOString().split('T')[0], // Today's date in YYYY-MM-DD format\n        currentPerformance: issue.increaseMultiplier, // Initial performance is the same as the issue multiplier\n        acceptedBy: 'Engineering Team' // Default value\n      };\n\n      // Add to outstanding issues\n      this.outstandingIssues.push(outstandingIssue);\n\n      // Remove from unresolved issues\n      const index = this.unresolvedCriticalIssues.findIndex(i => i.id === issue.id);\n      if (index !== -1) {\n        this.unresolvedCriticalIssues.splice(index, 1);\n      }\n\n      // Show success message\n      alert(`Issue marked as outstanding: ${issue.category}`);\n    },\n\n    handleSeverityFilterChange() {\n      // Update the selected filters based on the severity dropdown\n      if (this.severityFilter === 'all') {\n        this.selectedFilters.severity = [];\n      } else {\n        this.selectedFilters.severity = [this.severityFilter];\n      }\n    },\n\n    handleAnalysisFilterChange() {\n      // Update the selected filters based on the analysis type dropdown\n      if (this.analysisTypeFilter === 'all') {\n        this.selectedFilters.analysisType = [];\n      } else {\n        this.selectedFilters.analysisType = [this.analysisTypeFilter];\n      }\n    },\n\n    clearFilters() {\n      this.selectedFilters.severity = [];\n      this.selectedFilters.analysisType = [];\n      this.severityFilter = 'all';\n      this.analysisTypeFilter = 'all';\n    },\n\n    handleResolvedCategoryFilterChange() {\n      // Update the selected filters based on the category dropdown\n      if (this.resolvedCategoryFilter === 'all') {\n        this.resolvedFilters.category = [];\n      } else {\n        this.resolvedFilters.category = [this.resolvedCategoryFilter];\n      }\n    },\n\n    handleResolvedAnalysisFilterChange() {\n      // Update the selected filters based on the analysis type dropdown\n      if (this.resolvedAnalysisTypeFilter === 'all') {\n        this.resolvedFilters.analysisType = [];\n      } else {\n        this.resolvedFilters.analysisType = [this.resolvedAnalysisTypeFilter];\n      }\n    },\n\n    clearResolvedFilters() {\n      this.resolvedFilters.category = [];\n      this.resolvedFilters.analysisType = [];\n      this.resolvedCategoryFilter = 'all';\n      this.resolvedAnalysisTypeFilter = 'all';\n    },\n\n    handleOutstandingCategoryFilterChange() {\n      // Update the selected filters based on the category dropdown\n      if (this.outstandingCategoryFilter === 'all') {\n        this.outstandingFilters.category = [];\n      } else {\n        this.outstandingFilters.category = [this.outstandingCategoryFilter];\n      }\n    },\n\n    handleOutstandingAnalysisFilterChange() {\n      // Update the selected filters based on the analysis type dropdown\n      if (this.outstandingAnalysisTypeFilter === 'all') {\n        this.outstandingFilters.analysisType = [];\n      } else {\n        this.outstandingFilters.analysisType = [this.outstandingAnalysisTypeFilter];\n      }\n    },\n\n    clearOutstandingFilters() {\n      this.outstandingFilters.category = [];\n      this.outstandingFilters.analysisType = [];\n      this.outstandingCategoryFilter = 'all';\n      this.outstandingAnalysisTypeFilter = 'all';\n    },\n\n    updateIssue(issue, markAsResolved = false, markAsOutstanding = false) {\n      console.log('Update issue:', issue, 'Mark as resolved:', markAsResolved, 'Mark as outstanding:', markAsOutstanding);\n\n      // Emit event to update action tracker\n      this.$emit('update-action-tracker', {\n        issueId: issue.id,\n        category: issue.category,\n        comment: issue.comment,\n        severity: issue.severity,\n        pqeOwner: this.pqeOwner,\n        month: issue.month,\n        analysisType: issue.analysisType,\n        resolved: markAsResolved,\n        outstanding: markAsOutstanding\n      });\n\n      // If marking as resolved, move the issue to resolved issues\n      if (markAsResolved) {\n        this.markIssueAsResolved(issue);\n      }\n      // If marking as outstanding, move the issue to outstanding issues\n      else if (markAsOutstanding) {\n        this.markIssueAsOutstanding(issue);\n      }\n      else {\n        // Show success message for regular update\n        alert(`Action tracker updated for issue: ${issue.category}`);\n      }\n    },\n\n    viewPerformanceData(issue) {\n      console.log('View performance data for:', issue.category);\n\n      // In a real implementation, this would show a modal or chart with performance data\n      // For now, we'll just show an alert with the data\n\n      const performanceData = this.performanceData[issue.category];\n      if (performanceData && performanceData.length > 0) {\n        const performanceText = performanceData\n          .map(data => `${data.month}: ${data.xFactor}x`)\n          .join('\\n');\n\n        alert(`Performance data for ${issue.category}:\\n${performanceText}`);\n      } else {\n        alert(`No performance data available for ${issue.category}`);\n      }\n    },\n\n    viewIssueDetails(issue) {\n      console.log('View issue details for:', issue.category);\n      // In a real implementation, this would show a modal or navigate to a detailed view\n      alert(`Viewing details for issue: ${issue.category}\\nMonth: ${issue.month}\\nSeverity: ${issue.severity}\\nMultiplier: ${issue.increaseMultiplier}x`);\n    },\n\n    // Root Cause Chart methods\n    async loadRootCauseData() {\n      console.log('Loading root cause chart data for PQE Owner Dashboard');\n      this.isRootCauseDataLoading = true;\n\n      try {\n        // Get authentication config\n        const config = this.getAuthConfig();\n\n        // Calculate date range based on selected time range\n        const endDate = new Date();\n        const startDate = new Date();\n        const monthsToFetch = this.rootCauseTimeRange === '3month' ? 2 : 5; // 3 or 6 months including current\n        startDate.setMonth(endDate.getMonth() - monthsToFetch);\n\n        const startDateStr = `${startDate.getFullYear()}-${String(startDate.getMonth() + 1).padStart(2, '0')}`;\n        const endDateStr = `${endDate.getFullYear()}-${String(endDate.getMonth() + 1).padStart(2, '0')}`;\n\n        console.log(`Fetching root cause data for ${this.pqeOwner} from ${startDateStr} to ${endDateStr}, Group: ${this.rootCauseSelectedGroup}`);\n\n        // Call the new API endpoint\n        const response = await fetch('/api-statit2/get_pqe_root_cause_analysis', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({\n            pqeOwner: this.pqeOwner,\n            startDate: startDateStr,\n            endDate: endDateStr,\n            selectedGroup: this.rootCauseSelectedGroup === 'all' ? null : this.rootCauseSelectedGroup\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch root cause data: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success') {\n          // Transform the API data to chart format\n          this.rootCauseChartData = this.transformRootCauseData(data.categoryData);\n          console.log('Root cause chart data loaded:', data.categoryData);\n          console.log('Chart data length:', this.rootCauseChartData.length);\n          console.log('Sample data point:', this.rootCauseChartData[0]);\n          console.log('Breakout groups:', data.breakoutGroups);\n          console.log('Part numbers:', data.partNumbers);\n\n          console.log('Chart data loaded, watcher should handle update');\n        } else {\n          console.error('Failed to load root cause data:', data.sql_error_msg || data.message);\n          // Fall back to mock data\n          this.rootCauseChartData = this.generateMockRootCauseData();\n          console.log('Using mock data:', this.rootCauseChartData.length);\n        }\n      } catch (error) {\n        console.error('Error loading root cause data:', error);\n        // Fall back to mock data\n        this.rootCauseChartData = this.generateMockRootCauseData();\n      } finally {\n        this.isRootCauseDataLoading = false;\n      }\n    },\n\n    transformRootCauseData(categoryData) {\n      // Transform the API response into chart format\n      const chartData = [];\n\n      // categoryData structure: { \"category\": { \"2024-03\": { defects: 5, volume: 1000, failRate: 0.5 }, ... }, ... }\n      for (const [category, monthData] of Object.entries(categoryData)) {\n        // Clean up category name (trim whitespace)\n        const cleanCategory = category.trim();\n        console.log(\"trim cat\", cleanCategory)\n\n        for (const [month, data] of Object.entries(monthData)) {\n          chartData.push({\n            group: cleanCategory,\n            key: month,\n            value: parseFloat(data.failRate.toFixed(2))\n          });\n        }\n      }\n\n      console.log('Transformed root cause data:', chartData);\n      console.log('Unique groups in data:', [...new Set(chartData.map(d => d.group))]);\n      console.log('Unique keys in data:', [...new Set(chartData.map(d => d.key))]);\n      console.log('Value range:', Math.min(...chartData.map(d => d.value)), 'to', Math.max(...chartData.map(d => d.value)));\n      return chartData;\n    },\n\n    generateMockRootCauseData() {\n      // Use the actual breakout groups for this PQE owner instead of generic categories\n      let categories = [];\n\n      if (this.breakoutGroups && this.breakoutGroups.length > 0) {\n        // Use the actual breakout groups for this PQE owner\n        categories = this.breakoutGroups.map(group => group.name);\n\n        // If a specific group is selected, filter to only that group\n        if (this.rootCauseSelectedGroup !== 'all') {\n          categories = categories.filter(category => category === this.rootCauseSelectedGroup);\n        }\n      } else {\n        // Fallback to sample data if breakout groups aren't loaded yet\n        if (this.pqeOwner === 'Albert G.') {\n          categories = ['Fan Themis', 'Victoria Crypto', 'Quantum Nexus'];\n        } else if (this.pqeOwner === 'Sarah L.') {\n          categories = ['Stellar Core', 'Nebula Drive'];\n        } else {\n          categories = ['Sample Group 1', 'Sample Group 2'];\n        }\n\n        // If a specific group is selected, filter to only that group\n        if (this.rootCauseSelectedGroup !== 'all') {\n          categories = categories.filter(category => category === this.rootCauseSelectedGroup);\n        }\n      }\n\n      // Generate months based on selected time range\n      const now = new Date();\n      const months = [];\n      const monthsToGenerate = this.rootCauseTimeRange === '3month' ? 3 : 6;\n\n      for (let i = monthsToGenerate - 1; i >= 0; i--) {\n        const date = new Date(now.getFullYear(), now.getMonth() - i, 1);\n        const year = date.getFullYear();\n        const month = String(date.getMonth() + 1).padStart(2, '0');\n        months.push(`${year}-${month}`);\n      }\n\n      const data = [];\n\n      months.forEach(month => {\n        categories.forEach(category => {\n          // Generate realistic fail rate data (0-5% range)\n          const baseRate = Math.random() * 3 + 0.5; // 0.5% to 3.5%\n          const variation = (Math.random() - 0.5) * 1; // ±0.5%\n          const failRate = Math.max(0.1, baseRate + variation);\n\n          data.push({\n            group: category,\n            key: month,\n            value: parseFloat(failRate.toFixed(2))\n          });\n        });\n      });\n\n      return data;\n    },\n\n    handleRootCauseBarClick(event) {\n      if (event && event.data) {\n        const clickedData = event.data;\n        console.log(`Root cause bar clicked for category: ${clickedData.group}, month: ${clickedData.key}`);\n\n        // You can implement additional functionality here, such as showing more details\n        // or navigating to the MetisXFactors Group tab with this specific category\n        alert(`Clicked on ${clickedData.group} for ${clickedData.key}\\nFail Rate: ${clickedData.value}%`);\n      }\n    },\n\n    handleRootCauseTimeRangeChange() {\n      console.log(`Root cause time range changed to: ${this.rootCauseTimeRange}`);\n      this.loadRootCauseData();\n    },\n\n    handleRootCauseViewByChange() {\n      console.log(`Root cause time range changed to: ${this.rootCauseTimeRange}`);\n      this.loadRootCauseData();\n    },\n\n    handleRootCauseGroupChange() {\n      console.log(`Root cause group changed to: ${this.rootCauseSelectedGroup}`);\n      console.log('Current chart data length before reload:', this.rootCauseChartData.length);\n      this.loadRootCauseData();\n    },\n\n    getAuthConfig() {\n      // Get authentication token from localStorage or Vuex store\n      const token = localStorage.getItem('token') || this.$store.state.token;\n\n      return {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      };\n    },\n\n    // Navigate to Action Tracker\n    goToActionTracker() {\n      console.log('Navigating to Action Tracker...');\n      this.$router.push('/action-tracker');\n    },\n\n    // Navigate to Validation Page\n    goToValidationPage() {\n      console.log('Navigating to Validation Page...');\n      this.$router.push('/defect-validations');\n    },\n\n    // Navigate to Heatmap tab in Metis XFactors\n    goToHeatmap() {\n      console.log('Navigating to Metis XFactors Heatmap...');\n      this.$router.push('/metis-xfactors?tab=heatmap');\n    },\n\n    // Scroll to alerts section (action tracker alerts section)\n    scrollToAlerts() {\n      console.log('Scrolling to action tracker alerts section...');\n      // Scroll to the Action Tracker Alerts section\n      this.$nextTick(() => {\n        const actionTrackerSection = document.querySelector('.section-card:first-child'); // First section card (Action Tracker Alerts)\n        if (actionTrackerSection) {\n          actionTrackerSection.scrollIntoView({\n            behavior: 'smooth',\n            block: 'start'\n          });\n\n          // Add a brief highlight effect\n          actionTrackerSection.style.transition = 'box-shadow 0.3s ease';\n          actionTrackerSection.style.boxShadow = '0 0 20px rgba(0, 98, 255, 0.5)';\n\n          setTimeout(() => {\n            actionTrackerSection.style.boxShadow = '0 2px 6px rgba(0, 0, 0, 0.2)';\n          }, 2000);\n        }\n      });\n    },\n\n    // Toggle Action Tracker Alerts section\n    toggleActionTrackerAlertsExpanded() {\n      this.isActionTrackerAlertsExpanded = !this.isActionTrackerAlertsExpanded;\n      console.log('Action Tracker Alerts expanded:', this.isActionTrackerAlertsExpanded);\n    },\n\n    // Toggle In-Progress Alerts subsection\n    toggleInProgressAlertsExpanded() {\n      this.isInProgressAlertsExpanded = !this.isInProgressAlertsExpanded;\n      console.log('In-Progress Alerts expanded:', this.isInProgressAlertsExpanded);\n    },\n\n    // Toggle Outstanding Alerts subsection\n    toggleOutstandingAlertsExpanded() {\n      this.isOutstandingAlertsExpanded = !this.isOutstandingAlertsExpanded;\n      console.log('Outstanding Alerts expanded:', this.isOutstandingAlertsExpanded);\n    },\n\n    // Toggle Resolved Alerts subsection\n    toggleResolvedAlertsExpanded() {\n      this.isResolvedAlertsExpanded = !this.isResolvedAlertsExpanded;\n      console.log('Resolved Alerts expanded:', this.isResolvedAlertsExpanded);\n    },\n\n    // View Action Tracker Alert - opens tracking modal\n    async viewActionTrackerAlert(alert) {\n      console.log('Opening tracking modal for alert:', alert);\n\n      // Create a mock action tracker item from the alert data\n      this.selectedTrackingItem = {\n        id: alert.actionTrackerId || alert.id,\n        pn: alert.category || 'Unknown',\n        group: alert.category || 'Unknown',\n        status: alert.status || 'Unknown',\n        priority: alert.severity || 'Medium',\n        progress: 0,\n        commodity: 'Unknown',\n        assignee: 'Unknown',\n        actionItems: []\n      };\n\n      // Load alert data\n      await this.loadAlertUpdates(this.selectedTrackingItem);\n      await this.loadAiInsight(this.selectedTrackingItem, alert);\n      await this.loadAlertHistoryData(this.selectedTrackingItem);\n\n      // Open the modal\n      this.trackingModalVisible = true;\n    },\n\n    // Add critical issue to action tracker\n    async addCriticalIssueToActionTracker(issue) {\n      console.log('Adding critical issue to action tracker:', issue);\n\n      try {\n        const response = await fetch('/api-statit2/update_pqe_action', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...this.getAuthConfig().headers\n          },\n          body: JSON.stringify({\n            issueId: issue.id,\n            category: issue.category,\n            comment: issue.comment || `Critical issue: ${issue.aiDescription}`,\n            severity: issue.severity,\n            pqeOwner: this.pqeOwner,\n            month: issue.month,\n            analysisType: issue.analysisType\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to add to action tracker: ${response.status}`);\n        }\n\n        const data = await response.json();\n        if (data.status_res === 'success') {\n          alert(`Critical issue \"${issue.category}\" has been added to the action tracker with ID: ${data.actionId}`);\n        } else {\n          throw new Error(data.message || 'Failed to add to action tracker');\n        }\n      } catch (error) {\n        console.error('Error adding critical issue to action tracker:', error);\n        alert('Failed to add critical issue to action tracker: ' + error.message);\n      }\n    },\n\n    // Load alert updates for the selected item\n    async loadAlertUpdates(item) {\n      try {\n        const response = await fetch('/api-statit2/get_pqe_alert_history', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...this.getAuthConfig().headers\n          },\n          body: JSON.stringify({\n            actionTrackerId: item.id,\n            pqeOwner: this.pqeOwner\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch alert history: ${response.status}`);\n        }\n\n        const data = await response.json();\n        if (data.status_res === 'success') {\n          this.alertUpdates = data.alert_history || [];\n        } else {\n          console.error('Failed to load alert updates:', data.message);\n          this.alertUpdates = [];\n        }\n      } catch (error) {\n        console.error('Error loading alert updates:', error);\n        this.alertUpdates = [];\n      }\n    },\n\n    // Load AI insight for the selected item\n    async loadAiInsight(item, alertData = null) {\n      this.isLoadingAiInsight = true;\n      try {\n        const alertInfo = alertData || {\n          category: item.group || 'Unknown',\n          severity: item.priority || 'Medium',\n          xFactor: '1.5',\n          status: item.status || 'Unknown'\n        };\n\n        const response = await fetch('/api-statit2/get_pqe_alert_ai_insight', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...this.getAuthConfig().headers\n          },\n          body: JSON.stringify({\n            actionTrackerId: item.id,\n            alertData: alertInfo,\n            pqeOwner: this.pqeOwner\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch AI insight: ${response.status}`);\n        }\n\n        const data = await response.json();\n        if (data.status_res === 'success') {\n          this.aiInsight = data.ai_insight || 'No insight available';\n        } else {\n          console.error('Failed to load AI insight:', data.message);\n          this.aiInsight = 'Unable to generate insight at this time';\n        }\n      } catch (error) {\n        console.error('Error loading AI insight:', error);\n        this.aiInsight = 'Unable to generate insight at this time';\n      } finally {\n        this.isLoadingAiInsight = false;\n      }\n    },\n\n    // Add alert update\n    async addAlertUpdate() {\n      if (!this.newAlertUpdate.trim()) {\n        alert('Please enter an update');\n        return;\n      }\n\n      try {\n        const response = await fetch('/api-statit2/add_pqe_alert_update', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...this.getAuthConfig().headers\n          },\n          body: JSON.stringify({\n            actionTrackerId: this.selectedTrackingItem.id,\n            update: this.newAlertUpdate,\n            updatedBy: this.pqeOwner,\n            alertType: 'PQE Update'\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to add alert update: ${response.status}`);\n        }\n\n        const data = await response.json();\n        if (data.status_res === 'success') {\n          // Add the new alert to the history\n          this.alertUpdates.push(data.alert);\n          this.newAlertUpdate = '';\n          console.log('Alert update added successfully');\n        } else {\n          throw new Error(data.message || 'Failed to add alert update');\n        }\n      } catch (error) {\n        console.error('Error adding alert update:', error);\n        alert('Failed to add alert update: ' + error.message);\n      }\n    },\n\n    // Load alert history data for performance table\n    async loadAlertHistoryData(item) {\n      try {\n        // Mock data for now - this would come from your API\n        this.alertHistoryData = [\n          {\n            month: 'Jan',\n            year: '2024',\n            status: 'Normal',\n            actualRate: '0.5',\n            targetRate: '1.0',\n            xFactor: '0.5',\n            volume: '1000',\n            defects: '5',\n            notes: 'Within target'\n          },\n          {\n            month: 'Feb',\n            year: '2024',\n            status: 'Alert',\n            actualRate: '2.1',\n            targetRate: '1.0',\n            xFactor: '2.1',\n            volume: '1200',\n            defects: '25',\n            notes: 'Above target - investigating'\n          }\n        ];\n      } catch (error) {\n        console.error('Error loading alert history data:', error);\n        this.alertHistoryData = [];\n      }\n    },\n\n    // Get alert row class for styling\n    getAlertRowClass(record) {\n      if (record.status === 'Alert') return 'alert-row';\n      if (record.status === 'Normal') return 'normal-row';\n      return '';\n    },\n\n    // Format date helper\n    formatDate(dateString) {\n      if (!dateString) return '';\n      return new Date(dateString).toLocaleDateString();\n    },\n\n    // Update action item completion\n    updateActionItemCompletion(actionItem, index) {\n      if (actionItem.completed) {\n        actionItem.completedDate = new Date().toISOString();\n      } else {\n        actionItem.completedDate = null;\n      }\n      actionItem.lastUpdated = new Date().toISOString();\n    },\n\n    // Add default action items\n    addDefaultActionItems() {\n      if (!this.selectedTrackingItem.actionItems) {\n        this.selectedTrackingItem.actionItems = [];\n      }\n\n      const defaultItems = [\n        {\n          title: 'Investigate root cause',\n          description: 'Analyze data to identify potential root causes',\n          completed: false,\n          lastUpdated: new Date().toISOString()\n        },\n        {\n          title: 'Implement corrective action',\n          description: 'Execute plan to address identified issues',\n          completed: false,\n          lastUpdated: new Date().toISOString()\n        },\n        {\n          title: 'Monitor results',\n          description: 'Track performance to verify effectiveness',\n          completed: false,\n          lastUpdated: new Date().toISOString()\n        }\n      ];\n\n      this.selectedTrackingItem.actionItems.push(...defaultItems);\n    }\n  }\n};\n</script>\n\n<style scoped>\n.pqe-owner-dashboard-container {\n  color: #f4f4f4;\n}\n\n.content-wrapper {\n  padding: 1rem;\n}\n\n.dashboard-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1.5rem;\n}\n\n.dashboard-header h3 {\n  margin: 0;\n  font-size: 1.5rem;\n  font-weight: 400;\n}\n\n.last-updated-text {\n  font-size: 0.875rem;\n  color: #8d8d8d;\n}\n\n.key-metrics-section {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1rem;\n  margin-bottom: 1.5rem;\n}\n\n.metric-card {\n  background-color: #262626;\n  border-radius: 8px;\n  padding: 1.25rem;\n  display: flex;\n  align-items: center;\n  border: 1px solid #333333;\n  transition: transform 0.2s ease, box-shadow 0.2s ease;\n}\n\n.metric-card.clickable {\n  cursor: pointer;\n\n  &:hover {\n    transform: translateY(-3px);\n    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);\n    background-color: #353535; /* Darker gray on hover */\n  }\n\n  &:active {\n    transform: translateY(-1px);\n    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);\n  }\n}\n\n.metric-icon {\n  margin-right: 1rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 48px;\n  height: 48px;\n  border-radius: 50%;\n}\n\n.metric-icon.alerts {\n  background-color: rgba(250, 77, 86, 0.1);\n  color: #fa4d56;\n}\n\n.metric-icon.in-progress {\n  background-color: rgba(255, 131, 43, 0.1);\n  color: #ff832b;\n}\n\n.metric-icon.validated {\n  background-color: rgba(36, 161, 72, 0.1);\n  color: #24a148;\n}\n\n.metric-icon.new-issues {\n  background-color: rgba(15, 98, 254, 0.1);\n  color: #0f62fe;\n}\n\n.metric-icon.critical-issues {\n  background-color: rgba(250, 77, 86, 0.1);\n  color: #fa4d56;\n}\n\n.metric-icon.unvalidated {\n  background-color: rgba(255, 131, 43, 0.1);\n  color: #ff832b;\n}\n\n.metric-icon.groups-over-target {\n  background-color: rgba(138, 63, 252, 0.1);\n  color: #8a3ffc;\n}\n\n/* Action Tracker Alerts Styles */\n.alert-subsection {\n  margin-bottom: 1.5rem;\n  border: 1px solid #393939;\n  border-radius: 8px;\n  background-color: #2a2a2a;\n}\n\n.subsection-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 1rem 1.25rem;\n  cursor: pointer;\n  border-bottom: 1px solid #393939;\n  transition: background-color 0.2s ease;\n}\n\n.subsection-header:hover {\n  background-color: #333333;\n}\n\n.subsection-title {\n  color: #f4f4f4;\n  font-size: 1rem;\n  font-weight: 500;\n  margin: 0;\n}\n\n.subsection-controls {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n}\n\n.subsection-content {\n  padding: 1rem 1.25rem;\n}\n\n.action-tracker-alert {\n  background-color: #333333;\n  border: 1px solid #525252;\n  border-radius: 6px;\n  margin-bottom: 0.75rem;\n  transition: all 0.2s ease;\n}\n\n.action-tracker-alert:hover {\n  background-color: #393939;\n  border-color: #6f6f6f;\n}\n\n.action-tracker-alert .issue-header {\n  padding: 1rem;\n}\n\n.action-tracker-alert .issue-tags {\n  display: flex;\n  gap: 0.5rem;\n  margin-bottom: 0.5rem;\n}\n\n.action-tracker-alert .issue-title {\n  color: #f4f4f4;\n  font-size: 1rem;\n  font-weight: 500;\n  margin-bottom: 0.5rem;\n  display: block;\n}\n\n.action-tracker-alert .issue-metadata {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n}\n\n.action-tracker-alert .issue-multiplier {\n  font-size: 0.875rem;\n  font-weight: 600;\n  padding: 0.25rem 0.5rem;\n  border-radius: 4px;\n}\n\n.action-tracker-alert .issue-multiplier.medium-severity {\n  background-color: rgba(255, 131, 43, 0.2);\n  color: #ff832b;\n}\n\n.action-tracker-alert .issue-multiplier.good-performance {\n  background-color: rgba(36, 161, 72, 0.2);\n  color: #24a148;\n}\n\n.no-data-message {\n  text-align: center;\n  color: #8d8d8d;\n  font-style: italic;\n  padding: 2rem;\n}\n\n.metric-content {\n  display: flex;\n  flex-direction: column;\n}\n\n.metric-label {\n  font-size: 0.875rem;\n  color: #8d8d8d;\n  margin-bottom: 0.25rem;\n}\n\n.metric-value {\n  font-size: 1.5rem;\n  font-weight: 600;\n  margin-bottom: 0.25rem;\n}\n\n.metric-description {\n  font-size: 0.75rem;\n  color: #8d8d8d;\n}\n\n.chart-section {\n  margin-bottom: 1.5rem;\n}\n\n.chart-controls {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 1rem;\n  padding: 1rem;\n  background-color: #262626;\n  border-bottom: 1px solid #333333;\n}\n\n.control-group {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.control-label {\n  color: #8d8d8d;\n  font-size: 0.875rem;\n  white-space: nowrap;\n}\n\n.control-dropdown {\n  width: 200px;\n}\n\n.chart-container {\n  height: 500px;\n  padding: 1rem;\n  background-color: #161616;\n  border-radius: 8px;\n  margin: 1rem;\n}\n\n.section-footer {\n  padding: 0 1.25rem 1.25rem;\n}\n\n.section-description {\n  margin: 0;\n  font-size: 0.875rem;\n  color: #8d8d8d;\n  line-height: 1.4;\n}\n\n.dashboard-main-content {\n  display: grid;\n  grid-template-columns: 1fr;\n  gap: 1.5rem;\n}\n\n.dashboard-column {\n  display: flex;\n  flex-direction: column;\n  gap: 1.5rem;\n}\n\n.section-card {\n  background-color: #262626;\n  border-radius: 8px;\n  overflow: hidden;\n  border: 1px solid #333333;\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 1.25rem;\n  cursor: pointer;\n  transition: background-color 0.2s ease;\n}\n\n.section-header:hover {\n  background-color: #333333;\n}\n\n.section-title-container {\n  display: flex;\n  flex-direction: column;\n}\n\n.section-title {\n  margin: 0;\n  font-size: 1.25rem;\n  font-weight: 400;\n}\n\n.section-subtitle {\n  font-size: 0.875rem;\n  color: #8d8d8d;\n  margin-top: 0.25rem;\n}\n\n.section-controls {\n  display: flex;\n  align-items: center;\n}\n\n.status-indicator {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 24px;\n  height: 24px;\n  border-radius: 50%;\n  background-color: #fa4d56;\n  color: #ffffff;\n  font-size: 0.75rem;\n  font-weight: 600;\n  margin-right: 0.75rem;\n}\n\n.status-indicator.flashing {\n  animation: flash 2s infinite;\n}\n\n.status-indicator.resolved-indicator {\n  background-color: #24a148;\n}\n\n.status-indicator.outstanding-indicator {\n  background-color: #0f62fe;\n}\n\n@keyframes flash {\n  0%, 100% { opacity: 1; }\n  50% { opacity: 0.5; }\n}\n\n.expand-indicator {\n  transition: transform 0.2s ease;\n}\n\n.expand-indicator.expanded {\n  transform: rotate(180deg);\n}\n\n.section-content {\n  padding: 0 1.25rem 1.25rem;\n}\n\n.filter-container {\n  margin-bottom: 1.5rem;\n  background-color: #333333;\n  padding: 1rem;\n  border-radius: 8px;\n}\n\n.filter-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1rem;\n}\n\n.filter-title {\n  margin: 0;\n  font-size: 1rem;\n  font-weight: 400;\n}\n\n.filter-controls {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 1rem;\n}\n\n.filter-group {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.filter-label {\n  color: #8d8d8d;\n  font-size: 0.875rem;\n  white-space: nowrap;\n}\n\n.filter-dropdown {\n  width: 200px;\n}\n\n.issues-list {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n\n.issue-card {\n  background-color: #333333;\n  border-radius: 8px;\n  overflow: hidden;\n  transition: transform 0.2s ease, box-shadow 0.2s ease;\n  cursor: pointer;\n}\n\n.issue-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);\n}\n\n.issue-header {\n  display: flex;\n  align-items: center;\n  padding: 1rem;\n  position: relative;\n}\n\n.issue-tags {\n  display: flex;\n  gap: 0.5rem;\n  margin-right: 1rem;\n}\n\n.issue-title {\n  flex-grow: 1;\n  font-weight: 500;\n}\n\n.issue-metadata {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  margin-right: 1rem;\n}\n\n.issue-multiplier {\n  font-weight: 600;\n  padding: 0.125rem 0.375rem;\n  border-radius: 4px;\n}\n\n.issue-multiplier.high-severity {\n  background-color: rgba(250, 77, 86, 0.1);\n  color: #fa4d56;\n}\n\n.issue-multiplier.medium-severity {\n  background-color: rgba(255, 131, 43, 0.1);\n  color: #ff832b;\n}\n\n.issue-multiplier.good-performance {\n  background-color: rgba(36, 161, 72, 0.1);\n  color: #24a148;\n}\n\n.issue-multiplier.medium-performance {\n  background-color: rgba(255, 131, 43, 0.1);\n  color: #ff832b;\n}\n\n.resolved-issue-card {\n  border-left: 4px solid #24a148;\n}\n\n.outstanding-issue-card {\n  border-left: 4px solid #0f62fe;\n}\n\n.resolution-details, .acceptance-details {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 1rem;\n  font-size: 0.875rem;\n  color: #c6c6c6;\n}\n\n.issue-multiplier.low-performance {\n  background-color: rgba(36, 161, 72, 0.1);\n  color: #24a148;\n}\n\n.resolution-comment, .acceptance-comment {\n  margin: 1rem 0;\n  padding: 0.75rem;\n  background-color: #333333;\n  border-radius: 4px;\n}\n\n.comment-label {\n  font-weight: 600;\n  margin-bottom: 0.5rem;\n  color: #c6c6c6;\n}\n\n.comment-text {\n  font-size: 0.875rem;\n  white-space: pre-wrap;\n}\n\n.issue-content {\n  padding: 0 1rem 1rem;\n  border-top: 1px solid #444444;\n}\n\n.ai-description {\n  margin-bottom: 1rem;\n  padding: 1rem;\n  background-color: #262626;\n  border-radius: 8px;\n  font-size: 0.875rem;\n  line-height: 1.5;\n}\n\n.action-comment {\n  margin-bottom: 1rem;\n}\n\n.issue-actions {\n  display: flex;\n  justify-content: flex-end;\n  gap: 0.5rem;\n}\n\n.no-data-message {\n  color: #8d8d8d;\n  font-size: 1rem;\n  text-align: center;\n  padding: 2rem;\n}\n\n@media (max-width: 768px) {\n  .key-metrics-section {\n    grid-template-columns: 1fr;\n  }\n\n  .dashboard-main-content {\n    grid-template-columns: 1fr;\n  }\n\n  .filter-controls {\n    flex-direction: column;\n  }\n\n  .filter-group {\n    width: 100%;\n  }\n}\n\n/* Issue actions styling */\n.issue-actions {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 0.5rem;\n  margin-top: 1rem;\n  padding-top: 1rem;\n  border-top: 1px solid #444444;\n}\n\n.issue-actions .cv-button {\n  flex: 0 0 auto;\n}\n\n@media (max-width: 768px) {\n  .issue-actions {\n    flex-direction: column;\n  }\n\n  .issue-actions .cv-button {\n    width: 100%;\n  }\n}\n\n/* Action Tracker Modal Styles */\n.tracking-modal .cv-modal-container {\n  max-width: 95vw;\n  width: 1200px;\n}\n\n.tracking-modal-content {\n  padding: 1rem;\n  color: #f4f4f4;\n}\n\n.tracking-tab-content {\n  padding: 1.5rem;\n  background-color: #262626;\n  border-radius: 8px;\n  margin-top: 1rem;\n}\n\n.section-title {\n  color: #f4f4f4;\n  font-size: 1.25rem;\n  font-weight: 600;\n  margin: 0 0 1rem 0;\n  border-bottom: 1px solid #444444;\n  padding-bottom: 0.5rem;\n}\n\n.section-description {\n  color: #c6c6c6;\n  font-size: 0.875rem;\n  margin-bottom: 2rem;\n}\n\n.subsection-title {\n  color: #f4f4f4;\n  font-size: 1rem;\n  font-weight: 500;\n  margin: 0 0 1rem 0;\n  border-bottom: 1px solid #444444;\n  padding-bottom: 0.5rem;\n}\n\n/* New Alert Functionality Styles */\n.new-alerts-section {\n  padding: 0;\n}\n\n.ai-insight-section {\n  background-color: #333333;\n  border-radius: 8px;\n  padding: 1.5rem;\n  margin-bottom: 2rem;\n  border: 1px solid #444444;\n}\n\n.ai-insight-content {\n  background-color: #262626;\n  border-radius: 6px;\n  padding: 1rem;\n  border: 1px solid #525252;\n  font-size: 0.875rem;\n  line-height: 1.5;\n  color: #f4f4f4;\n}\n\n.loading-message {\n  text-align: center;\n  color: #8d8d8d;\n  font-style: italic;\n  padding: 1rem;\n}\n\n.add-alert-section {\n  background-color: #333333;\n  border-radius: 8px;\n  padding: 1.5rem;\n  margin-bottom: 2rem;\n  border: 1px solid #444444;\n}\n\n.add-update-form {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n\n.update-form-actions {\n  display: flex;\n  justify-content: flex-end;\n}\n\n.alert-updates-section {\n  background-color: #333333;\n  border-radius: 8px;\n  padding: 1.5rem;\n  border: 1px solid #444444;\n}\n\n.alert-updates-table {\n  background-color: #262626;\n  border-radius: 6px;\n  overflow: hidden;\n  border: 1px solid #525252;\n}\n\n.updates-header {\n  display: grid;\n  grid-template-columns: 120px 1fr 150px;\n  background-color: #393939;\n  border-bottom: 1px solid #525252;\n}\n\n.update-column {\n  padding: 0.75rem;\n  font-weight: 600;\n  color: #f4f4f4;\n  border-right: 1px solid #525252;\n}\n\n.update-column:last-child {\n  border-right: none;\n}\n\n.update-row {\n  display: grid;\n  grid-template-columns: 120px 1fr 150px;\n  border-bottom: 1px solid #525252;\n}\n\n.update-row:last-child {\n  border-bottom: none;\n}\n\n.update-cell {\n  padding: 0.75rem;\n  color: #f4f4f4;\n  border-right: 1px solid #525252;\n  font-size: 0.875rem;\n}\n\n.update-cell:last-child {\n  border-right: none;\n}\n\n.no-updates-message {\n  text-align: center;\n  color: #8d8d8d;\n  font-style: italic;\n  padding: 2rem;\n}\n\n/* Action Items Tab Styles */\n.tracking-section {\n  background-color: #333333;\n  border-radius: 8px;\n  padding: 1.5rem;\n  margin-bottom: 2rem;\n  border: 1px solid #444444;\n}\n\n.tracking-section-title {\n  color: #f4f4f4;\n  font-size: 1.125rem;\n  font-weight: 600;\n  margin: 0 0 1rem 0;\n  border-bottom: 1px solid #444444;\n  padding-bottom: 0.5rem;\n}\n\n.action-summary {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1rem;\n}\n\n.summary-item {\n  display: flex;\n  flex-direction: column;\n  gap: 0.25rem;\n}\n\n.summary-label {\n  font-weight: 600;\n  color: #c6c6c6;\n  font-size: 0.875rem;\n}\n\n.summary-value {\n  color: #f4f4f4;\n  font-size: 1rem;\n}\n\n.priority-badge {\n  padding: 0.25rem 0.5rem;\n  border-radius: 4px;\n  font-size: 0.75rem;\n  font-weight: 600;\n  text-transform: uppercase;\n  width: fit-content;\n}\n\n.priority-badge.high {\n  background-color: #da1e28;\n  color: #ffffff;\n}\n\n.priority-badge.medium {\n  background-color: #f1c21b;\n  color: #000000;\n}\n\n.priority-badge.low {\n  background-color: #24a148;\n  color: #ffffff;\n}\n\n.action-items-list {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n\n.action-item-card {\n  background-color: #262626;\n  border-radius: 6px;\n  padding: 1rem;\n  border: 1px solid #525252;\n}\n\n.action-item-card.completed {\n  opacity: 0.7;\n  background-color: #1e3a1e;\n}\n\n.action-item-header {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n}\n\n.completion-date,\n.last-updated {\n  font-size: 0.75rem;\n  color: #8d8d8d;\n}\n\n.action-item-description {\n  margin-top: 0.5rem;\n  color: #c6c6c6;\n  font-size: 0.875rem;\n}\n\n.no-action-items {\n  text-align: center;\n  padding: 2rem;\n  color: #8d8d8d;\n}\n\n/* Performance Chart & History Tab Styles */\n.chart-section {\n  background-color: #333333;\n  border-radius: 8px;\n  padding: 1.5rem;\n  margin-bottom: 2rem;\n  border: 1px solid #444444;\n}\n\n.performance-history-section {\n  background-color: #333333;\n  border-radius: 8px;\n  padding: 1.5rem;\n  border: 1px solid #444444;\n}\n\n.alert-history-table {\n  margin-top: 1rem;\n}\n\n.status-cell {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.status-indicator {\n  width: 8px;\n  height: 8px;\n  border-radius: 50%;\n}\n\n.status-indicator.normal {\n  background-color: #24a148;\n}\n\n.status-indicator.alert {\n  background-color: #da1e28;\n}\n\n.status-text.normal {\n  color: #24a148;\n}\n\n.status-text.alert {\n  color: #da1e28;\n}\n\n.alert-row {\n  background-color: rgba(218, 30, 40, 0.1);\n}\n\n.normal-row {\n  background-color: rgba(36, 161, 72, 0.1);\n}\n\n.no-alert-data {\n  text-align: center;\n  color: #8d8d8d;\n  padding: 2rem;\n}\n\n.no-alert-data .note {\n  font-size: 0.875rem;\n  font-style: italic;\n}\n\n@media (max-width: 768px) {\n  .tracking-modal .cv-modal-container {\n    max-width: 95vw;\n    width: 95vw;\n  }\n\n  .updates-header,\n  .update-row {\n    grid-template-columns: 1fr;\n  }\n\n  .update-column,\n  .update-cell {\n    border-right: none;\n    border-bottom: 1px solid #525252;\n  }\n\n  .action-summary {\n    grid-template-columns: 1fr;\n  }\n}\n</style>\n"]}]}