{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\PQEDashboard\\PQEOwnerDashboard.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\PQEDashboard\\PQEOwnerDashboard.vue", "mtime": 1748987780911}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["PQEOwnerDashboard.vue"], "names": [], "mappings": ";AAsf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file": "PQEOwnerDashboard.vue", "sourceRoot": "src/views/PQEDashboard", "sourcesContent": ["<template>\n  <div class=\"pqe-owner-dashboard-container\">\n    <div class=\"content-wrapper\">\n      <!-- Header Section with Critical Issues Summary -->\n      <div class=\"dashboard-header\">\n        <h3>{{ pqeOwner }}'s Dashboard</h3>\n        <div class=\"last-updated-text\">\n          Last Updated: {{ new Date().toLocaleString() }}\n        </div>\n      </div>\n\n      <!-- Key Metrics Section -->\n      <div class=\"key-metrics-section\">\n        <div class=\"metric-card clickable\" @click=\"scrollToAlerts\" title=\"Click to go to alerts section\">\n          <div class=\"metric-icon alerts\">\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n              <path d=\"M16,2A14,14,0,1,0,30,16,14,14,0,0,0,16,2Zm0,26A12,12,0,1,1,28,16,12,12,0,0,1,16,28Z\"></path>\n              <path d=\"M15,8h2v11H15Zm1,14a1.5,1.5,0,1,1-1.5,1.5A1.5,1.5,0,0,1,16,22Z\"></path>\n            </svg>\n          </div>\n          <div class=\"metric-content\">\n            <div class=\"metric-label\"># Alerts</div>\n            <div class=\"metric-value\">{{ totalAlerts }}</div>\n            <div class=\"metric-description\">Current Month</div>\n          </div>\n        </div>\n\n        <div class=\"metric-card clickable\" @click=\"goToActionTracker\" title=\"Click to go to Action Tracker\">\n          <div class=\"metric-icon in-progress\">\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n              <path d=\"M16,2A14,14,0,1,0,30,16,14,14,0,0,0,16,2Zm0,26A12,12,0,1,1,28,16,12,12,0,0,1,16,28Z\"></path>\n              <path d=\"M20.59,22,15,16.41V7h2v8.58l5,5.01L20.59,22z\"></path>\n            </svg>\n          </div>\n          <div class=\"metric-content\">\n            <div class=\"metric-label\"># In Progress Issues</div>\n            <div class=\"metric-value\">{{ inProgressIssuesCount }}</div>\n            <div class=\"metric-description\">From Action Tracker</div>\n          </div>\n        </div>\n\n        <div class=\"metric-card clickable\" @click=\"goToValidationPage\" title=\"Click to go to Validation page\">\n          <div class=\"metric-icon validated\">\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n              <path d=\"M26,4H6A2,2,0,0,0,4,6V26a2,2,0,0,0,2,2H26a2,2,0,0,0,2-2V6A2,2,0,0,0,26,4ZM14,21.5l-5-5,1.59-1.5L14,18.35,21.41,11,23,12.58Z\"></path>\n            </svg>\n          </div>\n          <div class=\"metric-content\">\n            <div class=\"metric-label\">Validated Fails</div>\n            <div class=\"metric-value\">{{ validatedCount }}/{{ totalFails }}</div>\n            <div class=\"metric-description\">This Month</div>\n          </div>\n        </div>\n\n        <div class=\"metric-card clickable\" @click=\"goToHeatmap\" title=\"Click to go to Heatmap\">\n          <div class=\"metric-icon groups-over-target\">\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n              <path d=\"M4,20V12a2,2,0,0,1,2-2H8a2,2,0,0,1,2,2v8a2,2,0,0,1-2,2H6A2,2,0,0,1,4,20Z\"></path>\n              <path d=\"M12,20V6a2,2,0,0,1,2-2h2a2,2,0,0,1,2,2V20a2,2,0,0,1-2,2H14A2,2,0,0,1,12,20Z\"></path>\n              <path d=\"M20,20V16a2,2,0,0,1,2-2h2a2,2,0,0,1,2,2v4a2,2,0,0,1-2,2H22A2,2,0,0,1,20,20Z\"></path>\n            </svg>\n          </div>\n          <div class=\"metric-content\">\n            <div class=\"metric-label\"># Groups Over Target</div>\n            <div class=\"metric-value\">{{ groupsOverTargetCount }}</div>\n            <div class=\"metric-description\">Current Month</div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Main Content Layout -->\n      <div class=\"dashboard-main-content\">\n        <!-- Left Column -->\n        <div class=\"dashboard-column\">\n          <!-- Action Tracker Alerts Section -->\n          <div class=\"section-card\">\n            <div class=\"section-header\" @click=\"toggleActionTrackerAlertsExpanded\">\n              <div class=\"section-title-container\">\n                <h4 class=\"section-title\">Action Tracker Alerts</h4>\n                <div class=\"section-subtitle\">\n                  Issues from action tracker above target for the month\n                </div>\n              </div>\n              <div class=\"section-controls\">\n                <div class=\"status-indicator flashing\" v-if=\"totalActionTrackerAlerts > 0\">\n                  {{ totalActionTrackerAlerts }}\n                </div>\n                <div class=\"expand-indicator\" :class=\"{ expanded: isActionTrackerAlertsExpanded }\">\n                  <svg width=\"16\" height=\"16\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                    <path d=\"M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z\"></path>\n                  </svg>\n                </div>\n              </div>\n            </div>\n\n            <div v-if=\"isActionTrackerAlertsExpanded\" class=\"section-content\">\n              <!-- In-Progress Issue Alerts -->\n              <div class=\"alert-subsection\">\n                <div class=\"subsection-header\" @click=\"toggleInProgressAlertsExpanded\">\n                  <h5 class=\"subsection-title\">In-Progress Issue Alerts</h5>\n                  <div class=\"subsection-controls\">\n                    <div class=\"status-indicator\" v-if=\"inProgressAlerts.length > 0\" style=\"background-color: #ff832b;\">\n                      {{ inProgressAlerts.length }}\n                    </div>\n                    <div class=\"expand-indicator\" :class=\"{ expanded: isInProgressAlertsExpanded }\">\n                      <svg width=\"12\" height=\"12\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                        <path d=\"M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z\"></path>\n                      </svg>\n                    </div>\n                  </div>\n                </div>\n\n                <div v-if=\"isInProgressAlertsExpanded\" class=\"subsection-content\">\n                  <div v-if=\"inProgressAlerts.length > 0\" class=\"issues-list\">\n                    <div v-for=\"alert in inProgressAlerts\" :key=\"alert.id\" class=\"issue-card action-tracker-alert\">\n                      <div class=\"issue-header\">\n                        <div class=\"issue-tags\">\n                          <cv-tag kind=\"orange\" label=\"In-Progress\" />\n                          <cv-tag kind=\"red\" :label=\"alert.severity\" />\n                        </div>\n                        <div class=\"issue-title\">{{ alert.category }}</div>\n                        <div class=\"issue-metadata\">\n                          <span class=\"issue-multiplier medium-severity\">{{ alert.xFactor }}x</span>\n                        </div>\n                      </div>\n                      <div class=\"issue-actions\">\n                        <cv-button\n                          kind=\"primary\"\n                          size=\"small\"\n                          @click=\"viewActionTrackerAlert(alert)\"\n                        >\n                          View\n                        </cv-button>\n                      </div>\n                    </div>\n                  </div>\n                  <div v-else class=\"no-data-message\">\n                    No in-progress issues above target this month.\n                  </div>\n                </div>\n              </div>\n\n              <!-- Outstanding Issue Alerts -->\n              <div class=\"alert-subsection\">\n                <div class=\"subsection-header\" @click=\"toggleOutstandingAlertsExpanded\">\n                  <h5 class=\"subsection-title\">Outstanding Issue Alerts</h5>\n                  <div class=\"subsection-controls\">\n                    <div class=\"status-indicator\" v-if=\"outstandingAlerts.length > 0\" style=\"background-color: #0f62fe;\">\n                      {{ outstandingAlerts.length }}\n                    </div>\n                    <div class=\"expand-indicator\" :class=\"{ expanded: isOutstandingAlertsExpanded }\">\n                      <svg width=\"12\" height=\"12\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                        <path d=\"M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z\"></path>\n                      </svg>\n                    </div>\n                  </div>\n                </div>\n\n                <div v-if=\"isOutstandingAlertsExpanded\" class=\"subsection-content\">\n                  <div v-if=\"outstandingAlerts.length > 0\" class=\"issues-list\">\n                    <div v-for=\"alert in outstandingAlerts\" :key=\"alert.id\" class=\"issue-card action-tracker-alert\">\n                      <div class=\"issue-header\">\n                        <div class=\"issue-tags\">\n                          <cv-tag kind=\"blue\" label=\"Outstanding\" />\n                          <cv-tag kind=\"red\" :label=\"alert.severity\" />\n                        </div>\n                        <div class=\"issue-title\">{{ alert.category }}</div>\n                        <div class=\"issue-metadata\">\n                          <span class=\"issue-multiplier medium-severity\">{{ alert.xFactor }}x</span>\n                        </div>\n                      </div>\n                      <div class=\"issue-actions\">\n                        <cv-button\n                          kind=\"primary\"\n                          size=\"small\"\n                          @click=\"viewActionTrackerAlert(alert)\"\n                        >\n                          View\n                        </cv-button>\n                      </div>\n                    </div>\n                  </div>\n                  <div v-else class=\"no-data-message\">\n                    No outstanding issues above target this month.\n                  </div>\n                </div>\n              </div>\n\n              <!-- Resolved Issue Alerts -->\n              <div class=\"alert-subsection\">\n                <div class=\"subsection-header\" @click=\"toggleResolvedAlertsExpanded\">\n                  <h5 class=\"subsection-title\">Resolved Issue Alerts</h5>\n                  <div class=\"subsection-controls\">\n                    <div class=\"status-indicator\" v-if=\"resolvedAlerts.length > 0\" style=\"background-color: #24a148;\">\n                      {{ resolvedAlerts.length }}\n                    </div>\n                    <div class=\"expand-indicator\" :class=\"{ expanded: isResolvedAlertsExpanded }\">\n                      <svg width=\"12\" height=\"12\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                        <path d=\"M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z\"></path>\n                      </svg>\n                    </div>\n                  </div>\n                </div>\n\n                <div v-if=\"isResolvedAlertsExpanded\" class=\"subsection-content\">\n                  <div v-if=\"resolvedAlerts.length > 0\" class=\"issues-list\">\n                    <div v-for=\"alert in resolvedAlerts\" :key=\"alert.id\" class=\"issue-card action-tracker-alert\">\n                      <div class=\"issue-header\">\n                        <div class=\"issue-tags\">\n                          <cv-tag kind=\"green\" label=\"Resolved\" />\n                          <cv-tag kind=\"red\" :label=\"alert.severity\" />\n                        </div>\n                        <div class=\"issue-title\">{{ alert.category }}</div>\n                        <div class=\"issue-metadata\">\n                          <span class=\"issue-multiplier good-performance\">{{ alert.xFactor }}x</span>\n                        </div>\n                      </div>\n                      <div class=\"issue-actions\">\n                        <cv-button\n                          kind=\"primary\"\n                          size=\"small\"\n                          @click=\"viewActionTrackerAlert(alert)\"\n                        >\n                          View\n                        </cv-button>\n                      </div>\n                    </div>\n                  </div>\n                  <div v-else class=\"no-data-message\">\n                    No resolved issues above target this month.\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Critical Issues Section -->\n          <div class=\"section-card\">\n            <div class=\"section-header critical-issues-header\" @click=\"toggleCriticalIssuesExpanded\">\n              <div class=\"section-title-container\">\n                <h4 class=\"section-title\">Critical Issues</h4>\n                <div class=\"section-subtitle\">New/unknown issues requiring immediate attention this month</div>\n              </div>\n              <div class=\"section-controls\">\n                <div class=\"status-indicator\" :class=\"{ 'flashing': !isCriticalIssuesExpanded && unresolvedCriticalIssues.length > 0 }\">\n                  {{ unresolvedCriticalIssues.length }}\n                </div>\n                <div class=\"expand-indicator\" :class=\"{ 'expanded': isCriticalIssuesExpanded }\">\n                  <svg width=\"16\" height=\"16\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                    <path d=\"M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z\"></path>\n                  </svg>\n                </div>\n              </div>\n            </div>\n\n            <div v-if=\"isCriticalIssuesExpanded\" class=\"section-content\">\n              <!-- Filter Controls in a Single Row -->\n              <div class=\"filter-container\">\n                <div class=\"filter-header\">\n                  <h5 class=\"filter-title\">Filter Issues</h5>\n                  <cv-button\n                    kind=\"ghost\"\n                    size=\"small\"\n                    class=\"clear-filters-button\"\n                    @click=\"clearFilters\"\n                    v-if=\"isFiltersActive\"\n                  >\n                    Clear Filters\n                  </cv-button>\n                </div>\n\n                <div class=\"filter-controls\">\n                  <div class=\"filter-group\">\n                    <label for=\"severity-dropdown\" class=\"filter-label\">Severity:</label>\n                    <cv-dropdown\n                      id=\"severity-dropdown\"\n                      v-model=\"severityFilter\"\n                      @change=\"handleSeverityFilterChange\"\n                      class=\"filter-dropdown\"\n                    >\n                      <cv-dropdown-item value=\"all\">All Severities</cv-dropdown-item>\n                      <cv-dropdown-item value=\"high\">High</cv-dropdown-item>\n                      <cv-dropdown-item value=\"medium\">Medium</cv-dropdown-item>\n                    </cv-dropdown>\n                  </div>\n\n                  <div class=\"filter-group\">\n                    <label for=\"analysis-dropdown\" class=\"filter-label\">Analysis Type:</label>\n                    <cv-dropdown\n                      id=\"analysis-dropdown\"\n                      v-model=\"analysisTypeFilter\"\n                      @change=\"handleAnalysisFilterChange\"\n                      class=\"filter-dropdown\"\n                    >\n                      <cv-dropdown-item value=\"all\">All Analysis Types</cv-dropdown-item>\n                      <cv-dropdown-item value=\"Root Cause\">Root Cause</cv-dropdown-item>\n                    </cv-dropdown>\n                  </div>\n                </div>\n              </div>\n\n              <!-- Critical Issues List -->\n              <div v-if=\"filteredCriticalIssues.length > 0\" class=\"issues-list\">\n                <div\n                  v-for=\"issue in filteredCriticalIssues\"\n                  :key=\"issue.id\"\n                  class=\"issue-card\"\n                  @click=\"toggleIssueExpanded(issue)\"\n                  :class=\"{ 'expanded': isIssueExpanded(issue.id) }\"\n                >\n                  <div class=\"issue-header\">\n                    <div class=\"issue-tags\">\n                      <cv-tag\n                        :kind=\"issue.severity === 'high' ? 'red' : 'magenta'\"\n                        :label=\"issue.severity === 'high' ? 'High' : 'Medium'\"\n                      />\n                      <cv-tag\n                        kind=\"purple\"\n                        class=\"analysis-tag\"\n                        :label=\"issue.analysisType\"\n                      />\n                    </div>\n                    <span class=\"issue-title\">{{ issue.category }}</span>\n                    <div class=\"issue-metadata\">\n                      <cv-tag\n                        kind=\"cool-gray\"\n                        class=\"month-tag\"\n                        :label=\"issue.month\"\n                      />\n                      <span class=\"issue-multiplier\" :class=\"issue.severity === 'high' ? 'high-severity' : 'medium-severity'\">\n                        {{ issue.increaseMultiplier === '(new)' ? '(new)' : `${issue.increaseMultiplier}x` }}\n                      </span>\n                    </div>\n                    <div class=\"expand-indicator\">\n                      <svg width=\"16\" height=\"16\" viewBox=\"0 0 32 32\" fill=\"currentColor\">\n                        <path d=\"M16 22L6 12 7.4 10.6 16 19.2 24.6 10.6 26 12z\"></path>\n                      </svg>\n                    </div>\n                  </div>\n\n                  <div class=\"issue-content\" v-if=\"isIssueExpanded(issue.id)\">\n                    <div class=\"ai-description\">\n                      <p>{{ issue.aiDescription || 'Loading AI description...' }}</p>\n                    </div>\n\n                    <!-- Action Comment Text Box -->\n                    <div class=\"action-comment\">\n                      <cv-text-area\n                        v-model=\"issue.comment\"\n                        label=\"Action Comments\"\n                        placeholder=\"Add your comments or action plan here...\"\n                        :helper-text=\"issue.comment ? `${issue.comment.length} characters` : 'This will be added to the Action Tracker'\"\n                      ></cv-text-area>\n                    </div>\n\n                    <div class=\"issue-actions\">\n                      <cv-button\n                        kind=\"tertiary\"\n                        size=\"small\"\n                        @click.stop=\"viewIssueDetails(issue)\"\n                      >\n                        View Data\n                      </cv-button>\n                      <cv-button\n                        kind=\"tertiary\"\n                        size=\"small\"\n                        @click.stop=\"updateIssue(issue)\"\n                      >\n                        Update\n                      </cv-button>\n                      <cv-button\n                        kind=\"secondary\"\n                        size=\"small\"\n                        @click.stop=\"updateIssue(issue, false, true)\"\n                      >\n                        Mark Outstanding\n                      </cv-button>\n                      <cv-button\n                        kind=\"primary\"\n                        size=\"small\"\n                        @click.stop=\"updateIssue(issue, true, false)\"\n                      >\n                        Mark Resolved\n                      </cv-button>\n                      <cv-button\n                        kind=\"ghost\"\n                        size=\"small\"\n                        @click.stop=\"addCriticalIssueToActionTracker(issue)\"\n                      >\n                        Add item to action tracker\n                      </cv-button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <div v-else class=\"no-data-message\">\n                No critical issues found matching the selected filters.\n              </div>\n            </div>\n          </div>\n\n          <!-- Root Cause Analysis Section -->\n          <div class=\"section-card chart-section\">\n            <div class=\"section-header\">\n              <div class=\"section-title-container\">\n                <h4 class=\"section-title\">Analysis</h4>\n                <div class=\"section-subtitle\">Defect categories and fail rates over time</div>\n              </div>\n            </div>\n\n            <div class=\"chart-controls\">\n              <div class=\"control-group\">\n                <label class=\"control-label\">View By:</label>\n                <cv-dropdown\n                  v-model=\"rootCauseViewBy\"\n                  @change=\"handleRootCauseViewByChange\"\n                  class=\"control-dropdown\"\n                >\n                  <cv-dropdown-item value=\"rootCause\">Root Cause</cv-dropdown-item>\n                  <cv-dropdown-item value=\"vintage\">Vintage</cv-dropdown-item>\n                  <cv-dropdown-item value=\"sector\">Sector</cv-dropdown-item>\n                  <cv-dropdown-item value=\"supplier\">Supplier</cv-dropdown-item>\n                </cv-dropdown>\n              </div>\n\n              <div class=\"control-group\">\n                <label class=\"control-label\">Time Range:</label>\n                <cv-dropdown\n                  v-model=\"rootCauseTimeRange\"\n                  @change=\"handleRootCauseTimeRangeChange\"\n                  class=\"control-dropdown\"\n                >\n                  <cv-dropdown-item value=\"3month\">3 Months</cv-dropdown-item>\n                  <cv-dropdown-item value=\"6month\">6 Months</cv-dropdown-item>\n                </cv-dropdown>\n              </div>\n\n              <div class=\"control-group\" v-if=\"breakoutGroups.length > 0\">\n                <label class=\"control-label\">Group:</label>\n                <cv-dropdown\n                  v-model=\"rootCauseSelectedGroup\"\n                  @change=\"handleRootCauseGroupChange\"\n                  class=\"control-dropdown\"\n                >\n                  <cv-dropdown-item value=\"all\">All Groups</cv-dropdown-item>\n                  <cv-dropdown-item\n                    v-for=\"group in breakoutGroups\"\n                    :key=\"group.name\"\n                    :value=\"group.name\"\n                  >\n                    {{ group.name }}\n                  </cv-dropdown-item>\n                </cv-dropdown>\n              </div>\n            </div>\n\n            <div class=\"chart-container\">\n              <div v-if=\"isRootCauseDataLoading\" >\n                        Loading Chart...\n                        <RootCauseChart :data = [] :loading=\"isRootCauseDataLoading\"/>\n                      </div>\n              <RootCauseChart\n               v-if=\"rootCauseChartData.length > 0\"\n                :data=\"rootCauseChartData\"\n                :viewBy=\"rootCauseViewBy\"\n                :timeRange=\"rootCauseTimeRange\"\n                :selectedGroup=\"rootCauseSelectedGroup\"\n                :loading=\"isRootCauseDataLoading\"\n                @bar-click=\"handleRootCauseBarClick\"\n              />\n\n              <div v-if=\"rootCauseChartData.length == 0 && !isRootCauseDataLoading\" >\n                        No data available\n                      </div>\n              <!-- <div v-else-if=\"isRootCauseDataLoading\" class=\"loading-container\">\n                <p>Loading root cause data...</p>\n              </div>\n              <div v-else class=\"no-data-message\">\n                No root cause data available for the selected criteria.\n              </div> -->\n            </div>\n\n            <!-- <div class=\"section-footer\">\n              <p class=\"section-description\">\n                Root cause analysis showing defect categories and their fail rates over time.\n                Click on bars to see detailed information.\n              </p>\n            </div> -->\n          </div>\n\n        </div>\n      </div>\n    </div>\n  </div>\n\n\n\n\n</template>\n\n<script>\nimport {\n  CvButton,\n  CvTag,\n  CvTextArea,\n  CvDropdown,\n  CvDropdownItem\n} from '@carbon/vue';\n\nimport RootCauseChart from '@/components/RootCauseChart/RootCauseChart';\n\nexport default {\n  name: 'PQEOwnerDashboard',\n  components: {\n    CvButton,\n    CvTag,\n    CvTextArea,\n    CvDropdown,\n    CvDropdownItem,\n    RootCauseChart\n  },\n  props: {\n    pqeOwner: {\n      type: String,\n      required: true\n    }\n  },\n  data() {\n    return {\n      // Critical Issues Data\n      newCriticalIssues: [],\n      unresolvedCriticalIssues: [],\n      expandedIssueIds: [], // Track which issues are expanded\n      isCriticalIssuesExpanded: false, // Track if critical issues section is expanded\n\n      // Action Tracker Alerts Data\n      isActionTrackerAlertsExpanded: false,\n      isInProgressAlertsExpanded: false,\n      isOutstandingAlertsExpanded: false,\n      isResolvedAlertsExpanded: false,\n\n      // Action Tracker Alert Data\n      inProgressAlerts: [],\n      outstandingAlerts: [],\n      resolvedAlerts: [],\n\n      // Filtering for Critical Issues\n      selectedFilters: {\n        severity: [],\n        analysisType: []\n      },\n      severityFilter: 'all',\n      analysisTypeFilter: 'all',\n\n      // Validation Data\n      validatedCount: 0,\n      unvalidatedCount: 0,\n\n      // Breakout Groups Data\n      breakoutGroups: [],\n\n      // Root Cause Chart Data\n      rootCauseChartData: [],\n      isRootCauseDataLoading: true,\n\n      // Root Cause Chart Controls\n      rootCauseViewBy: 'rootCause',\n      rootCauseTimeRange: '6month',\n      rootCauseSelectedGroup: 'all',\n\n      // Loading States\n      isLoading: false\n    };\n  },\n  computed: {\n    // Filtered critical issues based on selected filters\n    filteredCriticalIssues() {\n      // If no filters are selected, return all issues\n      if (this.selectedFilters.severity.length === 0 && this.selectedFilters.analysisType.length === 0) {\n        return this.unresolvedCriticalIssues;\n      }\n\n      // Apply filters\n      return this.unresolvedCriticalIssues.filter(issue => {\n        // Check if issue passes severity filter\n        const passesSeverityFilter = this.selectedFilters.severity.length === 0 ||\n                                    this.selectedFilters.severity.includes(issue.severity);\n\n        // Check if issue passes analysis type filter\n        const passesAnalysisFilter = this.selectedFilters.analysisType.length === 0 ||\n                                    this.selectedFilters.analysisType.includes(issue.analysisType);\n\n        // Issue must pass both filters\n        return passesSeverityFilter && passesAnalysisFilter;\n      });\n    },\n\n    // Check if any filters are active\n    isFiltersActive() {\n      return this.selectedFilters.severity.length > 0 || this.selectedFilters.analysisType.length > 0;\n    },\n\n\n\n    // Calculate total alerts (critical breakout groups)\n    totalAlerts() {\n      return this.breakoutGroups.filter(group => group.xFactor >= 1.5).length;\n    },\n\n    // Calculate total in-progress issues from action tracker\n    inProgressIssuesCount() {\n      // This would normally fetch from action tracker API\n      // For now, return a calculated value based on action tracker alerts\n      return this.inProgressAlerts.length;\n    },\n\n    // Calculate total fails (validated + unvalidated)\n    totalFails() {\n      return this.validatedCount + this.unvalidatedCount;\n    },\n\n    // Calculate groups over target (xFactor > 1.0)\n    groupsOverTargetCount() {\n      return this.breakoutGroups.filter(group => group.xFactor > 1.0).length;\n    },\n\n    // Calculate total action tracker alerts\n    totalActionTrackerAlerts() {\n      return this.inProgressAlerts.length + this.outstandingAlerts.length + this.resolvedAlerts.length;\n    }\n  },\n  watch: {\n    pqeOwner: {\n      immediate: true,\n      handler(newValue, oldValue) {\n        console.log(`PQE Owner changed from ${oldValue} to ${newValue}`);\n        if (newValue) {\n          // Reset the group selection when PQE owner changes\n          this.rootCauseSelectedGroup = 'all';\n          this.loadDashboardData();\n        }\n      }\n    }\n  },\n  methods: {\n    loadDashboardData() {\n      this.loadCriticalIssues();\n      this.loadValidationCounts();\n      this.loadActionTrackerAlerts();\n      this.loadRootCauseData();\n    },\n\n\n\n\n\n    async loadCriticalIssues() {\n      console.log(`Loading critical issues for PQE owner: ${this.pqeOwner}`);\n      this.isLoading = true;\n\n      try {\n        // Get authentication config\n        const config = this.getAuthConfig();\n\n        // First, load the breakout groups for this PQE owner\n        await this.loadBreakoutGroups();\n\n        // Fetch critical issues from the API\n        const response = await fetch('/api-statit2/get_pqe_critical_issues', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({\n            pqeOwner: this.pqeOwner\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch critical issues: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success') {\n          // Filter critical issues to only include those related to this PQE's breakout groups\n          const allIssues = data.critical_issues || [];\n\n          // If we have breakout groups, filter issues to only include those for this PQE's groups\n          if (this.breakoutGroups.length > 0) {\n            this.unresolvedCriticalIssues = allIssues.filter(issue => {\n              // Check if the issue's category matches any of the PQE's breakout groups\n              return this.breakoutGroups.some(group =>\n                issue.category.includes(group.name) || group.name.includes(issue.category)\n              );\n            });\n          } else {\n            // If we don't have breakout groups yet, use all issues\n            this.unresolvedCriticalIssues = allIssues;\n          }\n\n          console.log(`Loaded ${this.unresolvedCriticalIssues.length} critical issues for ${this.pqeOwner}'s groups`);\n        } else {\n          console.error('Failed to load critical issues:', data.message);\n          // Use sample data for development\n          this.loadSampleCriticalIssues();\n        }\n      } catch (error) {\n        console.error('Error loading critical issues:', error);\n        // Use sample data for development\n        this.loadSampleCriticalIssues();\n      } finally {\n        this.isLoading = false;\n      }\n    },\n\n    async loadBreakoutGroups() {\n      console.log(`Loading breakout groups for PQE owner: ${this.pqeOwner}`);\n\n      try {\n        // Get authentication config\n        const config = this.getAuthConfig();\n\n        // Fetch breakout groups from the API\n        const response = await fetch('/api-statit2/get_pqe_breakout_groups', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({\n            pqeOwner: this.pqeOwner\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch breakout groups: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success') {\n          this.breakoutGroups = data.breakout_groups || [];\n          console.log(`Loaded ${this.breakoutGroups.length} breakout groups for ${this.pqeOwner}`);\n\n          // Reload root cause chart data now that we have the correct breakout groups\n          await this.loadRootCauseData();\n        } else {\n          console.error('Failed to load breakout groups:', data.message);\n          // Use sample data for development\n          this.loadSampleBreakoutGroups();\n          // Reload root cause chart data with sample groups\n          await this.loadRootCauseData();\n        }\n      } catch (error) {\n        console.error('Error loading breakout groups:', error);\n        // Use sample data for development\n        this.loadSampleBreakoutGroups();\n        // Reload root cause chart data with sample groups\n        await this.loadRootCauseData();\n      }\n    },\n\n    async loadSampleBreakoutGroups() {\n      // Sample data for development\n      if (this.pqeOwner === 'Albert G.') {\n        this.breakoutGroups = [\n          { name: 'Fan Themis', status: 'Short-Term Spike', xFactor: 3.2 },\n          { name: 'Victoria Crypto', status: 'Sustained Problem', xFactor: 1.8 },\n          { name: 'Quantum Nexus', status: 'Sustained Problem', xFactor: 1.6 }\n        ];\n      } else if (this.pqeOwner === 'Sarah L.') {\n        this.breakoutGroups = [\n          { name: 'Stellar Core', status: 'Short-Term Spike', xFactor: 2.5 },\n          { name: 'Nebula Drive', status: 'Sustained Problem', xFactor: 1.7 }\n        ];\n      } else {\n        // Default sample data\n        this.breakoutGroups = [\n          { name: 'Sample Group 1', status: 'Normal', xFactor: 0.9 },\n          { name: 'Sample Group 2', status: 'Normal', xFactor: 0.8 }\n        ];\n      }\n\n      // Reload root cause chart data with the sample groups\n      await this.loadRootCauseData();\n    },\n\n    loadSampleCriticalIssues() {\n      // Sample data for development\n      this.unresolvedCriticalIssues = [\n        {\n          id: 'ci1',\n          category: 'Fan Themis',\n          month: '2024-06',\n          severity: 'high',\n          increaseMultiplier: '3.2',\n          aiDescription: 'Fan Themis showing 3.2x spike in June 2024 with 12 fails vs 4 expected. Root cause appears to be airflow issues in the new design.',\n          comment: '',\n          analysisType: 'Root Cause'\n        },\n        {\n          id: 'ci2',\n          category: 'Victoria Crypto',\n          month: '2024-06',\n          severity: 'medium',\n          increaseMultiplier: '1.8',\n          aiDescription: 'Victoria Crypto has sustained problem with 1.8x target rate for 3 consecutive months. Consistent pattern of power delivery issues.',\n          comment: '',\n          analysisType: 'Root Cause'\n        },\n\n      ];\n\n      // Set new critical issues to empty array for now\n      this.newCriticalIssues = [];\n    },\n\n    async loadValidationCounts() {\n      console.log(`Loading validation counts for PQE owner: ${this.pqeOwner}`);\n\n      try {\n        // Get authentication config\n        const config = this.getAuthConfig();\n\n        // Fetch validation counts from the API\n        const response = await fetch('/api-statit2/get_validation_counts', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({\n            pqeOwner: this.pqeOwner\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch validation counts: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success') {\n          this.validatedCount = data.validated_count || 0;\n          this.unvalidatedCount = data.unvalidated_count || 0;\n          console.log(`Loaded validation counts: ${this.validatedCount} validated, ${this.unvalidatedCount} unvalidated`);\n        } else {\n          console.error('Failed to load validation counts:', data.message);\n          // Use sample data for development\n          this.validatedCount = 125;\n          this.unvalidatedCount = 37;\n        }\n      } catch (error) {\n        console.error('Error loading validation counts:', error);\n        // Use sample data for development\n        this.validatedCount = 125;\n        this.unvalidatedCount = 37;\n      }\n    },\n\n    // Load Action Tracker Alerts data\n    loadActionTrackerAlerts() {\n      console.log(`Loading Action Tracker Alerts for PQE owner: ${this.pqeOwner}`);\n\n      // Generate sample data for demonstration\n      // In a real implementation, this would fetch from the action tracker API\n      this.inProgressAlerts = [\n        {\n          id: 'ip-1',\n          category: 'Thermal Management',\n          severity: 'High',\n          xFactor: '2.1',\n          status: 'In-Progress'\n        },\n        {\n          id: 'ip-2',\n          category: 'Power Delivery',\n          severity: 'Medium',\n          xFactor: '1.8',\n          status: 'In-Progress'\n        }\n      ];\n\n      this.outstandingAlerts = [\n        {\n          id: 'out-1',\n          category: 'Signal Integrity',\n          severity: 'Medium',\n          xFactor: '1.6',\n          status: 'Outstanding'\n        }\n      ];\n\n      this.resolvedAlerts = [\n        {\n          id: 'res-1',\n          category: 'Manufacturing Defect',\n          severity: 'High',\n          xFactor: '0.8',\n          status: 'Resolved'\n        },\n        {\n          id: 'res-2',\n          category: 'Component Quality',\n          severity: 'Medium',\n          xFactor: '0.9',\n          status: 'Resolved'\n        }\n      ];\n\n      console.log(`Loaded ${this.totalActionTrackerAlerts} Action Tracker Alerts`);\n    },\n\n    toggleCriticalIssuesExpanded() {\n      this.isCriticalIssuesExpanded = !this.isCriticalIssuesExpanded;\n    },\n\n    toggleResolvedIssuesExpanded() {\n      this.isResolvedIssuesExpanded = !this.isResolvedIssuesExpanded;\n    },\n\n    toggleOutstandingIssuesExpanded() {\n      this.isOutstandingIssuesExpanded = !this.isOutstandingIssuesExpanded;\n    },\n\n    toggleIssueExpanded(issue) {\n      const issueId = issue.id;\n      const index = this.expandedIssueIds.indexOf(issueId);\n\n      if (index === -1) {\n        // Issue is not expanded, so expand it\n        this.expandedIssueIds.push(issueId);\n      } else {\n        // Issue is expanded, so collapse it\n        this.expandedIssueIds.splice(index, 1);\n      }\n    },\n\n    toggleResolvedIssueExpanded(issue) {\n      const issueId = issue.id;\n      const index = this.expandedResolvedIssueIds.indexOf(issueId);\n\n      if (index === -1) {\n        // Issue is not expanded, so expand it\n        this.expandedResolvedIssueIds.push(issueId);\n      } else {\n        // Issue is expanded, so collapse it\n        this.expandedResolvedIssueIds.splice(index, 1);\n      }\n    },\n\n    toggleOutstandingIssueExpanded(issue) {\n      const issueId = issue.id;\n      const index = this.expandedOutstandingIssueIds.indexOf(issueId);\n\n      if (index === -1) {\n        // Issue is not expanded, so expand it\n        this.expandedOutstandingIssueIds.push(issueId);\n      } else {\n        // Issue is expanded, so collapse it\n        this.expandedOutstandingIssueIds.splice(index, 1);\n      }\n    },\n\n    isIssueExpanded(issueId) {\n      return this.expandedIssueIds.includes(issueId);\n    },\n\n    isResolvedIssueExpanded(issueId) {\n      return this.expandedResolvedIssueIds.includes(issueId);\n    },\n\n    isOutstandingIssueExpanded(issueId) {\n      return this.expandedOutstandingIssueIds.includes(issueId);\n    },\n\n    markIssueAsResolved(issue) {\n      console.log('Mark issue as resolved:', issue);\n\n      // In a real implementation, this would call an API to update the issue status\n      // For now, we'll just move the issue from unresolved to resolved\n\n      // Create a resolved issue object with additional fields\n      const resolvedIssue = {\n        ...issue,\n        resolutionDate: new Date().toISOString().split('T')[0], // Today's date in YYYY-MM-DD format\n        currentPerformance: '1.0x' // Initial performance after resolution\n      };\n\n      // Add to resolved issues\n      this.resolvedIssues.push(resolvedIssue);\n\n      // Remove from unresolved issues\n      const index = this.unresolvedCriticalIssues.findIndex(i => i.id === issue.id);\n      if (index !== -1) {\n        this.unresolvedCriticalIssues.splice(index, 1);\n      }\n\n      // Show success message\n      alert(`Issue marked as resolved: ${issue.category}`);\n    },\n\n    markIssueAsOutstanding(issue) {\n      console.log('Mark issue as outstanding:', issue);\n\n      // In a real implementation, this would call an API to update the issue status\n      // For now, we'll just move the issue from unresolved to outstanding\n\n      // Create an outstanding issue object with additional fields\n      const outstandingIssue = {\n        ...issue,\n        acceptanceDate: new Date().toISOString().split('T')[0], // Today's date in YYYY-MM-DD format\n        currentPerformance: issue.increaseMultiplier, // Initial performance is the same as the issue multiplier\n        acceptedBy: 'Engineering Team' // Default value\n      };\n\n      // Add to outstanding issues\n      this.outstandingIssues.push(outstandingIssue);\n\n      // Remove from unresolved issues\n      const index = this.unresolvedCriticalIssues.findIndex(i => i.id === issue.id);\n      if (index !== -1) {\n        this.unresolvedCriticalIssues.splice(index, 1);\n      }\n\n      // Show success message\n      alert(`Issue marked as outstanding: ${issue.category}`);\n    },\n\n    handleSeverityFilterChange() {\n      // Update the selected filters based on the severity dropdown\n      if (this.severityFilter === 'all') {\n        this.selectedFilters.severity = [];\n      } else {\n        this.selectedFilters.severity = [this.severityFilter];\n      }\n    },\n\n    handleAnalysisFilterChange() {\n      // Update the selected filters based on the analysis type dropdown\n      if (this.analysisTypeFilter === 'all') {\n        this.selectedFilters.analysisType = [];\n      } else {\n        this.selectedFilters.analysisType = [this.analysisTypeFilter];\n      }\n    },\n\n    clearFilters() {\n      this.selectedFilters.severity = [];\n      this.selectedFilters.analysisType = [];\n      this.severityFilter = 'all';\n      this.analysisTypeFilter = 'all';\n    },\n\n    handleResolvedCategoryFilterChange() {\n      // Update the selected filters based on the category dropdown\n      if (this.resolvedCategoryFilter === 'all') {\n        this.resolvedFilters.category = [];\n      } else {\n        this.resolvedFilters.category = [this.resolvedCategoryFilter];\n      }\n    },\n\n    handleResolvedAnalysisFilterChange() {\n      // Update the selected filters based on the analysis type dropdown\n      if (this.resolvedAnalysisTypeFilter === 'all') {\n        this.resolvedFilters.analysisType = [];\n      } else {\n        this.resolvedFilters.analysisType = [this.resolvedAnalysisTypeFilter];\n      }\n    },\n\n    clearResolvedFilters() {\n      this.resolvedFilters.category = [];\n      this.resolvedFilters.analysisType = [];\n      this.resolvedCategoryFilter = 'all';\n      this.resolvedAnalysisTypeFilter = 'all';\n    },\n\n    handleOutstandingCategoryFilterChange() {\n      // Update the selected filters based on the category dropdown\n      if (this.outstandingCategoryFilter === 'all') {\n        this.outstandingFilters.category = [];\n      } else {\n        this.outstandingFilters.category = [this.outstandingCategoryFilter];\n      }\n    },\n\n    handleOutstandingAnalysisFilterChange() {\n      // Update the selected filters based on the analysis type dropdown\n      if (this.outstandingAnalysisTypeFilter === 'all') {\n        this.outstandingFilters.analysisType = [];\n      } else {\n        this.outstandingFilters.analysisType = [this.outstandingAnalysisTypeFilter];\n      }\n    },\n\n    clearOutstandingFilters() {\n      this.outstandingFilters.category = [];\n      this.outstandingFilters.analysisType = [];\n      this.outstandingCategoryFilter = 'all';\n      this.outstandingAnalysisTypeFilter = 'all';\n    },\n\n    updateIssue(issue, markAsResolved = false, markAsOutstanding = false) {\n      console.log('Update issue:', issue, 'Mark as resolved:', markAsResolved, 'Mark as outstanding:', markAsOutstanding);\n\n      // Emit event to update action tracker\n      this.$emit('update-action-tracker', {\n        issueId: issue.id,\n        category: issue.category,\n        comment: issue.comment,\n        severity: issue.severity,\n        pqeOwner: this.pqeOwner,\n        month: issue.month,\n        analysisType: issue.analysisType,\n        resolved: markAsResolved,\n        outstanding: markAsOutstanding\n      });\n\n      // If marking as resolved, move the issue to resolved issues\n      if (markAsResolved) {\n        this.markIssueAsResolved(issue);\n      }\n      // If marking as outstanding, move the issue to outstanding issues\n      else if (markAsOutstanding) {\n        this.markIssueAsOutstanding(issue);\n      }\n      else {\n        // Show success message for regular update\n        alert(`Action tracker updated for issue: ${issue.category}`);\n      }\n    },\n\n    viewPerformanceData(issue) {\n      console.log('View performance data for:', issue.category);\n\n      // In a real implementation, this would show a modal or chart with performance data\n      // For now, we'll just show an alert with the data\n\n      const performanceData = this.performanceData[issue.category];\n      if (performanceData && performanceData.length > 0) {\n        const performanceText = performanceData\n          .map(data => `${data.month}: ${data.xFactor}x`)\n          .join('\\n');\n\n        alert(`Performance data for ${issue.category}:\\n${performanceText}`);\n      } else {\n        alert(`No performance data available for ${issue.category}`);\n      }\n    },\n\n    viewIssueDetails(issue) {\n      console.log('View issue details for:', issue.category);\n      // In a real implementation, this would show a modal or navigate to a detailed view\n      alert(`Viewing details for issue: ${issue.category}\\nMonth: ${issue.month}\\nSeverity: ${issue.severity}\\nMultiplier: ${issue.increaseMultiplier}x`);\n    },\n\n    // Root Cause Chart methods\n    async loadRootCauseData() {\n      console.log('Loading root cause chart data for PQE Owner Dashboard');\n      this.isRootCauseDataLoading = true;\n\n      try {\n        // Get authentication config\n        const config = this.getAuthConfig();\n\n        // Calculate date range based on selected time range\n        const endDate = new Date();\n        const startDate = new Date();\n        const monthsToFetch = this.rootCauseTimeRange === '3month' ? 2 : 5; // 3 or 6 months including current\n        startDate.setMonth(endDate.getMonth() - monthsToFetch);\n\n        const startDateStr = `${startDate.getFullYear()}-${String(startDate.getMonth() + 1).padStart(2, '0')}`;\n        const endDateStr = `${endDate.getFullYear()}-${String(endDate.getMonth() + 1).padStart(2, '0')}`;\n\n        console.log(`Fetching root cause data for ${this.pqeOwner} from ${startDateStr} to ${endDateStr}, Group: ${this.rootCauseSelectedGroup}`);\n\n        // Call the new API endpoint\n        const response = await fetch('/api-statit2/get_pqe_root_cause_analysis', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({\n            pqeOwner: this.pqeOwner,\n            startDate: startDateStr,\n            endDate: endDateStr,\n            selectedGroup: this.rootCauseSelectedGroup === 'all' ? null : this.rootCauseSelectedGroup\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch root cause data: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success') {\n          // Transform the API data to chart format\n          this.rootCauseChartData = this.transformRootCauseData(data.categoryData);\n          console.log('Root cause chart data loaded:', data.categoryData);\n          console.log('Chart data length:', this.rootCauseChartData.length);\n          console.log('Sample data point:', this.rootCauseChartData[0]);\n          console.log('Breakout groups:', data.breakoutGroups);\n          console.log('Part numbers:', data.partNumbers);\n\n          console.log('Chart data loaded, watcher should handle update');\n        } else {\n          console.error('Failed to load root cause data:', data.sql_error_msg || data.message);\n          // Fall back to mock data\n          this.rootCauseChartData = this.generateMockRootCauseData();\n          console.log('Using mock data:', this.rootCauseChartData.length);\n        }\n      } catch (error) {\n        console.error('Error loading root cause data:', error);\n        // Fall back to mock data\n        this.rootCauseChartData = this.generateMockRootCauseData();\n      } finally {\n        this.isRootCauseDataLoading = false;\n      }\n    },\n\n    transformRootCauseData(categoryData) {\n      // Transform the API response into chart format\n      const chartData = [];\n\n      // categoryData structure: { \"category\": { \"2024-03\": { defects: 5, volume: 1000, failRate: 0.5 }, ... }, ... }\n      for (const [category, monthData] of Object.entries(categoryData)) {\n        // Clean up category name (trim whitespace)\n        const cleanCategory = category.trim();\n        console.log(\"trim cat\", cleanCategory)\n\n        for (const [month, data] of Object.entries(monthData)) {\n          chartData.push({\n            group: cleanCategory,\n            key: month,\n            value: parseFloat(data.failRate.toFixed(2))\n          });\n        }\n      }\n\n      console.log('Transformed root cause data:', chartData);\n      console.log('Unique groups in data:', [...new Set(chartData.map(d => d.group))]);\n      console.log('Unique keys in data:', [...new Set(chartData.map(d => d.key))]);\n      console.log('Value range:', Math.min(...chartData.map(d => d.value)), 'to', Math.max(...chartData.map(d => d.value)));\n      return chartData;\n    },\n\n    generateMockRootCauseData() {\n      // Use the actual breakout groups for this PQE owner instead of generic categories\n      let categories = [];\n\n      if (this.breakoutGroups && this.breakoutGroups.length > 0) {\n        // Use the actual breakout groups for this PQE owner\n        categories = this.breakoutGroups.map(group => group.name);\n\n        // If a specific group is selected, filter to only that group\n        if (this.rootCauseSelectedGroup !== 'all') {\n          categories = categories.filter(category => category === this.rootCauseSelectedGroup);\n        }\n      } else {\n        // Fallback to sample data if breakout groups aren't loaded yet\n        if (this.pqeOwner === 'Albert G.') {\n          categories = ['Fan Themis', 'Victoria Crypto', 'Quantum Nexus'];\n        } else if (this.pqeOwner === 'Sarah L.') {\n          categories = ['Stellar Core', 'Nebula Drive'];\n        } else {\n          categories = ['Sample Group 1', 'Sample Group 2'];\n        }\n\n        // If a specific group is selected, filter to only that group\n        if (this.rootCauseSelectedGroup !== 'all') {\n          categories = categories.filter(category => category === this.rootCauseSelectedGroup);\n        }\n      }\n\n      // Generate months based on selected time range\n      const now = new Date();\n      const months = [];\n      const monthsToGenerate = this.rootCauseTimeRange === '3month' ? 3 : 6;\n\n      for (let i = monthsToGenerate - 1; i >= 0; i--) {\n        const date = new Date(now.getFullYear(), now.getMonth() - i, 1);\n        const year = date.getFullYear();\n        const month = String(date.getMonth() + 1).padStart(2, '0');\n        months.push(`${year}-${month}`);\n      }\n\n      const data = [];\n\n      months.forEach(month => {\n        categories.forEach(category => {\n          // Generate realistic fail rate data (0-5% range)\n          const baseRate = Math.random() * 3 + 0.5; // 0.5% to 3.5%\n          const variation = (Math.random() - 0.5) * 1; // ±0.5%\n          const failRate = Math.max(0.1, baseRate + variation);\n\n          data.push({\n            group: category,\n            key: month,\n            value: parseFloat(failRate.toFixed(2))\n          });\n        });\n      });\n\n      return data;\n    },\n\n    handleRootCauseBarClick(event) {\n      if (event && event.data) {\n        const clickedData = event.data;\n        console.log(`Root cause bar clicked for category: ${clickedData.group}, month: ${clickedData.key}`);\n\n        // You can implement additional functionality here, such as showing more details\n        // or navigating to the MetisXFactors Group tab with this specific category\n        alert(`Clicked on ${clickedData.group} for ${clickedData.key}\\nFail Rate: ${clickedData.value}%`);\n      }\n    },\n\n    handleRootCauseTimeRangeChange() {\n      console.log(`Root cause time range changed to: ${this.rootCauseTimeRange}`);\n      this.loadRootCauseData();\n    },\n\n    handleRootCauseViewByChange() {\n      console.log(`Root cause time range changed to: ${this.rootCauseTimeRange}`);\n      this.loadRootCauseData();\n    },\n\n    handleRootCauseGroupChange() {\n      console.log(`Root cause group changed to: ${this.rootCauseSelectedGroup}`);\n      console.log('Current chart data length before reload:', this.rootCauseChartData.length);\n      this.loadRootCauseData();\n    },\n\n    getAuthConfig() {\n      // Get authentication token from localStorage or Vuex store\n      const token = localStorage.getItem('token') || this.$store.state.token;\n\n      return {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      };\n    },\n\n    // Navigate to Action Tracker\n    goToActionTracker() {\n      console.log('Navigating to Action Tracker...');\n      this.$router.push('/action-tracker');\n    },\n\n    // Navigate to Validation Page\n    goToValidationPage() {\n      console.log('Navigating to Validation Page...');\n      this.$router.push('/defect-validations');\n    },\n\n    // Navigate to Heatmap tab in Metis XFactors\n    goToHeatmap() {\n      console.log('Navigating to Metis XFactors Heatmap...');\n      this.$router.push('/metis-xfactors?tab=heatmap');\n    },\n\n    // Scroll to alerts section (action tracker alerts section)\n    scrollToAlerts() {\n      console.log('Scrolling to action tracker alerts section...');\n      // Scroll to the Action Tracker Alerts section\n      this.$nextTick(() => {\n        const actionTrackerSection = document.querySelector('.section-card:first-child'); // First section card (Action Tracker Alerts)\n        if (actionTrackerSection) {\n          actionTrackerSection.scrollIntoView({\n            behavior: 'smooth',\n            block: 'start'\n          });\n\n          // Add a brief highlight effect\n          actionTrackerSection.style.transition = 'box-shadow 0.3s ease';\n          actionTrackerSection.style.boxShadow = '0 0 20px rgba(0, 98, 255, 0.5)';\n\n          setTimeout(() => {\n            actionTrackerSection.style.boxShadow = '0 2px 6px rgba(0, 0, 0, 0.2)';\n          }, 2000);\n        }\n      });\n    },\n\n    // Toggle Action Tracker Alerts section\n    toggleActionTrackerAlertsExpanded() {\n      this.isActionTrackerAlertsExpanded = !this.isActionTrackerAlertsExpanded;\n      console.log('Action Tracker Alerts expanded:', this.isActionTrackerAlertsExpanded);\n    },\n\n    // Toggle In-Progress Alerts subsection\n    toggleInProgressAlertsExpanded() {\n      this.isInProgressAlertsExpanded = !this.isInProgressAlertsExpanded;\n      console.log('In-Progress Alerts expanded:', this.isInProgressAlertsExpanded);\n    },\n\n    // Toggle Outstanding Alerts subsection\n    toggleOutstandingAlertsExpanded() {\n      this.isOutstandingAlertsExpanded = !this.isOutstandingAlertsExpanded;\n      console.log('Outstanding Alerts expanded:', this.isOutstandingAlertsExpanded);\n    },\n\n    // Toggle Resolved Alerts subsection\n    toggleResolvedAlertsExpanded() {\n      this.isResolvedAlertsExpanded = !this.isResolvedAlertsExpanded;\n      console.log('Resolved Alerts expanded:', this.isResolvedAlertsExpanded);\n    },\n\n    // View Action Tracker Alert - navigates to Action Tracker page\n    viewActionTrackerAlert(alert) {\n      console.log('Navigating to action tracker for alert:', alert);\n\n      // Navigate to the Action Tracker page with the specific alert ID\n      // This will be handled by the parent component or router\n      this.$emit('navigate-to-action-tracker', {\n        alertId: alert.id,\n        actionTrackerId: alert.actionTrackerId || alert.id,\n        tab: 'alerts' // Specify which tab to open\n      });\n    },\n\n    // Add critical issue to action tracker\n    async addCriticalIssueToActionTracker(issue) {\n      console.log('Adding critical issue to action tracker:', issue);\n\n      try {\n        const response = await fetch('/api-statit2/update_pqe_action', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...this.getAuthConfig().headers\n          },\n          body: JSON.stringify({\n            issueId: issue.id,\n            category: issue.category,\n            comment: issue.comment || `Critical issue: ${issue.aiDescription}`,\n            severity: issue.severity,\n            pqeOwner: this.pqeOwner,\n            month: issue.month,\n            analysisType: issue.analysisType\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to add to action tracker: ${response.status}`);\n        }\n\n        const data = await response.json();\n        if (data.status_res === 'success') {\n          alert(`Critical issue \"${issue.category}\" has been added to the action tracker with ID: ${data.actionId}`);\n        } else {\n          throw new Error(data.message || 'Failed to add to action tracker');\n        }\n      } catch (error) {\n        console.error('Error adding critical issue to action tracker:', error);\n        alert('Failed to add critical issue to action tracker: ' + error.message);\n      }\n    }\n  }\n};\n</script>\n\n<style scoped>\n.pqe-owner-dashboard-container {\n  color: #f4f4f4;\n}\n\n.content-wrapper {\n  padding: 1rem;\n}\n\n.dashboard-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1.5rem;\n}\n\n.dashboard-header h3 {\n  margin: 0;\n  font-size: 1.5rem;\n  font-weight: 400;\n}\n\n.last-updated-text {\n  font-size: 0.875rem;\n  color: #8d8d8d;\n}\n\n.key-metrics-section {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1rem;\n  margin-bottom: 1.5rem;\n}\n\n.metric-card {\n  background-color: #262626;\n  border-radius: 8px;\n  padding: 1.25rem;\n  display: flex;\n  align-items: center;\n  border: 1px solid #333333;\n  transition: transform 0.2s ease, box-shadow 0.2s ease;\n}\n\n.metric-card.clickable {\n  cursor: pointer;\n\n  &:hover {\n    transform: translateY(-3px);\n    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);\n    background-color: #353535; /* Darker gray on hover */\n  }\n\n  &:active {\n    transform: translateY(-1px);\n    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);\n  }\n}\n\n.metric-icon {\n  margin-right: 1rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 48px;\n  height: 48px;\n  border-radius: 50%;\n}\n\n.metric-icon.alerts {\n  background-color: rgba(250, 77, 86, 0.1);\n  color: #fa4d56;\n}\n\n.metric-icon.in-progress {\n  background-color: rgba(255, 131, 43, 0.1);\n  color: #ff832b;\n}\n\n.metric-icon.validated {\n  background-color: rgba(36, 161, 72, 0.1);\n  color: #24a148;\n}\n\n.metric-icon.new-issues {\n  background-color: rgba(15, 98, 254, 0.1);\n  color: #0f62fe;\n}\n\n.metric-icon.critical-issues {\n  background-color: rgba(250, 77, 86, 0.1);\n  color: #fa4d56;\n}\n\n.metric-icon.unvalidated {\n  background-color: rgba(255, 131, 43, 0.1);\n  color: #ff832b;\n}\n\n.metric-icon.groups-over-target {\n  background-color: rgba(138, 63, 252, 0.1);\n  color: #8a3ffc;\n}\n\n/* Action Tracker Alerts Styles */\n.alert-subsection {\n  margin-bottom: 1.5rem;\n  border: 1px solid #393939;\n  border-radius: 8px;\n  background-color: #2a2a2a;\n}\n\n.subsection-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 1rem 1.25rem;\n  cursor: pointer;\n  border-bottom: 1px solid #393939;\n  transition: background-color 0.2s ease;\n}\n\n.subsection-header:hover {\n  background-color: #333333;\n}\n\n.subsection-title {\n  color: #f4f4f4;\n  font-size: 1rem;\n  font-weight: 500;\n  margin: 0;\n}\n\n.subsection-controls {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n}\n\n.subsection-content {\n  padding: 1rem 1.25rem;\n}\n\n.action-tracker-alert {\n  background-color: #333333;\n  border: 1px solid #525252;\n  border-radius: 6px;\n  margin-bottom: 0.75rem;\n  transition: all 0.2s ease;\n}\n\n.action-tracker-alert:hover {\n  background-color: #393939;\n  border-color: #6f6f6f;\n}\n\n.action-tracker-alert .issue-header {\n  padding: 1rem;\n}\n\n.action-tracker-alert .issue-tags {\n  display: flex;\n  gap: 0.5rem;\n  margin-bottom: 0.5rem;\n}\n\n.action-tracker-alert .issue-title {\n  color: #f4f4f4;\n  font-size: 1rem;\n  font-weight: 500;\n  margin-bottom: 0.5rem;\n  display: block;\n}\n\n.action-tracker-alert .issue-metadata {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n}\n\n.action-tracker-alert .issue-multiplier {\n  font-size: 0.875rem;\n  font-weight: 600;\n  padding: 0.25rem 0.5rem;\n  border-radius: 4px;\n}\n\n.action-tracker-alert .issue-multiplier.medium-severity {\n  background-color: rgba(255, 131, 43, 0.2);\n  color: #ff832b;\n}\n\n.action-tracker-alert .issue-multiplier.good-performance {\n  background-color: rgba(36, 161, 72, 0.2);\n  color: #24a148;\n}\n\n.no-data-message {\n  text-align: center;\n  color: #8d8d8d;\n  font-style: italic;\n  padding: 2rem;\n}\n\n.metric-content {\n  display: flex;\n  flex-direction: column;\n}\n\n.metric-label {\n  font-size: 0.875rem;\n  color: #8d8d8d;\n  margin-bottom: 0.25rem;\n}\n\n.metric-value {\n  font-size: 1.5rem;\n  font-weight: 600;\n  margin-bottom: 0.25rem;\n}\n\n.metric-description {\n  font-size: 0.75rem;\n  color: #8d8d8d;\n}\n\n.chart-section {\n  margin-bottom: 1.5rem;\n}\n\n.chart-controls {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 1rem;\n  padding: 1rem;\n  background-color: #262626;\n  border-bottom: 1px solid #333333;\n}\n\n.control-group {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.control-label {\n  color: #8d8d8d;\n  font-size: 0.875rem;\n  white-space: nowrap;\n}\n\n.control-dropdown {\n  width: 200px;\n}\n\n.chart-container {\n  height: 500px;\n  padding: 1rem;\n  background-color: #161616;\n  border-radius: 8px;\n  margin: 1rem;\n}\n\n.section-footer {\n  padding: 0 1.25rem 1.25rem;\n}\n\n.section-description {\n  margin: 0;\n  font-size: 0.875rem;\n  color: #8d8d8d;\n  line-height: 1.4;\n}\n\n.dashboard-main-content {\n  display: grid;\n  grid-template-columns: 1fr;\n  gap: 1.5rem;\n}\n\n.dashboard-column {\n  display: flex;\n  flex-direction: column;\n  gap: 1.5rem;\n}\n\n.section-card {\n  background-color: #262626;\n  border-radius: 8px;\n  overflow: hidden;\n  border: 1px solid #333333;\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 1.25rem;\n  cursor: pointer;\n  transition: background-color 0.2s ease;\n}\n\n.section-header:hover {\n  background-color: #333333;\n}\n\n.section-title-container {\n  display: flex;\n  flex-direction: column;\n}\n\n.section-title {\n  margin: 0;\n  font-size: 1.25rem;\n  font-weight: 400;\n}\n\n.section-subtitle {\n  font-size: 0.875rem;\n  color: #8d8d8d;\n  margin-top: 0.25rem;\n}\n\n.section-controls {\n  display: flex;\n  align-items: center;\n}\n\n.status-indicator {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 24px;\n  height: 24px;\n  border-radius: 50%;\n  background-color: #fa4d56;\n  color: #ffffff;\n  font-size: 0.75rem;\n  font-weight: 600;\n  margin-right: 0.75rem;\n}\n\n.status-indicator.flashing {\n  animation: flash 2s infinite;\n}\n\n.status-indicator.resolved-indicator {\n  background-color: #24a148;\n}\n\n.status-indicator.outstanding-indicator {\n  background-color: #0f62fe;\n}\n\n@keyframes flash {\n  0%, 100% { opacity: 1; }\n  50% { opacity: 0.5; }\n}\n\n.expand-indicator {\n  transition: transform 0.2s ease;\n}\n\n.expand-indicator.expanded {\n  transform: rotate(180deg);\n}\n\n.section-content {\n  padding: 0 1.25rem 1.25rem;\n}\n\n.filter-container {\n  margin-bottom: 1.5rem;\n  background-color: #333333;\n  padding: 1rem;\n  border-radius: 8px;\n}\n\n.filter-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1rem;\n}\n\n.filter-title {\n  margin: 0;\n  font-size: 1rem;\n  font-weight: 400;\n}\n\n.filter-controls {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 1rem;\n}\n\n.filter-group {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.filter-label {\n  color: #8d8d8d;\n  font-size: 0.875rem;\n  white-space: nowrap;\n}\n\n.filter-dropdown {\n  width: 200px;\n}\n\n.issues-list {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n\n.issue-card {\n  background-color: #333333;\n  border-radius: 8px;\n  overflow: hidden;\n  transition: transform 0.2s ease, box-shadow 0.2s ease;\n  cursor: pointer;\n}\n\n.issue-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);\n}\n\n.issue-header {\n  display: flex;\n  align-items: center;\n  padding: 1rem;\n  position: relative;\n}\n\n.issue-tags {\n  display: flex;\n  gap: 0.5rem;\n  margin-right: 1rem;\n}\n\n.issue-title {\n  flex-grow: 1;\n  font-weight: 500;\n}\n\n.issue-metadata {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  margin-right: 1rem;\n}\n\n.issue-multiplier {\n  font-weight: 600;\n  padding: 0.125rem 0.375rem;\n  border-radius: 4px;\n}\n\n.issue-multiplier.high-severity {\n  background-color: rgba(250, 77, 86, 0.1);\n  color: #fa4d56;\n}\n\n.issue-multiplier.medium-severity {\n  background-color: rgba(255, 131, 43, 0.1);\n  color: #ff832b;\n}\n\n.issue-multiplier.good-performance {\n  background-color: rgba(36, 161, 72, 0.1);\n  color: #24a148;\n}\n\n.issue-multiplier.medium-performance {\n  background-color: rgba(255, 131, 43, 0.1);\n  color: #ff832b;\n}\n\n.resolved-issue-card {\n  border-left: 4px solid #24a148;\n}\n\n.outstanding-issue-card {\n  border-left: 4px solid #0f62fe;\n}\n\n.resolution-details, .acceptance-details {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 1rem;\n  font-size: 0.875rem;\n  color: #c6c6c6;\n}\n\n.issue-multiplier.low-performance {\n  background-color: rgba(36, 161, 72, 0.1);\n  color: #24a148;\n}\n\n.resolution-comment, .acceptance-comment {\n  margin: 1rem 0;\n  padding: 0.75rem;\n  background-color: #333333;\n  border-radius: 4px;\n}\n\n.comment-label {\n  font-weight: 600;\n  margin-bottom: 0.5rem;\n  color: #c6c6c6;\n}\n\n.comment-text {\n  font-size: 0.875rem;\n  white-space: pre-wrap;\n}\n\n.issue-content {\n  padding: 0 1rem 1rem;\n  border-top: 1px solid #444444;\n}\n\n.ai-description {\n  margin-bottom: 1rem;\n  padding: 1rem;\n  background-color: #262626;\n  border-radius: 8px;\n  font-size: 0.875rem;\n  line-height: 1.5;\n}\n\n.action-comment {\n  margin-bottom: 1rem;\n}\n\n.issue-actions {\n  display: flex;\n  justify-content: flex-end;\n  gap: 0.5rem;\n}\n\n.no-data-message {\n  color: #8d8d8d;\n  font-size: 1rem;\n  text-align: center;\n  padding: 2rem;\n}\n\n@media (max-width: 768px) {\n  .key-metrics-section {\n    grid-template-columns: 1fr;\n  }\n\n  .dashboard-main-content {\n    grid-template-columns: 1fr;\n  }\n\n  .filter-controls {\n    flex-direction: column;\n  }\n\n  .filter-group {\n    width: 100%;\n  }\n}\n\n/* Issue actions styling */\n.issue-actions {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 0.5rem;\n  margin-top: 1rem;\n  padding-top: 1rem;\n  border-top: 1px solid #444444;\n}\n\n.issue-actions .cv-button {\n  flex: 0 0 auto;\n}\n\n@media (max-width: 768px) {\n  .issue-actions {\n    flex-direction: column;\n  }\n\n  .issue-actions .cv-button {\n    width: 100%;\n  }\n}\n</style>\n"]}]}