{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--5!C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\DefectValidations\\DefectValidations.vue?vue&type=template&id=0b639959&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\DefectValidations\\DefectValidations.vue", "mtime": 1748973026822}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}