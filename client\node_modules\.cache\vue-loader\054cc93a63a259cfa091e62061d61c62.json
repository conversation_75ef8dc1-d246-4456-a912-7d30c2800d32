{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\DefectValidations\\DefectValidations.vue?vue&type=style&index=0&id=0b639959&scoped=true&lang=scss", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\DefectValidations\\DefectValidations.vue", "mtime": 1748973026822}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\@vue\\cli-service\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["DefectValidations.vue"], "names": [], "mappings": ";AAmqCA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "DefectValidations.vue", "sourceRoot": "src/views/DefectValidations", "sourcesContent": ["<template>\r\n  <div class=\"validations-container\">\r\n    <!-- Inherit the MainHeader component -->\r\n    <MainHeader :expandedSideNav=\"expandedSideNav\" :useFixed=\"useFixed\" />\r\n\r\n    <!-- Page Header -->\r\n    <div class=\"page-header\">\r\n      <h1 class=\"page-title\">Defect Validations</h1>\r\n      <div class=\"header-actions\">\r\n        <cv-button kind=\"primary\" @click=\"refreshData\">Refresh Data</cv-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Filter Bar -->\r\n    <div class=\"filter-bar\">\r\n      <div class=\"filter-group\">\r\n        <div class=\"filter-label\">PQE Owner:</div>\r\n        <cv-dropdown\r\n          v-model=\"selectedPQEOwner\"\r\n          label=\"Filter by PQE Owner\"\r\n          :items=\"pqeOwnerOptions\"\r\n          @change=\"handlePQEOwnerChange\"\r\n        />\r\n      </div>\r\n\r\n      <div class=\"filter-group\">\r\n        <div class=\"filter-label\">Process/Commodity:</div>\r\n        <cv-dropdown\r\n          v-model=\"selectedProcess\"\r\n          label=\"Filter by process\"\r\n          :items=\"processOptions\"\r\n          @change=\"loadValidationData\"\r\n        ></cv-dropdown>\r\n      </div>\r\n\r\n      <div class=\"filter-group\">\r\n        <div class=\"filter-label\">Time Period:</div>\r\n        <cv-dropdown\r\n          v-model=\"selectedTimeRange\"\r\n          label=\"Filter by time period\"\r\n          :items=\"rangeOptions\"\r\n          @change=\"loadValidationData\"\r\n        ></cv-dropdown>\r\n      </div>\r\n\r\n      <div class=\"search-box\">\r\n        <cv-search\r\n          v-model=\"searchQuery\"\r\n          label=\"Search\"\r\n          placeholder=\"Search by part number or group...\"\r\n          @input=\"filterValidations\"\r\n        ></cv-search>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Loading State -->\r\n    <div v-if=\"isLoading\" class=\"loading-container\">\r\n      <cv-loading :active=\"true\" :small=\"false\" :withOverlay=\"false\" />\r\n      <p class=\"loading-text\">Loading validation data...</p>\r\n    </div>\r\n\r\n    <!-- Error State -->\r\n    <div v-else-if=\"loadingError\" class=\"error-container\">\r\n      <cv-inline-notification\r\n        kind=\"error\"\r\n        :title=\"'Error'\"\r\n        :sub-title=\"loadingError\"\r\n      />\r\n    </div>\r\n\r\n    <!-- Content when data is loaded -->\r\n    <div v-else class=\"validations-content\">\r\n      <!-- Summary Tiles -->\r\n      <div class=\"validations-summary\">\r\n\r\n\r\n        <cv-tile class=\"summary-tile\">\r\n          <h4 class=\"tile-title\">Validated</h4>\r\n          <p class=\"tile-value validated\">{{ validationsSummary.validated }}</p>\r\n          <p class=\"tile-percentage\">{{ validationsSummary.validatedPercentage }}%</p>\r\n        </cv-tile>\r\n\r\n        <cv-tile class=\"summary-tile\">\r\n          <h4 class=\"tile-title\">Unvalidated</h4>\r\n          <p class=\"tile-value unvalidated\">{{ validationsSummary.unvalidated }}</p>\r\n          <p class=\"tile-percentage\">{{ validationsSummary.unvalidatedPercentage }}%</p>\r\n        </cv-tile>\r\n      </div>\r\n\r\n      <!-- Main Content -->\r\n      <div class=\"main-validation-content\">\r\n        <!-- PQE Validation Results -->\r\n        <div class=\"validation-section\" v-if=\"selectedPQEOwner && selectedPQEOwner !== 'All'\">\r\n          <h3>Validation Results - {{ selectedPQEOwner }}</h3>\r\n\r\n          <!-- Validation Counts -->\r\n          <div class=\"validation-counts\">\r\n            <div class=\"count-item validated\">\r\n              <div class=\"count-number\">{{ pqeValidationData.validated }}</div>\r\n              <div class=\"count-label\">Validated Defects</div>\r\n            </div>\r\n            <div class=\"count-item unvalidated\">\r\n              <div class=\"count-number\">{{ pqeValidationData.unvalidated }}</div>\r\n              <div class=\"count-label\">Unvalidated Defects</div>\r\n            </div>\r\n            <div class=\"count-item total\">\r\n              <div class=\"count-number\">{{ pqeValidationData.total }}</div>\r\n              <div class=\"count-label\">Total Defects</div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Part Numbers/Groupings -->\r\n          <div class=\"part-numbers-section\" v-if=\"pqePartNumbers.length > 0\">\r\n            <h4>Associated Part Numbers ({{ pqePartNumbers.length }})</h4>\r\n            <div class=\"part-numbers-list\">\r\n              <cv-tag\r\n                v-for=\"pn in pqePartNumbers\"\r\n                :key=\"pn\"\r\n                kind=\"gray\"\r\n                class=\"part-number-tag\"\r\n              >\r\n                {{ pn }}\r\n              </cv-tag>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Breakout Groups -->\r\n          <div class=\"breakout-groups-section\" v-if=\"pqeBreakoutGroups.length > 0\">\r\n            <h4>Associated Breakout Groups ({{ pqeBreakoutGroups.length }})</h4>\r\n            <div class=\"breakout-groups-list\">\r\n              <cv-tag\r\n                v-for=\"group in pqeBreakoutGroups\"\r\n                :key=\"group\"\r\n                kind=\"blue\"\r\n                class=\"breakout-group-tag\"\r\n              >\r\n                {{ group }}\r\n              </cv-tag>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Default message when no PQE selected -->\r\n        <div class=\"validation-section\" v-else>\r\n          <h3>Defect Validation</h3>\r\n          <div class=\"empty-state\">\r\n            <p>Please select a PQE Owner to view validation data.</p>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Legacy View Section -->\r\n        <div class=\"validation-section legacy-view\">\r\n          <h3>Legacy Validation View</h3>\r\n          <cv-content-switcher aria-label=\"Choose content\" @selected='reset()'>\r\n            <cv-content-switcher-button content-selector=\".content-1\" :selected=\"selectedIndex === 0\">Cables</cv-content-switcher-button>\r\n            <cv-content-switcher-button content-selector=\".content-2\" :selected=\"selectedIndex === 1\">Power/Thermal</cv-content-switcher-button>\r\n          </cv-content-switcher>\r\n\r\n          <div class=\"progress-section content-1\">\r\n            <cv-progress>\r\n              <cv-progress-step\r\n                v-for=\"(step, index) in cableStepsStatus\"\r\n                :key=\"index\"\r\n                :label=\"step.label\"\r\n                :complete=\"step.complete\"\r\n                @step-clicked=\"stepClick(step.pn, step.label)\"\r\n              ></cv-progress-step>\r\n            </cv-progress>\r\n          </div>\r\n\r\n          <div class=\"progress-section content-2\">\r\n            <cv-progress>\r\n              <cv-progress-step\r\n                v-for=\"(step, index) in powerThermalSteps\"\r\n                :key=\"index\"\r\n                :label=\"step.label\"\r\n                :complete=\"step.complete\"\r\n                @step-clicked=\"stepClick(step.pn, step.label)\"\r\n              ></cv-progress-step>\r\n            </cv-progress>\r\n          </div>\r\n\r\n          <h3>{{ clickedStepName }}</h3>\r\n\r\n          <div v-if=\"stepClicked\">\r\n            <cv-dropdown\r\n              label=\"Range of Fails\"\r\n              v-model=\"selectedRange\"\r\n              :items=\"rangeOptions\"\r\n              :selected-item=\"selectedRange\"\r\n            ></cv-dropdown>\r\n\r\n            <GaugeChart v-if=\"gaugeActive\" :data=\"gaugeData\" />\r\n\r\n            <div class=\"fail-container\">\r\n              <p><strong>Unvalidated count:</strong> {{ unvalidated_count }}</p>\r\n              <p><strong>Total number of Fails:</strong> {{ total_fails }}</p>\r\n\r\n              <!-- Buttons under the bar chart -->\r\n              <div class=\"button-container\">\r\n                <cv-button @click=\"viewData\">View Data</cv-button>\r\n                <cv-button @click=\"validateEach\">Validate Each</cv-button>\r\n                <cv-button @click=\"validateBulk\">Validate Bulk</cv-button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Validation Details Modal -->\r\n    <cv-modal\r\n      class=\"validation-details-modal\"\r\n      :visible=\"detailsModalVisible\"\r\n      @modal-hidden=\"detailsModalVisible = false\"\r\n      :size=\"'lg'\"\r\n    >\r\n      <template slot=\"title\">\r\n        <div>{{ selectedItem ? (selectedItem.partNumber ? 'Part Number: ' + selectedItem.partNumber : 'Group: ' + selectedItem.name) : 'Validation Details' }}</div>\r\n      </template>\r\n      <template slot=\"content\">\r\n        <div class=\"modal-content\" v-if=\"selectedItem\">\r\n          <!-- Status Banner -->\r\n          <div class=\"status-banner status-banner-in-progress\">\r\n            <div class=\"validation-summary\">\r\n              <span class=\"validation-label\">Validation Rate:</span>\r\n              <cv-tag :kind=\"getValidationTagKind(selectedItem.percentage)\">\r\n                {{ selectedItem.percentage }}%\r\n              </cv-tag>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Validation Details -->\r\n          <div class=\"modal-section\">\r\n            <div class=\"section-title\">Validation Summary</div>\r\n            <div class=\"info-grid\">\r\n              <div class=\"info-item\">\r\n                <span class=\"info-label\">Total Validations</span>\r\n                <span class=\"info-value\">{{ selectedItem.total }}</span>\r\n              </div>\r\n              <div class=\"info-item\">\r\n                <span class=\"info-label\">Validated</span>\r\n                <span class=\"info-value\">{{ selectedItem.validated }}</span>\r\n              </div>\r\n              <div class=\"info-item\">\r\n                <span class=\"info-label\">Unvalidated</span>\r\n                <span class=\"info-value\">{{ selectedItem.unvalidated }}</span>\r\n              </div>\r\n              <div class=\"info-item\">\r\n                <span class=\"info-label\">Time Period</span>\r\n                <span class=\"info-value\">{{ selectedTimeRange }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Validation Items Table -->\r\n          <div class=\"modal-section\">\r\n            <div class=\"section-title\">Validation Items</div>\r\n            <cv-data-table\r\n              :columns=\"validationDetailsColumns\"\r\n              :pagination=\"{ pageSize: 10 }\"\r\n              :title=\"''\"\r\n            >\r\n              <template slot=\"data\">\r\n                <cv-data-table-row\r\n                  v-for=\"(item, index) in selectedItemDetails\"\r\n                  :key=\"index\"\r\n                >\r\n                  <cv-data-table-cell>{{ item.defect_id }}</cv-data-table-cell>\r\n                  <cv-data-table-cell>{{ item.pn }}</cv-data-table-cell>\r\n                  <cv-data-table-cell>{{ item.sn }}</cv-data-table-cell>\r\n                  <cv-data-table-cell>{{ formatDate(item.date) }}</cv-data-table-cell>\r\n                  <cv-data-table-cell>\r\n                    <cv-tag :kind=\"item.status === 'validated' ? 'green' : 'red'\">\r\n                      {{ item.status === 'validated' ? 'Validated' : 'Unvalidated' }}\r\n                    </cv-tag>\r\n                  </cv-data-table-cell>\r\n                  <cv-data-table-cell>{{ item.root_cause_1 || 'N/A' }}</cv-data-table-cell>\r\n                  <cv-data-table-cell>\r\n                    <cv-button\r\n                      kind=\"ghost\"\r\n                      size=\"small\"\r\n                      @click=\"validateItem(item)\"\r\n                      v-if=\"item.status !== 'validated'\"\r\n                    >\r\n                      Validate\r\n                    </cv-button>\r\n                    <span v-else>-</span>\r\n                  </cv-data-table-cell>\r\n                </cv-data-table-row>\r\n\r\n                <!-- Empty state -->\r\n                <cv-data-table-row v-if=\"selectedItemDetails.length === 0\">\r\n                  <cv-data-table-cell colspan=\"7\" class=\"empty-message\">\r\n                    No validation items found.\r\n                  </cv-data-table-cell>\r\n                </cv-data-table-row>\r\n              </template>\r\n            </cv-data-table>\r\n          </div>\r\n\r\n          <!-- Bulk Validation Section -->\r\n          <div class=\"modal-section\" v-if=\"hasUnvalidatedItems\">\r\n            <div class=\"section-title\">Bulk Validation</div>\r\n            <div class=\"bulk-validation-form\">\r\n              <div class=\"form-group\">\r\n                <label class=\"form-label\">Root Cause</label>\r\n                <cv-dropdown\r\n                  v-model=\"bulkValidationRootCause\"\r\n                  label=\"Select Root Cause\"\r\n                  :items=\"rootCauseOptions\"\r\n                ></cv-dropdown>\r\n              </div>\r\n\r\n              <div class=\"form-group\">\r\n                <label class=\"form-label\">Comments</label>\r\n                <cv-text-area\r\n                  v-model=\"bulkValidationComments\"\r\n                  label=\"Comments\"\r\n                  placeholder=\"Enter validation comments\"\r\n                ></cv-text-area>\r\n              </div>\r\n\r\n              <div class=\"form-actions\">\r\n                <cv-button\r\n                  kind=\"primary\"\r\n                  @click=\"validateAllItems\"\r\n                >\r\n                  Validate All Unvalidated Items\r\n                </cv-button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Modal Actions -->\r\n          <div class=\"modal-actions\">\r\n            <cv-button kind=\"secondary\" @click=\"detailsModalVisible = false\">Close</cv-button>\r\n            <cv-button kind=\"primary\" @click=\"refreshItemDetails\">Refresh</cv-button>\r\n          </div>\r\n        </div>\r\n      </template>\r\n    </cv-modal>\r\n  </div>\r\n</template>\r\n\r\n\r\n<script>\r\nimport GaugeChart from '../../components/GaugeChart';\r\nimport MainHeader from '@/components/MainHeader'; // Import the MainHeader component\r\n\r\nexport default {\r\n  name: 'DefectValidations',\r\n  components: {\r\n    GaugeChart,\r\n    MainHeader\r\n  },\r\n\r\n  data() {\r\n    return {\r\n      // PQE Owner data\r\n      selectedPQEOwner: 'All',\r\n      pqeOwners: [],\r\n      pqeOwnerOptions: [{ label: 'All PQE Owners', value: 'All' }],\r\n\r\n      // PQE Validation data\r\n      pqeValidationData: {\r\n        total: 0,\r\n        validated: 0,\r\n        unvalidated: 0,\r\n        percentage: 0\r\n      },\r\n      pqePartNumbers: [],\r\n      pqeBreakoutGroups: [],\r\n\r\n      // Legacy view data\r\n      selectedIndex: 0, // Track the selected index\r\n      stepClicked: false, // Track whether a step is clicked\r\n      clickedStepName: '', // Track the clicked step's name\r\n      selectedRange: '', // Default range of fails\r\n\r\n      numberOfFails: 0, // Default number of fails\r\n      cableStepsStatus: [\r\n        { label: \"SMP9\", pn: \"02EA657\", complete: true },\r\n        { label: \"Signal\", pn: \"03FM185\", complete: false }, //signal\r\n        { label: \"CDFP\", pn: \"02EC799\", complete: true }, //cdfp\r\n      ],\r\n\r\n      powerThermalSteps: [\r\n        { label: \"Fans\", pn: \"02ED368\", complete: true },\r\n        { label: \"PSU\", pn: \"03KP588\", complete: false }, //signal\r\n        { label: \"PDU\", pn: \"03JG497\", complete: true }, //cdfp\r\n      ],\r\n      gaugeActive: false,\r\n      gaugeData: [\r\n        {\r\n          group: 'value',\r\n          value: 0\r\n        }\r\n      ], // Data for the gauge chart (number of validations left)\r\n      unvalidated_fails: [],\r\n      validated_fails: [],\r\n      unvalidated_count: 0,\r\n      validated_count: 0,\r\n      total_fails: 0,\r\n      perc_val: 0,\r\n      selectedPN: \"\",\r\n\r\n      // New UI data\r\n      isLoading: false,\r\n      loadingError: null,\r\n      searchQuery: '',\r\n      selectedProcess: 'All',\r\n      processOptions: ['All', 'FUL', 'FAB', 'Power', 'Cable', 'Memory'],\r\n      selectedTimeRange: 'Last 3 Months',\r\n      rangeOptions: [\"Last Month\", \"Last 3 Months\", \"Last 6 Months\", \"Last Year\", \"All Time\"],\r\n\r\n      // Validation data\r\n      validationColumns: ['Part Number', 'Group', 'Total', 'Validated', 'Unvalidated', 'Validation %', 'Actions'],\r\n      validationDetailsColumns: ['Defect ID', 'Part Number', 'Serial Number', 'Date', 'Status', 'Root Cause', 'Actions'],\r\n      validationsByGroup: [],\r\n      validationsByPart: [],\r\n\r\n      // Modal data\r\n      detailsModalVisible: false,\r\n      selectedItem: null,\r\n      selectedItemDetails: [],\r\n      bulkValidationRootCause: '',\r\n      bulkValidationComments: '',\r\n      rootCauseOptions: ['Design Issue', 'Manufacturing Defect', 'Material Issue', 'Test Error', 'Handling Damage', 'Unknown'],\r\n\r\n      // Summary data\r\n      validationsSummary: {\r\n        total: 0,\r\n        validated: 0,\r\n        unvalidated: 0,\r\n        validatedPercentage: 0,\r\n        unvalidatedPercentage: 0\r\n      },\r\n\r\n      // Date tracking\r\n      currentDate: '',\r\n      selectedMonthDate: '',\r\n      selectedWeekDate: '',\r\n      expandedSideNav: false,\r\n      useFixed: true\r\n    };\r\n  },\r\n\r\n  computed: {\r\n    hasUnvalidatedItems() {\r\n      return this.selectedItemDetails.some(item => item.status !== 'validated');\r\n    }\r\n  },\r\n\r\n  watch: {\r\n    selectedRange(newRange) {\r\n      if (newRange) {\r\n        this.gaugeActive = false;\r\n        this.get_unval();\r\n      }\r\n    }\r\n  },\r\n\r\n  mounted() {\r\n    this.get_dates();\r\n    this.loadPQEOwners();\r\n  },\r\n\r\n  methods: {\r\n    // PQE Owner methods\r\n    async loadPQEOwners() {\r\n      try {\r\n        const config = this.getAuthConfig();\r\n        const response = await fetch('/api-statit2/get_pqe_owners', {\r\n          method: 'POST',\r\n          headers: {\r\n            'Content-Type': 'application/json',\r\n            ...config.headers\r\n          },\r\n          body: JSON.stringify({})\r\n        });\r\n\r\n        if (!response.ok) {\r\n          throw new Error(`Failed to fetch PQE owners: ${response.status} ${response.statusText}`);\r\n        }\r\n\r\n        const data = await response.json();\r\n        if (data.status_res === 'success') {\r\n          this.pqeOwners = data.pqe_owners || [];\r\n\r\n          // Update dropdown options\r\n          this.pqeOwnerOptions = [\r\n            { label: 'All PQE Owners', value: 'All' },\r\n            ...this.pqeOwners.map(owner => ({ label: owner, value: owner }))\r\n          ];\r\n\r\n          console.log(`Loaded ${this.pqeOwners.length} PQE owners`);\r\n        } else {\r\n          console.error('Failed to load PQE owners:', data.message);\r\n        }\r\n      } catch (error) {\r\n        console.error('Error loading PQE owners:', error);\r\n      }\r\n    },\r\n\r\n    // Get authentication config\r\n    getAuthConfig() {\r\n      return {\r\n        headers: {\r\n          'Authorization': 'Bearer ' + (localStorage.getItem('token') || ''),\r\n          'X-User-ID': localStorage.getItem('userId') || ''\r\n        }\r\n      };\r\n    },\r\n\r\n    // Handle PQE owner change\r\n    async handlePQEOwnerChange() {\r\n      console.log(`Selected PQE owner: ${this.selectedPQEOwner}`);\r\n\r\n      if (this.selectedPQEOwner && this.selectedPQEOwner !== 'All') {\r\n        await this.loadPQEValidationData();\r\n      } else {\r\n        // Reset PQE data when \"All\" is selected\r\n        this.pqeValidationData = {\r\n          total: 0,\r\n          validated: 0,\r\n          unvalidated: 0,\r\n          percentage: 0\r\n        };\r\n        this.pqePartNumbers = [];\r\n        this.pqeBreakoutGroups = [];\r\n      }\r\n\r\n      this.updateValidationsSummary();\r\n    },\r\n\r\n    // Update validations summary\r\n    updateValidationsSummary() {\r\n      // Use PQE validation data if a specific PQE is selected\r\n      if (this.selectedPQEOwner && this.selectedPQEOwner !== 'All') {\r\n        this.validationsSummary = {\r\n          total: this.pqeValidationData.total,\r\n          validated: this.pqeValidationData.validated,\r\n          unvalidated: this.pqeValidationData.unvalidated,\r\n          validatedPercentage: this.pqeValidationData.percentage,\r\n          unvalidatedPercentage: this.pqeValidationData.total > 0 ? 100 - this.pqeValidationData.percentage : 0\r\n        };\r\n      } else {\r\n        // Reset summary when no specific PQE is selected\r\n        this.validationsSummary = {\r\n          total: 0,\r\n          validated: 0,\r\n          unvalidated: 0,\r\n          validatedPercentage: 0,\r\n          unvalidatedPercentage: 0\r\n        };\r\n      }\r\n    },\r\n\r\n    // Load PQE validation data\r\n    async loadPQEValidationData() {\r\n      if (!this.selectedPQEOwner || this.selectedPQEOwner === 'All') {\r\n        return;\r\n      }\r\n\r\n      try {\r\n        console.log(`Loading validation data for PQE owner: ${this.selectedPQEOwner}`);\r\n\r\n        // Get part numbers and breakout groups for this PQE owner\r\n        const pqeData = await this.getPQEData(this.selectedPQEOwner);\r\n        this.pqePartNumbers = pqeData.partNumbers;\r\n        this.pqeBreakoutGroups = pqeData.breakoutGroups;\r\n\r\n        if (this.pqePartNumbers.length === 0) {\r\n          console.warn(`No part numbers found for PQE owner: ${this.selectedPQEOwner}`);\r\n          this.pqeValidationData = {\r\n            total: 0,\r\n            validated: 0,\r\n            unvalidated: 0,\r\n            percentage: 0\r\n          };\r\n          return;\r\n        }\r\n\r\n        // Query validation data for these part numbers\r\n        let totalValidated = 0;\r\n        let totalUnvalidated = 0;\r\n\r\n        const user_type = this.$store.getters.getUser_type;\r\n        const token = this.$store.getters.getToken;\r\n        const action = \"view\";\r\n        const startdate = this.getStartDateFromRange(this.selectedTimeRange);\r\n\r\n        // Process each part number to get validation counts\r\n        for (const pn of this.pqePartNumbers) {\r\n          try {\r\n            const response = await fetch(process.env.VUE_APP_API_PATH + \"get_unval\", {\r\n              method: \"POST\",\r\n              headers: {\r\n                \"Content-Type\": \"application/json\",\r\n                Authorization: \"Bearer \" + token,\r\n              },\r\n              body: JSON.stringify({ \"PN\": pn, \"SD\": startdate, \"ED\": this.currentDate, user_type, action }),\r\n            });\r\n\r\n            if (response.ok) {\r\n              const data = await this.handleResponse(response);\r\n              if (data && data.status_res === \"success\") {\r\n                totalValidated += data.validated_count || 0;\r\n                totalUnvalidated += data.unvalidated_count || 0;\r\n              }\r\n            }\r\n          } catch (error) {\r\n            console.error(`Error fetching validation data for part ${pn}:`, error);\r\n          }\r\n        }\r\n\r\n        // Update PQE validation data\r\n        const totalFails = totalValidated + totalUnvalidated;\r\n        const percentage = totalFails > 0 ? Math.round((totalValidated / totalFails) * 100) : 0;\r\n\r\n        this.pqeValidationData = {\r\n          total: totalFails,\r\n          validated: totalValidated,\r\n          unvalidated: totalUnvalidated,\r\n          percentage: percentage\r\n        };\r\n\r\n        console.log(`PQE ${this.selectedPQEOwner} validation data:`, this.pqeValidationData);\r\n        console.log(`Part numbers (${this.pqePartNumbers.length}):`, this.pqePartNumbers);\r\n        console.log(`Breakout groups (${this.pqeBreakoutGroups.length}):`, this.pqeBreakoutGroups);\r\n\r\n      } catch (error) {\r\n        console.error(`Error loading PQE validation data:`, error);\r\n      }\r\n    },\r\n\r\n    // Get part numbers and breakout groups for a PQE owner\r\n    async getPQEData(pqeOwner) {\r\n      try {\r\n        // Use the existing PQE dashboard API to get breakout groups\r\n        const config = this.getAuthConfig();\r\n        const response = await fetch('/api-statit2/get_pqe_breakout_groups', {\r\n          method: 'POST',\r\n          headers: {\r\n            'Content-Type': 'application/json',\r\n            ...config.headers\r\n          },\r\n          body: JSON.stringify({ pqeOwner })\r\n        });\r\n\r\n        if (!response.ok) {\r\n          throw new Error(`Failed to fetch PQE data: ${response.status} ${response.statusText}`);\r\n        }\r\n\r\n        const data = await response.json();\r\n        if (data.status_res === 'success') {\r\n          const breakoutGroups = data.breakout_groups || [];\r\n\r\n          // Now get part numbers for these breakout groups\r\n          const partNumbers = await this.getPartNumbersForBreakoutGroups(breakoutGroups);\r\n\r\n          console.log(`Found ${breakoutGroups.length} breakout groups and ${partNumbers.length} part numbers for PQE owner ${pqeOwner}`);\r\n\r\n          return {\r\n            breakoutGroups: breakoutGroups,\r\n            partNumbers: partNumbers\r\n          };\r\n        } else {\r\n          console.error('Failed to get PQE data:', data.message);\r\n          return { breakoutGroups: [], partNumbers: [] };\r\n        }\r\n      } catch (error) {\r\n        console.error(`Error getting PQE data for ${pqeOwner}:`, error);\r\n        return { breakoutGroups: [], partNumbers: [] };\r\n      }\r\n    },\r\n\r\n    // Get part numbers for breakout groups\r\n    async getPartNumbersForBreakoutGroups(breakoutGroups) {\r\n      try {\r\n        if (!breakoutGroups || breakoutGroups.length === 0) {\r\n          return [];\r\n        }\r\n\r\n        const config = this.getAuthConfig();\r\n        const response = await fetch('/api-statit2/get_pqe_part_numbers', {\r\n          method: 'POST',\r\n          headers: {\r\n            'Content-Type': 'application/json',\r\n            ...config.headers\r\n          },\r\n          body: JSON.stringify({ breakoutGroups })\r\n        });\r\n\r\n        if (!response.ok) {\r\n          throw new Error(`Failed to fetch part numbers: ${response.status} ${response.statusText}`);\r\n        }\r\n\r\n        const data = await response.json();\r\n        if (data.status === 'success') {\r\n          return data.partNumbers || [];\r\n        } else {\r\n          console.error('Failed to get part numbers:', data.message);\r\n          return [];\r\n        }\r\n      } catch (error) {\r\n        console.error(`Error getting part numbers for breakout groups:`, error);\r\n        return [];\r\n      }\r\n    },\r\n\r\n    // Legacy methods\r\n    async get_unval() {\r\n      try {\r\n        let user_type = this.$store.getters.getUser_type;\r\n        let action = \"view\";\r\n        let startdate = \"\"\r\n        let token = this.$store.getters.getToken;\r\n        console.log(\"TOKEN\", token)\r\n        if (this.selectedRange === \"Past Month\"){\r\n          startdate = this.selectedMonthDate\r\n        } else if (this.selectedRange === \"Past Week\"){\r\n          startdate = this.selectedWeekDate\r\n        }\r\n\r\n        // Fetch data from the API\r\n        const response = await fetch(process.env.VUE_APP_API_PATH + \"get_unval\", {\r\n          method: \"POST\",\r\n          headers: {\r\n            \"Content-Type\": \"application/json\",\r\n            Authorization: \"Bearer \" + token,\r\n          },\r\n          body: JSON.stringify({ \"PN\": this.selectedPN, \"SD\": startdate, \"ED\": this.currentDate, user_type, action }),\r\n        });\r\n\r\n        if (response.status === 401) {\r\n          console.error(\"Unauthorized: Check your token or credentials.\");\r\n        }\r\n\r\n        const data = await this.handleResponse(response);\r\n\r\n        if (data.status_res === \"success\") {\r\n          this.unvalidated_fails = data.unvalidated_fails\r\n          this.validated_fails = data.validated_fails\r\n          this.unvalidated_count = data.unvalidated_count\r\n          this.validated_count = data.validated_count\r\n          this.total_fails = data.total_fails\r\n          this.perc_val = data.perc_val\r\n\r\n          console.log(\"Received data:\", data);\r\n          if(this.perc_val === null){\r\n            this.total_fails = \"No entries\"\r\n          }else{\r\n            this.gaugeActive = true;\r\n          }\r\n\r\n          this.gaugeData = [\r\n            {\r\n              group: 'value',\r\n              value: data.perc_val\r\n            }\r\n          ];\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error loading data:\", error);\r\n      }\r\n    },\r\n\r\n    get_dates() {\r\n      const currentDate = new Date();\r\n      const previousMonthDate = new Date();\r\n      const previousWeekDate = new Date();\r\n\r\n      previousMonthDate.setMonth(currentDate.getMonth() - 1);\r\n      // Subtract 7 days from the current date\r\n      previousWeekDate.setDate(currentDate.getDate() - 7);\r\n\r\n      // Format the dates (e.g., YYYY-MM-DD)\r\n      const formatDate = (date) => {\r\n        const year = date.getFullYear();\r\n        const month = String(date.getMonth() + 1).padStart(2, '0'); // Months are 0-indexed\r\n        const day = String(date.getDate()).padStart(2, '0');\r\n        return `${year}-${month}-${day}`;\r\n      };\r\n\r\n      // Create the selectedRange string\r\n      this.selectedWeekDate = formatDate(previousWeekDate)\r\n      this.selectedMonthDate = formatDate(previousMonthDate)\r\n      this.currentDate = formatDate(currentDate)\r\n    },\r\n\r\n    stepClick(pn, label) {\r\n      this.gaugeActive = false;\r\n      this.stepClicked = true; // Show the clicked info section\r\n      this.selectedPN = pn;\r\n      this.get_unval();\r\n      this.clickedStepName = `${pn} - ${label}`; // Update the clicked step's name\r\n    },\r\n\r\n    validateEach() {\r\n      console.log('Validating each for:', this.selectedPN);\r\n      // Implement logic for validating each item here\r\n    },\r\n\r\n    validateBulk() {\r\n      console.log('Validating bulk for:', this.selectedPN);\r\n      // Implement logic for bulk validation here\r\n    },\r\n\r\n    handleResponse(response) {\r\n      if (!response.ok) {\r\n        if (response.status === 401) {\r\n          this.session_expired_visible = true;\r\n        }\r\n      } else {\r\n        return response.json();\r\n      }\r\n    },\r\n\r\n    reset() {\r\n      this.stepClicked = false; // Reset the step clicked status\r\n      this.clickedStepName = 'Choose PN'; // Reset the clicked step name\r\n      this.selectedRange = 'Monthly'; // Reset the selected range\r\n      this.gaugeActive = false; // Hide the gauge chart\r\n      this.selectedPN = ''; // Reset the selected part number\r\n      // Any other reset logic can go here\r\n      console.log('Content switcher reset');\r\n    },\r\n\r\n    // Utility methods\r\n\r\n    async getPartNumbersForProcess(process) {\r\n      try {\r\n        let user_type = this.$store.getters.getUser_type;\r\n        let token = this.$store.getters.getToken;\r\n\r\n        // If \"All\" is selected, get all Metis part numbers\r\n        if (process === 'All') {\r\n          const response = await fetch(process.env.VUE_APP_API_PATH + \"get_metis_part_numbers\", {\r\n            method: \"POST\",\r\n            headers: {\r\n              \"Content-Type\": \"application/json\",\r\n              Authorization: \"Bearer \" + token,\r\n            },\r\n            body: JSON.stringify({ user_type }),\r\n          });\r\n\r\n          const data = await this.handleResponse(response);\r\n\r\n          if (data && data.status_res === \"success\" && data.pns) {\r\n            console.log(`Retrieved ${data.pns.length} part numbers from Metis file`);\r\n            return data.pns;\r\n          }\r\n        } else {\r\n          // For specific processes, use the commodity filter\r\n          const response = await fetch(process.env.VUE_APP_API_PATH + \"get_pns_from_excel\", {\r\n            method: \"POST\",\r\n            headers: {\r\n              \"Content-Type\": \"application/json\",\r\n              Authorization: \"Bearer \" + token,\r\n            },\r\n            body: JSON.stringify({ commodity: process, user_type }),\r\n          });\r\n\r\n          const data = await this.handleResponse(response);\r\n\r\n          if (data && data.status_res === \"success\" && data.pns) {\r\n            console.log(`Retrieved ${data.pns.length} part numbers for ${process} from commodity file`);\r\n            return data.pns;\r\n          }\r\n        }\r\n\r\n        // Fallback to hardcoded values if API fails\r\n        console.warn(\"Falling back to hardcoded part numbers\");\r\n        const fallbackParts = {\r\n          'All': [\"02EA657\", \"03FM185\", \"02EC799\", \"02ED368\", \"03KP588\", \"03JG497\", \"01KP123\", \"02FM456\", \"03EC789\"],\r\n          'FUL': [\"01KP123\", \"02FM456\", \"03EC789\"],\r\n          'FAB': [\"04KP321\", \"05FM654\", \"06EC987\"],\r\n          'Power': [\"02ED368\", \"03KP588\", \"03JG497\"],\r\n          'Cable': [\"02EA657\", \"03FM185\", \"02EC799\"],\r\n          'Memory': [\"07KP111\", \"08FM222\", \"09EC333\"]\r\n        };\r\n\r\n        return fallbackParts[process] || fallbackParts['All'];\r\n      } catch (error) {\r\n        console.error(\"Error fetching part numbers:\", error);\r\n        // Return fallback values in case of error\r\n        return [\"02EA657\", \"03FM185\", \"02EC799\"];\r\n      }\r\n    },\r\n\r\n    async getMetisGroupForPartNumber(partNumber) {\r\n      try {\r\n        let user_type = this.$store.getters.getUser_type;\r\n        let token = this.$store.getters.getToken;\r\n\r\n        // First try to get the Metis breakout names\r\n        const response = await fetch(process.env.VUE_APP_API_PATH + \"get_metis_breakout_names\", {\r\n          method: \"POST\",\r\n          headers: {\r\n            \"Content-Type\": \"application/json\",\r\n            Authorization: \"Bearer \" + token,\r\n          },\r\n          body: JSON.stringify({ user_type }),\r\n        });\r\n\r\n        const data = await this.handleResponse(response);\r\n\r\n        if (data && data.status_res === \"success\" && data.breakoutMap) {\r\n          // The API should return a mapping of part numbers to breakout names\r\n          const breakoutName = data.breakoutMap[partNumber];\r\n          if (breakoutName) {\r\n            return breakoutName;\r\n          }\r\n        }\r\n\r\n        // If we couldn't get the breakout name from the API, use a fallback mapping\r\n        // This is a simplified version of what would be in the Excel file\r\n        const fallbackGroupMap = {\r\n          \"02EA657\": \"SMP9 Cables\",\r\n          \"03FM185\": \"Signal Cables\",\r\n          \"02EC799\": \"CDFP Cables\",\r\n          \"02ED368\": \"Cooling Fans\",\r\n          \"03KP588\": \"Power Supply Units\",\r\n          \"03JG497\": \"Power Distribution\",\r\n          \"01KP123\": \"Memory Module A\",\r\n          \"02FM456\": \"Memory Module B\",\r\n          \"03EC789\": \"Memory Module C\",\r\n          \"04KP321\": \"Processor Module A\",\r\n          \"05FM654\": \"Processor Module B\",\r\n          \"06EC987\": \"Processor Module C\",\r\n          \"07KP111\": \"Storage Module A\",\r\n          \"08FM222\": \"Storage Module B\",\r\n          \"09EC333\": \"Storage Module C\"\r\n        };\r\n\r\n        return fallbackGroupMap[partNumber] || \"Unknown Group\";\r\n      } catch (error) {\r\n        console.error(\"Error fetching Metis group for part number:\", error);\r\n        // Return a generic group name in case of error\r\n        return \"Unknown Group\";\r\n      }\r\n    },\r\n\r\n    getStartDateFromRange(range) {\r\n      const now = new Date();\r\n      let startDate = new Date();\r\n\r\n      switch (range) {\r\n        case \"Last Month\":\r\n          startDate.setMonth(now.getMonth() - 1);\r\n          break;\r\n        case \"Last 3 Months\":\r\n          startDate.setMonth(now.getMonth() - 3);\r\n          break;\r\n        case \"Last 6 Months\":\r\n          startDate.setMonth(now.getMonth() - 6);\r\n          break;\r\n        case \"Last Year\":\r\n          startDate.setFullYear(now.getFullYear() - 1);\r\n          break;\r\n        case \"All Time\":\r\n          startDate = new Date(2000, 0, 1); // Far in the past\r\n          break;\r\n        default:\r\n          startDate.setMonth(now.getMonth() - 3); // Default to 3 months\r\n      }\r\n\r\n      // Format date as YYYY-MM-DD\r\n      return startDate.toISOString().split('T')[0];\r\n    },\r\n\r\n    filterValidations() {\r\n      // Filtering is handled by computed properties\r\n      console.log(\"Filtering with query:\", this.searchQuery);\r\n    },\r\n\r\n\r\n\r\n    async loadItemDetails(item) {\r\n      this.selectedItemDetails = [];\r\n\r\n      try {\r\n        let user_type = this.$store.getters.getUser_type;\r\n        let action = \"view\";\r\n        let startdate = this.getStartDateFromRange(this.selectedTimeRange);\r\n        let token = this.$store.getters.getToken;\r\n\r\n        console.log(`Loading details for ${item.partNumber ? 'part ' + item.partNumber : 'group ' + item.name}`);\r\n\r\n        // If it's a part number item\r\n        if (item.partNumber) {\r\n          const response = await fetch(process.env.VUE_APP_API_PATH + \"get_unval\", {\r\n            method: \"POST\",\r\n            headers: {\r\n              \"Content-Type\": \"application/json\",\r\n              Authorization: \"Bearer \" + token,\r\n            },\r\n            body: JSON.stringify({ \"PN\": item.partNumber, \"SD\": startdate, \"ED\": this.currentDate, user_type, action }),\r\n          });\r\n\r\n          const data = await this.handleResponse(response);\r\n\r\n          if (data && data.status_res === \"success\") {\r\n            // Combine validated and unvalidated fails\r\n            const validatedItems = (data.validated_fails || []).map(item => ({\r\n              ...item,\r\n              status: 'validated'\r\n            }));\r\n\r\n            const unvalidatedItems = (data.unvalidated_fails || []).map(item => ({\r\n              ...item,\r\n              status: 'unvalidated'\r\n            }));\r\n\r\n            this.selectedItemDetails = [...validatedItems, ...unvalidatedItems];\r\n            console.log(`Loaded ${this.selectedItemDetails.length} validation items for part ${item.partNumber}`);\r\n          }\r\n        }\r\n        // If it's a group item (Metis grouping)\r\n        else if (item.name) {\r\n          // First try to get part numbers for this Metis group from the API\r\n          let partNumbers = [];\r\n\r\n          try {\r\n            // Try to get part numbers for this Metis group from the API\r\n            const metisResponse = await fetch(process.env.VUE_APP_API_PATH + \"get_metis_part_numbers\", {\r\n              method: \"POST\",\r\n              headers: {\r\n                \"Content-Type\": \"application/json\",\r\n                Authorization: \"Bearer \" + token,\r\n              },\r\n              body: JSON.stringify({ breakoutName: item.name, user_type }),\r\n            });\r\n\r\n            const metisData = await this.handleResponse(metisResponse);\r\n\r\n            if (metisData && metisData.status_res === \"success\" && metisData.pns && metisData.pns.length > 0) {\r\n              partNumbers = metisData.pns;\r\n              console.log(`Retrieved ${partNumbers.length} part numbers for Metis group ${item.name} from API`);\r\n            }\r\n          } catch (error) {\r\n            console.error(`Error fetching part numbers for Metis group ${item.name}:`, error);\r\n          }\r\n\r\n          // If we couldn't get part numbers from the API, fall back to the local data\r\n          if (partNumbers.length === 0) {\r\n            partNumbers = this.validationsByPart\r\n              .filter(part => part.group === item.name)\r\n              .map(part => part.partNumber);\r\n\r\n            console.log(`Using ${partNumbers.length} part numbers for Metis group ${item.name} from local data`);\r\n          }\r\n\r\n          if (partNumbers.length === 0) {\r\n            console.warn(`No part numbers found for Metis group ${item.name}`);\r\n            return;\r\n          }\r\n\r\n          // Fetch details for each part number\r\n          let allDetails = [];\r\n\r\n          for (const pn of partNumbers) {\r\n            try {\r\n              const response = await fetch(process.env.VUE_APP_API_PATH + \"get_unval\", {\r\n                method: \"POST\",\r\n                headers: {\r\n                  \"Content-Type\": \"application/json\",\r\n                  Authorization: \"Bearer \" + token,\r\n                },\r\n                body: JSON.stringify({ \"PN\": pn, \"SD\": startdate, \"ED\": this.currentDate, user_type, action }),\r\n              });\r\n\r\n              const data = await this.handleResponse(response);\r\n\r\n              if (data && data.status_res === \"success\") {\r\n                // Combine validated and unvalidated fails\r\n                const validatedItems = (data.validated_fails || []).map(item => ({\r\n                  ...item,\r\n                  status: 'validated'\r\n                }));\r\n\r\n                const unvalidatedItems = (data.unvalidated_fails || []).map(item => ({\r\n                  ...item,\r\n                  status: 'unvalidated'\r\n                }));\r\n\r\n                allDetails = [...allDetails, ...validatedItems, ...unvalidatedItems];\r\n                console.log(`Added ${validatedItems.length + unvalidatedItems.length} items for part ${pn}`);\r\n              }\r\n            } catch (error) {\r\n              console.error(`Error fetching validation data for part ${pn}:`, error);\r\n              // Continue with other part numbers\r\n            }\r\n          }\r\n\r\n          this.selectedItemDetails = allDetails;\r\n          console.log(`Loaded ${this.selectedItemDetails.length} total validation items for group ${item.name}`);\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error loading item details:\", error);\r\n      }\r\n    },\r\n\r\n    refreshItemDetails() {\r\n      if (this.selectedItem) {\r\n        this.loadItemDetails(this.selectedItem);\r\n      }\r\n    },\r\n\r\n    validateItem(item) {\r\n      console.log(\"Validating item:\", item);\r\n      // This would typically call an API to validate the item\r\n      // For demo purposes, we'll just update the local state\r\n      item.status = 'validated';\r\n      item.root_cause_1 = this.bulkValidationRootCause || 'Manual Validation';\r\n\r\n      // Update counts\r\n      if (this.selectedItem) {\r\n        this.selectedItem.validated++;\r\n        this.selectedItem.unvalidated--;\r\n        this.selectedItem.percentage = Math.round((this.selectedItem.validated / this.selectedItem.total) * 100);\r\n      }\r\n    },\r\n\r\n    validateAllItems() {\r\n      if (!this.bulkValidationRootCause) {\r\n        alert(\"Please select a root cause before validating all items.\");\r\n        return;\r\n      }\r\n\r\n      // Find all unvalidated items\r\n      const unvalidatedItems = this.selectedItemDetails.filter(item => item.status !== 'validated');\r\n\r\n      // Validate each item\r\n      unvalidatedItems.forEach(item => {\r\n        item.status = 'validated';\r\n        item.root_cause_1 = this.bulkValidationRootCause;\r\n      });\r\n\r\n      // Update counts\r\n      if (this.selectedItem) {\r\n        this.selectedItem.validated = this.selectedItem.total;\r\n        this.selectedItem.unvalidated = 0;\r\n        this.selectedItem.percentage = 100;\r\n      }\r\n\r\n      // Clear form\r\n      this.bulkValidationRootCause = '';\r\n      this.bulkValidationComments = '';\r\n    },\r\n\r\n    getValidationTagKind(percentage) {\r\n      if (percentage >= 90) return 'green';\r\n      if (percentage >= 70) return 'teal';\r\n      if (percentage >= 50) return 'blue';\r\n      if (percentage >= 30) return 'purple';\r\n      if (percentage >= 10) return 'magenta';\r\n      return 'red';\r\n    },\r\n\r\n    formatDate(dateString) {\r\n      if (!dateString) return 'N/A';\r\n\r\n      // If it's already in a readable format, return as is\r\n      if (typeof dateString === 'string' && dateString.includes('/')) {\r\n        return dateString;\r\n      }\r\n\r\n      // Try to parse the date\r\n      try {\r\n        const date = new Date(dateString);\r\n        return date.toLocaleDateString();\r\n      } catch (e) {\r\n        return dateString;\r\n      }\r\n    },\r\n\r\n    viewData() {\r\n      console.log(\"View data clicked\");\r\n      // Implement view data functionality\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style scoped lang=\"scss\">\r\n@import \"../../styles/carbon-utils\";\r\n\r\n/* Main container */\r\n.validations-container {\r\n  min-height: 100vh;\r\n  background-color: #161616;\r\n  color: #f4f4f4;\r\n}\r\n\r\n/* Page header */\r\n.page-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 2rem;\r\n  padding: 2rem 2rem 1rem;\r\n  border-bottom: 1px solid #333333;\r\n}\r\n\r\n.page-title {\r\n  color: #f4f4f4;\r\n  font-size: 1.75rem;\r\n  font-weight: 400;\r\n  margin: 0;\r\n}\r\n\r\n.header-actions {\r\n  display: flex;\r\n  gap: 1rem;\r\n}\r\n\r\n/* Filter bar */\r\n.filter-bar {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin: 0 2rem 1.5rem;\r\n  background-color: #262626;\r\n  padding: 1rem;\r\n  border-radius: 8px;\r\n  border: 1px solid #333333;\r\n}\r\n\r\n.filter-group {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 1rem;\r\n}\r\n\r\n.filter-label {\r\n  color: #8d8d8d;\r\n  font-size: 0.875rem;\r\n}\r\n\r\n.search-box {\r\n  flex-grow: 1;\r\n  max-width: 300px;\r\n}\r\n\r\n/* Loading and error states */\r\n.loading-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 3rem;\r\n  margin: 2rem;\r\n  background-color: #262626;\r\n  border-radius: 8px;\r\n  border: 1px solid #333333;\r\n}\r\n\r\n.loading-text {\r\n  margin-top: 1rem;\r\n  color: #8d8d8d;\r\n}\r\n\r\n.error-container {\r\n  margin: 2rem;\r\n}\r\n\r\n/* Content area */\r\n.validations-content {\r\n  padding: 0 2rem 2rem;\r\n}\r\n\r\n.main-validation-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 2rem;\r\n}\r\n\r\n.validation-section {\r\n  background-color: #262626;\r\n  border: 1px solid #333333;\r\n  border-radius: 8px;\r\n  padding: 1.5rem;\r\n\r\n  h3 {\r\n    color: #f4f4f4;\r\n    font-size: 1.25rem;\r\n    font-weight: 600;\r\n    margin: 0 0 1.5rem 0;\r\n    padding-bottom: 0.75rem;\r\n    border-bottom: 1px solid #393939;\r\n  }\r\n}\r\n\r\n/* Validation counts styles */\r\n.validation-counts {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\r\n  gap: 1.5rem;\r\n  margin-bottom: 2rem;\r\n}\r\n\r\n.count-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  padding: 2rem 1rem;\r\n  background-color: #393939;\r\n  border-radius: 8px;\r\n  border: 1px solid #525252;\r\n  text-align: center;\r\n}\r\n\r\n.count-number {\r\n  font-size: 3rem;\r\n  font-weight: 700;\r\n  margin-bottom: 0.5rem;\r\n}\r\n\r\n.count-label {\r\n  font-size: 0.875rem;\r\n  color: #c6c6c6;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.16px;\r\n}\r\n\r\n.count-item.validated .count-number {\r\n  color: #42be65;\r\n}\r\n\r\n.count-item.unvalidated .count-number {\r\n  color: #fa4d56;\r\n}\r\n\r\n.count-item.total .count-number {\r\n  color: #0f62fe;\r\n}\r\n\r\n/* Part numbers and breakout groups sections */\r\n.part-numbers-section,\r\n.breakout-groups-section {\r\n  margin-bottom: 2rem;\r\n\r\n  h4 {\r\n    color: #f4f4f4;\r\n    font-size: 1rem;\r\n    font-weight: 600;\r\n    margin-bottom: 1rem;\r\n    padding-bottom: 0.5rem;\r\n    border-bottom: 1px solid #525252;\r\n  }\r\n}\r\n\r\n.part-numbers-list,\r\n.breakout-groups-list {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 0.5rem;\r\n  max-height: 200px;\r\n  overflow-y: auto;\r\n  padding: 0.5rem;\r\n  background-color: #393939;\r\n  border-radius: 8px;\r\n  border: 1px solid #525252;\r\n}\r\n\r\n.part-number-tag,\r\n.breakout-group-tag {\r\n  font-size: 0.75rem;\r\n  white-space: nowrap;\r\n}\r\n\r\n/* Empty state */\r\n.empty-state {\r\n  text-align: center;\r\n  padding: 3rem;\r\n  color: #8d8d8d;\r\n\r\n  p {\r\n    font-size: 1.125rem;\r\n    margin: 0;\r\n  }\r\n}\r\n\r\n/* Summary tiles */\r\n.validations-summary {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\r\n  gap: 1rem;\r\n  margin-bottom: 2rem;\r\n}\r\n\r\n.summary-tile {\r\n  background-color: #262626;\r\n  border: 1px solid #333333;\r\n  border-radius: 8px;\r\n  padding: 1.5rem;\r\n  text-align: center;\r\n}\r\n\r\n.tile-title {\r\n  color: #8d8d8d;\r\n  font-size: 0.875rem;\r\n  margin-bottom: 0.5rem;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.16px;\r\n}\r\n\r\n.tile-value {\r\n  color: #f4f4f4;\r\n  font-size: 2rem;\r\n  font-weight: 600;\r\n  margin-bottom: 0.25rem;\r\n\r\n  &.validated {\r\n    color: #42be65;\r\n  }\r\n\r\n  &.unvalidated {\r\n    color: #fa4d56;\r\n  }\r\n}\r\n\r\n.tile-percentage {\r\n  color: #8d8d8d;\r\n  font-size: 0.875rem;\r\n  font-weight: 400;\r\n}\r\n\r\n/* Empty states */\r\n.empty-message {\r\n  color: #8d8d8d;\r\n  text-align: center;\r\n  padding: 2rem;\r\n}\r\n\r\n/* Modal styles */\r\n.validation-details-modal {\r\n  max-width: 1000px;\r\n}\r\n\r\n.modal-content {\r\n  padding: 1.5rem 0;\r\n  color: #f4f4f4;\r\n}\r\n\r\n.modal-section {\r\n  margin-bottom: 2rem;\r\n}\r\n\r\n.section-title {\r\n  font-weight: 600;\r\n  margin-bottom: 1rem;\r\n  font-size: 1.125rem;\r\n  color: #f4f4f4;\r\n  border-bottom: 1px solid #333333;\r\n  padding-bottom: 0.5rem;\r\n}\r\n\r\n.info-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(2, 1fr);\r\n  gap: 1.5rem;\r\n  margin-bottom: 1.5rem;\r\n}\r\n\r\n.info-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.info-label {\r\n  font-size: 0.875rem;\r\n  color: #8d8d8d;\r\n  margin-bottom: 0.375rem;\r\n}\r\n\r\n.info-value {\r\n  font-size: 1rem;\r\n  color: #f4f4f4;\r\n}\r\n\r\n.status-banner {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 1rem;\r\n  border-radius: 8px;\r\n  margin-bottom: 1.5rem;\r\n  background-color: rgba(15, 98, 254, 0.1);\r\n  border: 1px solid rgba(15, 98, 254, 0.3);\r\n}\r\n\r\n.validation-summary {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n}\r\n\r\n.validation-label {\r\n  color: #f4f4f4;\r\n  font-weight: 500;\r\n}\r\n\r\n.bulk-validation-form {\r\n  background-color: #262626;\r\n  padding: 1.5rem;\r\n  border-radius: 8px;\r\n  border: 1px solid #333333;\r\n  margin-top: 1rem;\r\n}\r\n\r\n.form-group {\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.form-label {\r\n  display: block;\r\n  font-size: 0.875rem;\r\n  color: #8d8d8d;\r\n  margin-bottom: 0.5rem;\r\n}\r\n\r\n.form-actions {\r\n  margin-top: 1.5rem;\r\n  display: flex;\r\n  justify-content: flex-end;\r\n}\r\n\r\n.modal-actions {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  gap: 1rem;\r\n  margin-top: 2rem;\r\n}\r\n\r\n/* Legacy view styles */\r\n.legacy-view {\r\n  padding: 1rem;\r\n  background-color: #262626;\r\n  border-radius: 8px;\r\n  border: 1px solid #333333;\r\n}\r\n\r\n.chart-page__grid {\r\n  margin-top: $spacing-08;\r\n}\r\n\r\n.progress-section {\r\n  margin-top: 2rem;\r\n  margin-bottom: 2rem;\r\n}\r\n\r\n.button-container {\r\n  margin-top: 20px;\r\n  display: flex;\r\n  gap: 1rem; /* Adjust space between buttons */\r\n}\r\n\r\n.fail-container {\r\n  display: flex;                /* Enable flexbox */\r\n  flex-direction: column;       /* Stack items vertically */\r\n  align-items: center;          /* Center horizontally */\r\n  justify-content: center;      /* Center vertically */\r\n  text-align: center;           /* Center text */\r\n  margin: 20px;                 /* Optional: Add margin for spacing */\r\n}\r\n\r\n/* Responsive adjustments */\r\n@media (max-width: 1024px) {\r\n  .info-grid {\r\n    grid-template-columns: 1fr;\r\n  }\r\n\r\n  .filter-bar {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 1rem;\r\n  }\r\n\r\n  .filter-group {\r\n    width: 100%;\r\n    justify-content: space-between;\r\n  }\r\n\r\n  .search-box {\r\n    max-width: 100%;\r\n  }\r\n\r\n  .validations-summary {\r\n    flex-direction: column;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .page-header {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 1rem;\r\n  }\r\n\r\n  .header-actions {\r\n    width: 100%;\r\n  }\r\n}\r\n</style>"]}]}