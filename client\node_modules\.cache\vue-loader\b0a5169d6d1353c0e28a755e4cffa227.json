{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\PQEDashboard\\PQEOwnerDashboard.vue?vue&type=template&id=25dba680&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\PQEDashboard\\PQEOwnerDashboard.vue", "mtime": 1748986897909}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}