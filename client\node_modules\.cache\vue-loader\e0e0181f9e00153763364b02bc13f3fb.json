{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\PQEDashboard\\PQEDashboardPage.vue?vue&type=style&index=0&id=06b3b034&scoped=true&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\PQEDashboard\\PQEDashboardPage.vue", "mtime": 1748987610870}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\@vue\\cli-service\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5kYXNoYm9hcmQtY29udGFpbmVyIHsKICBtaW4taGVpZ2h0OiAxMDB2aDsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjMTYxNjE2Owp9CgoubWFpbi1jb250ZW50IHsKICBwYWRkaW5nOiAycmVtOwogIG1heC13aWR0aDogMTQwMHB4OwogIG1hcmdpbjogMCBhdXRvOwp9CgoucGFnZS1oZWFkZXIgewogIG1hcmdpbi1ib3R0b206IDJyZW07Cn0KCi5wYWdlLXRpdGxlIHsKICBjb2xvcjogI2Y0ZjRmNDsKICBmb250LXNpemU6IDEuNzVyZW07CiAgZm9udC13ZWlnaHQ6IDQwMDsKICBtYXJnaW46IDA7Cn0KCi5kYXNoYm9hcmQtdGFicyB7CiAgYmFja2dyb3VuZC1jb2xvcjogIzI2MjYyNjsKICBib3JkZXItcmFkaXVzOiA4cHg7CiAgb3ZlcmZsb3c6IGhpZGRlbjsKICBib3JkZXI6IDFweCBzb2xpZCAjMzMzMzMzOwogIGJveC1zaGFkb3c6IDAgNHB4IDhweCByZ2JhKDAsIDAsIDAsIDAuMik7Cn0KCi50YWItY29udGVudCB7CiAgcGFkZGluZzogMS41cmVtOwp9CgoucHFlLXNlbGVjdGlvbi1jb250YWluZXIgewogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBtYXJnaW4tYm90dG9tOiAxLjVyZW07CiAgYmFja2dyb3VuZC1jb2xvcjogIzMzMzMzMzsKICBwYWRkaW5nOiAxcmVtOwogIGJvcmRlci1yYWRpdXM6IDhweDsKfQoKLnNlbGVjdGlvbi1sYWJlbCB7CiAgY29sb3I6ICNmNGY0ZjQ7CiAgbWFyZ2luLXJpZ2h0OiAxcmVtOwogIGZvbnQtc2l6ZTogMC44NzVyZW07CiAgd2hpdGUtc3BhY2U6IG5vd3JhcDsKfQoKLnBxZS1kcm9wZG93biB7CiAgd2lkdGg6IDMwMHB4Owp9Cgouc2VsZWN0LXBxZS1tZXNzYWdlIHsKICBjb2xvcjogIzhkOGQ4ZDsKICBmb250LXNpemU6IDFyZW07CiAgdGV4dC1hbGlnbjogY2VudGVyOwogIHBhZGRpbmc6IDJyZW07CiAgYmFja2dyb3VuZC1jb2xvcjogIzMzMzMzMzsKICBib3JkZXItcmFkaXVzOiA4cHg7Cn0K"}, {"version": 3, "sources": ["PQEDashboardPage.vue"], "names": [], "mappings": ";AA2NA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "PQEDashboardPage.vue", "sourceRoot": "src/views/PQEDashboard", "sourcesContent": ["<template>\n  <div class=\"dashboard-container\">\n    <!-- Inherit the MainHeader component -->\n    <MainHeader :expandedSideNav=\"expandedSideNav\" :useFixed=\"useFixed\" />\n\n    <main class=\"main-content\">\n      <!-- Page Header -->\n      <div class=\"page-header\">\n        <h1 class=\"page-title\">PQE Dashboard</h1>\n      </div>\n\n      <!-- Tabs for PQE Owner and QE Dashboards -->\n      <div class=\"dashboard-tabs\">\n        <cv-tabs aria-label=\"PQE Dashboard Tabs\">\n          <cv-tab id=\"pqe-owner-tab\" label=\"PQE Owner Dashboard\">\n            <div class=\"tab-content\">\n              <!-- PQE Owner Selection -->\n              <div class=\"pqe-selection-container\">\n                <label for=\"pqe-owner-dropdown\" class=\"selection-label\">Select PQE Owner:</label>\n                <cv-dropdown\n                  id=\"pqe-owner-dropdown\"\n                  v-model=\"selectedPQEOwner\"\n                  @change=\"handlePQEOwnerChange\"\n                  class=\"pqe-dropdown\"\n                >\n                  <cv-dropdown-item\n                    v-for=\"owner in pqeOwners\"\n                    :key=\"owner\"\n                    :value=\"owner\"\n                  >\n                    {{ owner }}\n                  </cv-dropdown-item>\n                </cv-dropdown>\n              </div>\n\n              <!-- PQE Owner Dashboard Component -->\n              <PQEOwnerDashboard\n                v-if=\"selectedPQEOwner\"\n                :pqeOwner=\"selectedPQEOwner\"\n                @update-action-tracker=\"updateActionTracker\"\n                @navigate-to-action-tracker=\"navigateToActionTracker\"\n              />\n              <div v-else class=\"select-pqe-message\">\n                Please select a PQE owner to view their dashboard.\n              </div>\n            </div>\n          </cv-tab>\n          <cv-tab id=\"qe-tab\" label=\"QE Dashboard\">\n            <div class=\"tab-content\">\n              <!-- QE Dashboard Component -->\n              <QEDashboard @select-pqe=\"selectPQEFromQEDashboard\" />\n            </div>\n          </cv-tab>\n        </cv-tabs>\n      </div>\n    </main>\n  </div>\n</template>\n\n<script>\nimport MainHeader from '@/components/MainHeader/MainHeader';\nimport PQEOwnerDashboard from './PQEOwnerDashboard';\nimport QEDashboard from './QEDashboard';\nimport { CvTabs, CvTab, CvDropdown, CvDropdownItem } from '@carbon/vue';\n\nexport default {\n  name: 'PQEDashboardPage',\n  components: {\n    MainHeader,\n    PQEOwnerDashboard,\n    QEDashboard,\n    CvTabs,\n    CvTab,\n    CvDropdown,\n    CvDropdownItem\n  },\n  data() {\n    return {\n      expandedSideNav: false,\n      useFixed: false,\n      selectedPQEOwner: '',\n      pqeOwners: [],\n      isLoading: false\n    };\n  },\n  mounted() {\n    this.loadPQEOwners();\n  },\n  methods: {\n    async loadPQEOwners() {\n      this.isLoading = true;\n      try {\n        // Get authentication config\n        const config = this.getAuthConfig();\n\n        // Fetch PQE owners from the API\n        const response = await fetch('/api-statit2/get_pqe_owners', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({})\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch PQE owners: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success') {\n          this.pqeOwners = data.pqe_owners || [];\n          console.log(`Loaded ${this.pqeOwners.length} PQE owners`);\n\n          // If Albert G. is in the list, select him by default\n          const defaultOwner = this.pqeOwners.find(owner => owner === 'Albert G.');\n          if (defaultOwner) {\n            this.selectedPQEOwner = defaultOwner;\n          } else if (this.pqeOwners.length > 0) {\n            // Otherwise select the first owner\n            this.selectedPQEOwner = this.pqeOwners[0];\n          }\n        } else {\n          console.error('Failed to load PQE owners:', data.message);\n        }\n      } catch (error) {\n        console.error('Error loading PQE owners:', error);\n      } finally {\n        this.isLoading = false;\n      }\n    },\n\n    handlePQEOwnerChange() {\n      console.log(`Selected PQE owner: ${this.selectedPQEOwner}`);\n    },\n\n    updateActionTracker(actionData) {\n      console.log('Updating action tracker with:', actionData);\n\n      // Call the API to update the action tracker\n      this.isLoading = true;\n\n      fetch('/api-statit2/update_pqe_action', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          ...this.getAuthConfig().headers\n        },\n        body: JSON.stringify(actionData)\n      })\n        .then(response => {\n          if (!response.ok) {\n            throw new Error(`Failed to update action tracker: ${response.status} ${response.statusText}`);\n          }\n          return response.json();\n        })\n        .then(data => {\n          if (data.status_res === 'success') {\n            console.log('Action tracker updated successfully');\n          } else {\n            console.error('Failed to update action tracker:', data.message);\n          }\n        })\n        .catch(error => {\n          console.error('Error updating action tracker:', error);\n        })\n        .finally(() => {\n          this.isLoading = false;\n        });\n    },\n\n    selectPQEFromQEDashboard(pqeOwner) {\n      console.log(`Selecting PQE owner from QE Dashboard: ${pqeOwner}`);\n\n      // Set the selected PQE owner\n      this.selectedPQEOwner = pqeOwner;\n\n      // Switch to the PQE Owner Dashboard tab\n      // Find the tab element and click it\n      this.$nextTick(() => {\n        const tabElement = document.getElementById('pqe-owner-tab');\n        if (tabElement) {\n          tabElement.click();\n        }\n      });\n    },\n\n    navigateToActionTracker(alertData) {\n      console.log('Navigating to Action Tracker with alert data:', alertData);\n\n      // Navigate to the Action Tracker page\n      // You can use Vue Router to navigate to the action tracker page\n      this.$router.push({\n        name: 'ActionTracker', // Assuming you have a route named 'ActionTracker'\n        query: {\n          alertId: alertData.alertId,\n          actionTrackerId: alertData.actionTrackerId,\n          tab: alertData.tab || 'alerts',\n          pqeOwner: this.selectedPQEOwner\n        }\n      });\n    },\n\n    getAuthConfig() {\n      // Get authentication token from localStorage or Vuex store\n      const token = localStorage.getItem('token') || this.$store.state.token;\n\n      return {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      };\n    }\n  }\n};\n</script>\n\n<style scoped>\n.dashboard-container {\n  min-height: 100vh;\n  background-color: #161616;\n}\n\n.main-content {\n  padding: 2rem;\n  max-width: 1400px;\n  margin: 0 auto;\n}\n\n.page-header {\n  margin-bottom: 2rem;\n}\n\n.page-title {\n  color: #f4f4f4;\n  font-size: 1.75rem;\n  font-weight: 400;\n  margin: 0;\n}\n\n.dashboard-tabs {\n  background-color: #262626;\n  border-radius: 8px;\n  overflow: hidden;\n  border: 1px solid #333333;\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);\n}\n\n.tab-content {\n  padding: 1.5rem;\n}\n\n.pqe-selection-container {\n  display: flex;\n  align-items: center;\n  margin-bottom: 1.5rem;\n  background-color: #333333;\n  padding: 1rem;\n  border-radius: 8px;\n}\n\n.selection-label {\n  color: #f4f4f4;\n  margin-right: 1rem;\n  font-size: 0.875rem;\n  white-space: nowrap;\n}\n\n.pqe-dropdown {\n  width: 300px;\n}\n\n.select-pqe-message {\n  color: #8d8d8d;\n  font-size: 1rem;\n  text-align: center;\n  padding: 2rem;\n  background-color: #333333;\n  border-radius: 8px;\n}\n</style>\n"]}]}