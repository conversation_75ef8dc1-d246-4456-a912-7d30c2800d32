{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\DefectValidations\\DefectValidations.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\DefectValidations\\DefectValidations.vue", "mtime": 1748973026822}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["DefectValidations.vue"], "names": [], "mappings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file": "DefectValidations.vue", "sourceRoot": "src/views/DefectValidations", "sourcesContent": ["<template>\r\n  <div class=\"validations-container\">\r\n    <!-- Inherit the MainHeader component -->\r\n    <MainHeader :expandedSideNav=\"expandedSideNav\" :useFixed=\"useFixed\" />\r\n\r\n    <!-- Page Header -->\r\n    <div class=\"page-header\">\r\n      <h1 class=\"page-title\">Defect Validations</h1>\r\n      <div class=\"header-actions\">\r\n        <cv-button kind=\"primary\" @click=\"refreshData\">Refresh Data</cv-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Filter Bar -->\r\n    <div class=\"filter-bar\">\r\n      <div class=\"filter-group\">\r\n        <div class=\"filter-label\">PQE Owner:</div>\r\n        <cv-dropdown\r\n          v-model=\"selectedPQEOwner\"\r\n          label=\"Filter by PQE Owner\"\r\n          :items=\"pqeOwnerOptions\"\r\n          @change=\"handlePQEOwnerChange\"\r\n        />\r\n      </div>\r\n\r\n      <div class=\"filter-group\">\r\n        <div class=\"filter-label\">Process/Commodity:</div>\r\n        <cv-dropdown\r\n          v-model=\"selectedProcess\"\r\n          label=\"Filter by process\"\r\n          :items=\"processOptions\"\r\n          @change=\"loadValidationData\"\r\n        ></cv-dropdown>\r\n      </div>\r\n\r\n      <div class=\"filter-group\">\r\n        <div class=\"filter-label\">Time Period:</div>\r\n        <cv-dropdown\r\n          v-model=\"selectedTimeRange\"\r\n          label=\"Filter by time period\"\r\n          :items=\"rangeOptions\"\r\n          @change=\"loadValidationData\"\r\n        ></cv-dropdown>\r\n      </div>\r\n\r\n      <div class=\"search-box\">\r\n        <cv-search\r\n          v-model=\"searchQuery\"\r\n          label=\"Search\"\r\n          placeholder=\"Search by part number or group...\"\r\n          @input=\"filterValidations\"\r\n        ></cv-search>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Loading State -->\r\n    <div v-if=\"isLoading\" class=\"loading-container\">\r\n      <cv-loading :active=\"true\" :small=\"false\" :withOverlay=\"false\" />\r\n      <p class=\"loading-text\">Loading validation data...</p>\r\n    </div>\r\n\r\n    <!-- Error State -->\r\n    <div v-else-if=\"loadingError\" class=\"error-container\">\r\n      <cv-inline-notification\r\n        kind=\"error\"\r\n        :title=\"'Error'\"\r\n        :sub-title=\"loadingError\"\r\n      />\r\n    </div>\r\n\r\n    <!-- Content when data is loaded -->\r\n    <div v-else class=\"validations-content\">\r\n      <!-- Summary Tiles -->\r\n      <div class=\"validations-summary\">\r\n\r\n\r\n        <cv-tile class=\"summary-tile\">\r\n          <h4 class=\"tile-title\">Validated</h4>\r\n          <p class=\"tile-value validated\">{{ validationsSummary.validated }}</p>\r\n          <p class=\"tile-percentage\">{{ validationsSummary.validatedPercentage }}%</p>\r\n        </cv-tile>\r\n\r\n        <cv-tile class=\"summary-tile\">\r\n          <h4 class=\"tile-title\">Unvalidated</h4>\r\n          <p class=\"tile-value unvalidated\">{{ validationsSummary.unvalidated }}</p>\r\n          <p class=\"tile-percentage\">{{ validationsSummary.unvalidatedPercentage }}%</p>\r\n        </cv-tile>\r\n      </div>\r\n\r\n      <!-- Main Content -->\r\n      <div class=\"main-validation-content\">\r\n        <!-- PQE Validation Results -->\r\n        <div class=\"validation-section\" v-if=\"selectedPQEOwner && selectedPQEOwner !== 'All'\">\r\n          <h3>Validation Results - {{ selectedPQEOwner }}</h3>\r\n\r\n          <!-- Validation Counts -->\r\n          <div class=\"validation-counts\">\r\n            <div class=\"count-item validated\">\r\n              <div class=\"count-number\">{{ pqeValidationData.validated }}</div>\r\n              <div class=\"count-label\">Validated Defects</div>\r\n            </div>\r\n            <div class=\"count-item unvalidated\">\r\n              <div class=\"count-number\">{{ pqeValidationData.unvalidated }}</div>\r\n              <div class=\"count-label\">Unvalidated Defects</div>\r\n            </div>\r\n            <div class=\"count-item total\">\r\n              <div class=\"count-number\">{{ pqeValidationData.total }}</div>\r\n              <div class=\"count-label\">Total Defects</div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Part Numbers/Groupings -->\r\n          <div class=\"part-numbers-section\" v-if=\"pqePartNumbers.length > 0\">\r\n            <h4>Associated Part Numbers ({{ pqePartNumbers.length }})</h4>\r\n            <div class=\"part-numbers-list\">\r\n              <cv-tag\r\n                v-for=\"pn in pqePartNumbers\"\r\n                :key=\"pn\"\r\n                kind=\"gray\"\r\n                class=\"part-number-tag\"\r\n              >\r\n                {{ pn }}\r\n              </cv-tag>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Breakout Groups -->\r\n          <div class=\"breakout-groups-section\" v-if=\"pqeBreakoutGroups.length > 0\">\r\n            <h4>Associated Breakout Groups ({{ pqeBreakoutGroups.length }})</h4>\r\n            <div class=\"breakout-groups-list\">\r\n              <cv-tag\r\n                v-for=\"group in pqeBreakoutGroups\"\r\n                :key=\"group\"\r\n                kind=\"blue\"\r\n                class=\"breakout-group-tag\"\r\n              >\r\n                {{ group }}\r\n              </cv-tag>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Default message when no PQE selected -->\r\n        <div class=\"validation-section\" v-else>\r\n          <h3>Defect Validation</h3>\r\n          <div class=\"empty-state\">\r\n            <p>Please select a PQE Owner to view validation data.</p>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Legacy View Section -->\r\n        <div class=\"validation-section legacy-view\">\r\n          <h3>Legacy Validation View</h3>\r\n          <cv-content-switcher aria-label=\"Choose content\" @selected='reset()'>\r\n            <cv-content-switcher-button content-selector=\".content-1\" :selected=\"selectedIndex === 0\">Cables</cv-content-switcher-button>\r\n            <cv-content-switcher-button content-selector=\".content-2\" :selected=\"selectedIndex === 1\">Power/Thermal</cv-content-switcher-button>\r\n          </cv-content-switcher>\r\n\r\n          <div class=\"progress-section content-1\">\r\n            <cv-progress>\r\n              <cv-progress-step\r\n                v-for=\"(step, index) in cableStepsStatus\"\r\n                :key=\"index\"\r\n                :label=\"step.label\"\r\n                :complete=\"step.complete\"\r\n                @step-clicked=\"stepClick(step.pn, step.label)\"\r\n              ></cv-progress-step>\r\n            </cv-progress>\r\n          </div>\r\n\r\n          <div class=\"progress-section content-2\">\r\n            <cv-progress>\r\n              <cv-progress-step\r\n                v-for=\"(step, index) in powerThermalSteps\"\r\n                :key=\"index\"\r\n                :label=\"step.label\"\r\n                :complete=\"step.complete\"\r\n                @step-clicked=\"stepClick(step.pn, step.label)\"\r\n              ></cv-progress-step>\r\n            </cv-progress>\r\n          </div>\r\n\r\n          <h3>{{ clickedStepName }}</h3>\r\n\r\n          <div v-if=\"stepClicked\">\r\n            <cv-dropdown\r\n              label=\"Range of Fails\"\r\n              v-model=\"selectedRange\"\r\n              :items=\"rangeOptions\"\r\n              :selected-item=\"selectedRange\"\r\n            ></cv-dropdown>\r\n\r\n            <GaugeChart v-if=\"gaugeActive\" :data=\"gaugeData\" />\r\n\r\n            <div class=\"fail-container\">\r\n              <p><strong>Unvalidated count:</strong> {{ unvalidated_count }}</p>\r\n              <p><strong>Total number of Fails:</strong> {{ total_fails }}</p>\r\n\r\n              <!-- Buttons under the bar chart -->\r\n              <div class=\"button-container\">\r\n                <cv-button @click=\"viewData\">View Data</cv-button>\r\n                <cv-button @click=\"validateEach\">Validate Each</cv-button>\r\n                <cv-button @click=\"validateBulk\">Validate Bulk</cv-button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Validation Details Modal -->\r\n    <cv-modal\r\n      class=\"validation-details-modal\"\r\n      :visible=\"detailsModalVisible\"\r\n      @modal-hidden=\"detailsModalVisible = false\"\r\n      :size=\"'lg'\"\r\n    >\r\n      <template slot=\"title\">\r\n        <div>{{ selectedItem ? (selectedItem.partNumber ? 'Part Number: ' + selectedItem.partNumber : 'Group: ' + selectedItem.name) : 'Validation Details' }}</div>\r\n      </template>\r\n      <template slot=\"content\">\r\n        <div class=\"modal-content\" v-if=\"selectedItem\">\r\n          <!-- Status Banner -->\r\n          <div class=\"status-banner status-banner-in-progress\">\r\n            <div class=\"validation-summary\">\r\n              <span class=\"validation-label\">Validation Rate:</span>\r\n              <cv-tag :kind=\"getValidationTagKind(selectedItem.percentage)\">\r\n                {{ selectedItem.percentage }}%\r\n              </cv-tag>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Validation Details -->\r\n          <div class=\"modal-section\">\r\n            <div class=\"section-title\">Validation Summary</div>\r\n            <div class=\"info-grid\">\r\n              <div class=\"info-item\">\r\n                <span class=\"info-label\">Total Validations</span>\r\n                <span class=\"info-value\">{{ selectedItem.total }}</span>\r\n              </div>\r\n              <div class=\"info-item\">\r\n                <span class=\"info-label\">Validated</span>\r\n                <span class=\"info-value\">{{ selectedItem.validated }}</span>\r\n              </div>\r\n              <div class=\"info-item\">\r\n                <span class=\"info-label\">Unvalidated</span>\r\n                <span class=\"info-value\">{{ selectedItem.unvalidated }}</span>\r\n              </div>\r\n              <div class=\"info-item\">\r\n                <span class=\"info-label\">Time Period</span>\r\n                <span class=\"info-value\">{{ selectedTimeRange }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Validation Items Table -->\r\n          <div class=\"modal-section\">\r\n            <div class=\"section-title\">Validation Items</div>\r\n            <cv-data-table\r\n              :columns=\"validationDetailsColumns\"\r\n              :pagination=\"{ pageSize: 10 }\"\r\n              :title=\"''\"\r\n            >\r\n              <template slot=\"data\">\r\n                <cv-data-table-row\r\n                  v-for=\"(item, index) in selectedItemDetails\"\r\n                  :key=\"index\"\r\n                >\r\n                  <cv-data-table-cell>{{ item.defect_id }}</cv-data-table-cell>\r\n                  <cv-data-table-cell>{{ item.pn }}</cv-data-table-cell>\r\n                  <cv-data-table-cell>{{ item.sn }}</cv-data-table-cell>\r\n                  <cv-data-table-cell>{{ formatDate(item.date) }}</cv-data-table-cell>\r\n                  <cv-data-table-cell>\r\n                    <cv-tag :kind=\"item.status === 'validated' ? 'green' : 'red'\">\r\n                      {{ item.status === 'validated' ? 'Validated' : 'Unvalidated' }}\r\n                    </cv-tag>\r\n                  </cv-data-table-cell>\r\n                  <cv-data-table-cell>{{ item.root_cause_1 || 'N/A' }}</cv-data-table-cell>\r\n                  <cv-data-table-cell>\r\n                    <cv-button\r\n                      kind=\"ghost\"\r\n                      size=\"small\"\r\n                      @click=\"validateItem(item)\"\r\n                      v-if=\"item.status !== 'validated'\"\r\n                    >\r\n                      Validate\r\n                    </cv-button>\r\n                    <span v-else>-</span>\r\n                  </cv-data-table-cell>\r\n                </cv-data-table-row>\r\n\r\n                <!-- Empty state -->\r\n                <cv-data-table-row v-if=\"selectedItemDetails.length === 0\">\r\n                  <cv-data-table-cell colspan=\"7\" class=\"empty-message\">\r\n                    No validation items found.\r\n                  </cv-data-table-cell>\r\n                </cv-data-table-row>\r\n              </template>\r\n            </cv-data-table>\r\n          </div>\r\n\r\n          <!-- Bulk Validation Section -->\r\n          <div class=\"modal-section\" v-if=\"hasUnvalidatedItems\">\r\n            <div class=\"section-title\">Bulk Validation</div>\r\n            <div class=\"bulk-validation-form\">\r\n              <div class=\"form-group\">\r\n                <label class=\"form-label\">Root Cause</label>\r\n                <cv-dropdown\r\n                  v-model=\"bulkValidationRootCause\"\r\n                  label=\"Select Root Cause\"\r\n                  :items=\"rootCauseOptions\"\r\n                ></cv-dropdown>\r\n              </div>\r\n\r\n              <div class=\"form-group\">\r\n                <label class=\"form-label\">Comments</label>\r\n                <cv-text-area\r\n                  v-model=\"bulkValidationComments\"\r\n                  label=\"Comments\"\r\n                  placeholder=\"Enter validation comments\"\r\n                ></cv-text-area>\r\n              </div>\r\n\r\n              <div class=\"form-actions\">\r\n                <cv-button\r\n                  kind=\"primary\"\r\n                  @click=\"validateAllItems\"\r\n                >\r\n                  Validate All Unvalidated Items\r\n                </cv-button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Modal Actions -->\r\n          <div class=\"modal-actions\">\r\n            <cv-button kind=\"secondary\" @click=\"detailsModalVisible = false\">Close</cv-button>\r\n            <cv-button kind=\"primary\" @click=\"refreshItemDetails\">Refresh</cv-button>\r\n          </div>\r\n        </div>\r\n      </template>\r\n    </cv-modal>\r\n  </div>\r\n</template>\r\n\r\n\r\n<script>\r\nimport GaugeChart from '../../components/GaugeChart';\r\nimport MainHeader from '@/components/MainHeader'; // Import the MainHeader component\r\n\r\nexport default {\r\n  name: 'DefectValidations',\r\n  components: {\r\n    GaugeChart,\r\n    MainHeader\r\n  },\r\n\r\n  data() {\r\n    return {\r\n      // PQE Owner data\r\n      selectedPQEOwner: 'All',\r\n      pqeOwners: [],\r\n      pqeOwnerOptions: [{ label: 'All PQE Owners', value: 'All' }],\r\n\r\n      // PQE Validation data\r\n      pqeValidationData: {\r\n        total: 0,\r\n        validated: 0,\r\n        unvalidated: 0,\r\n        percentage: 0\r\n      },\r\n      pqePartNumbers: [],\r\n      pqeBreakoutGroups: [],\r\n\r\n      // Legacy view data\r\n      selectedIndex: 0, // Track the selected index\r\n      stepClicked: false, // Track whether a step is clicked\r\n      clickedStepName: '', // Track the clicked step's name\r\n      selectedRange: '', // Default range of fails\r\n\r\n      numberOfFails: 0, // Default number of fails\r\n      cableStepsStatus: [\r\n        { label: \"SMP9\", pn: \"02EA657\", complete: true },\r\n        { label: \"Signal\", pn: \"03FM185\", complete: false }, //signal\r\n        { label: \"CDFP\", pn: \"02EC799\", complete: true }, //cdfp\r\n      ],\r\n\r\n      powerThermalSteps: [\r\n        { label: \"Fans\", pn: \"02ED368\", complete: true },\r\n        { label: \"PSU\", pn: \"03KP588\", complete: false }, //signal\r\n        { label: \"PDU\", pn: \"03JG497\", complete: true }, //cdfp\r\n      ],\r\n      gaugeActive: false,\r\n      gaugeData: [\r\n        {\r\n          group: 'value',\r\n          value: 0\r\n        }\r\n      ], // Data for the gauge chart (number of validations left)\r\n      unvalidated_fails: [],\r\n      validated_fails: [],\r\n      unvalidated_count: 0,\r\n      validated_count: 0,\r\n      total_fails: 0,\r\n      perc_val: 0,\r\n      selectedPN: \"\",\r\n\r\n      // New UI data\r\n      isLoading: false,\r\n      loadingError: null,\r\n      searchQuery: '',\r\n      selectedProcess: 'All',\r\n      processOptions: ['All', 'FUL', 'FAB', 'Power', 'Cable', 'Memory'],\r\n      selectedTimeRange: 'Last 3 Months',\r\n      rangeOptions: [\"Last Month\", \"Last 3 Months\", \"Last 6 Months\", \"Last Year\", \"All Time\"],\r\n\r\n      // Validation data\r\n      validationColumns: ['Part Number', 'Group', 'Total', 'Validated', 'Unvalidated', 'Validation %', 'Actions'],\r\n      validationDetailsColumns: ['Defect ID', 'Part Number', 'Serial Number', 'Date', 'Status', 'Root Cause', 'Actions'],\r\n      validationsByGroup: [],\r\n      validationsByPart: [],\r\n\r\n      // Modal data\r\n      detailsModalVisible: false,\r\n      selectedItem: null,\r\n      selectedItemDetails: [],\r\n      bulkValidationRootCause: '',\r\n      bulkValidationComments: '',\r\n      rootCauseOptions: ['Design Issue', 'Manufacturing Defect', 'Material Issue', 'Test Error', 'Handling Damage', 'Unknown'],\r\n\r\n      // Summary data\r\n      validationsSummary: {\r\n        total: 0,\r\n        validated: 0,\r\n        unvalidated: 0,\r\n        validatedPercentage: 0,\r\n        unvalidatedPercentage: 0\r\n      },\r\n\r\n      // Date tracking\r\n      currentDate: '',\r\n      selectedMonthDate: '',\r\n      selectedWeekDate: '',\r\n      expandedSideNav: false,\r\n      useFixed: true\r\n    };\r\n  },\r\n\r\n  computed: {\r\n    hasUnvalidatedItems() {\r\n      return this.selectedItemDetails.some(item => item.status !== 'validated');\r\n    }\r\n  },\r\n\r\n  watch: {\r\n    selectedRange(newRange) {\r\n      if (newRange) {\r\n        this.gaugeActive = false;\r\n        this.get_unval();\r\n      }\r\n    }\r\n  },\r\n\r\n  mounted() {\r\n    this.get_dates();\r\n    this.loadPQEOwners();\r\n  },\r\n\r\n  methods: {\r\n    // PQE Owner methods\r\n    async loadPQEOwners() {\r\n      try {\r\n        const config = this.getAuthConfig();\r\n        const response = await fetch('/api-statit2/get_pqe_owners', {\r\n          method: 'POST',\r\n          headers: {\r\n            'Content-Type': 'application/json',\r\n            ...config.headers\r\n          },\r\n          body: JSON.stringify({})\r\n        });\r\n\r\n        if (!response.ok) {\r\n          throw new Error(`Failed to fetch PQE owners: ${response.status} ${response.statusText}`);\r\n        }\r\n\r\n        const data = await response.json();\r\n        if (data.status_res === 'success') {\r\n          this.pqeOwners = data.pqe_owners || [];\r\n\r\n          // Update dropdown options\r\n          this.pqeOwnerOptions = [\r\n            { label: 'All PQE Owners', value: 'All' },\r\n            ...this.pqeOwners.map(owner => ({ label: owner, value: owner }))\r\n          ];\r\n\r\n          console.log(`Loaded ${this.pqeOwners.length} PQE owners`);\r\n        } else {\r\n          console.error('Failed to load PQE owners:', data.message);\r\n        }\r\n      } catch (error) {\r\n        console.error('Error loading PQE owners:', error);\r\n      }\r\n    },\r\n\r\n    // Get authentication config\r\n    getAuthConfig() {\r\n      return {\r\n        headers: {\r\n          'Authorization': 'Bearer ' + (localStorage.getItem('token') || ''),\r\n          'X-User-ID': localStorage.getItem('userId') || ''\r\n        }\r\n      };\r\n    },\r\n\r\n    // Handle PQE owner change\r\n    async handlePQEOwnerChange() {\r\n      console.log(`Selected PQE owner: ${this.selectedPQEOwner}`);\r\n\r\n      if (this.selectedPQEOwner && this.selectedPQEOwner !== 'All') {\r\n        await this.loadPQEValidationData();\r\n      } else {\r\n        // Reset PQE data when \"All\" is selected\r\n        this.pqeValidationData = {\r\n          total: 0,\r\n          validated: 0,\r\n          unvalidated: 0,\r\n          percentage: 0\r\n        };\r\n        this.pqePartNumbers = [];\r\n        this.pqeBreakoutGroups = [];\r\n      }\r\n\r\n      this.updateValidationsSummary();\r\n    },\r\n\r\n    // Update validations summary\r\n    updateValidationsSummary() {\r\n      // Use PQE validation data if a specific PQE is selected\r\n      if (this.selectedPQEOwner && this.selectedPQEOwner !== 'All') {\r\n        this.validationsSummary = {\r\n          total: this.pqeValidationData.total,\r\n          validated: this.pqeValidationData.validated,\r\n          unvalidated: this.pqeValidationData.unvalidated,\r\n          validatedPercentage: this.pqeValidationData.percentage,\r\n          unvalidatedPercentage: this.pqeValidationData.total > 0 ? 100 - this.pqeValidationData.percentage : 0\r\n        };\r\n      } else {\r\n        // Reset summary when no specific PQE is selected\r\n        this.validationsSummary = {\r\n          total: 0,\r\n          validated: 0,\r\n          unvalidated: 0,\r\n          validatedPercentage: 0,\r\n          unvalidatedPercentage: 0\r\n        };\r\n      }\r\n    },\r\n\r\n    // Load PQE validation data\r\n    async loadPQEValidationData() {\r\n      if (!this.selectedPQEOwner || this.selectedPQEOwner === 'All') {\r\n        return;\r\n      }\r\n\r\n      try {\r\n        console.log(`Loading validation data for PQE owner: ${this.selectedPQEOwner}`);\r\n\r\n        // Get part numbers and breakout groups for this PQE owner\r\n        const pqeData = await this.getPQEData(this.selectedPQEOwner);\r\n        this.pqePartNumbers = pqeData.partNumbers;\r\n        this.pqeBreakoutGroups = pqeData.breakoutGroups;\r\n\r\n        if (this.pqePartNumbers.length === 0) {\r\n          console.warn(`No part numbers found for PQE owner: ${this.selectedPQEOwner}`);\r\n          this.pqeValidationData = {\r\n            total: 0,\r\n            validated: 0,\r\n            unvalidated: 0,\r\n            percentage: 0\r\n          };\r\n          return;\r\n        }\r\n\r\n        // Query validation data for these part numbers\r\n        let totalValidated = 0;\r\n        let totalUnvalidated = 0;\r\n\r\n        const user_type = this.$store.getters.getUser_type;\r\n        const token = this.$store.getters.getToken;\r\n        const action = \"view\";\r\n        const startdate = this.getStartDateFromRange(this.selectedTimeRange);\r\n\r\n        // Process each part number to get validation counts\r\n        for (const pn of this.pqePartNumbers) {\r\n          try {\r\n            const response = await fetch(process.env.VUE_APP_API_PATH + \"get_unval\", {\r\n              method: \"POST\",\r\n              headers: {\r\n                \"Content-Type\": \"application/json\",\r\n                Authorization: \"Bearer \" + token,\r\n              },\r\n              body: JSON.stringify({ \"PN\": pn, \"SD\": startdate, \"ED\": this.currentDate, user_type, action }),\r\n            });\r\n\r\n            if (response.ok) {\r\n              const data = await this.handleResponse(response);\r\n              if (data && data.status_res === \"success\") {\r\n                totalValidated += data.validated_count || 0;\r\n                totalUnvalidated += data.unvalidated_count || 0;\r\n              }\r\n            }\r\n          } catch (error) {\r\n            console.error(`Error fetching validation data for part ${pn}:`, error);\r\n          }\r\n        }\r\n\r\n        // Update PQE validation data\r\n        const totalFails = totalValidated + totalUnvalidated;\r\n        const percentage = totalFails > 0 ? Math.round((totalValidated / totalFails) * 100) : 0;\r\n\r\n        this.pqeValidationData = {\r\n          total: totalFails,\r\n          validated: totalValidated,\r\n          unvalidated: totalUnvalidated,\r\n          percentage: percentage\r\n        };\r\n\r\n        console.log(`PQE ${this.selectedPQEOwner} validation data:`, this.pqeValidationData);\r\n        console.log(`Part numbers (${this.pqePartNumbers.length}):`, this.pqePartNumbers);\r\n        console.log(`Breakout groups (${this.pqeBreakoutGroups.length}):`, this.pqeBreakoutGroups);\r\n\r\n      } catch (error) {\r\n        console.error(`Error loading PQE validation data:`, error);\r\n      }\r\n    },\r\n\r\n    // Get part numbers and breakout groups for a PQE owner\r\n    async getPQEData(pqeOwner) {\r\n      try {\r\n        // Use the existing PQE dashboard API to get breakout groups\r\n        const config = this.getAuthConfig();\r\n        const response = await fetch('/api-statit2/get_pqe_breakout_groups', {\r\n          method: 'POST',\r\n          headers: {\r\n            'Content-Type': 'application/json',\r\n            ...config.headers\r\n          },\r\n          body: JSON.stringify({ pqeOwner })\r\n        });\r\n\r\n        if (!response.ok) {\r\n          throw new Error(`Failed to fetch PQE data: ${response.status} ${response.statusText}`);\r\n        }\r\n\r\n        const data = await response.json();\r\n        if (data.status_res === 'success') {\r\n          const breakoutGroups = data.breakout_groups || [];\r\n\r\n          // Now get part numbers for these breakout groups\r\n          const partNumbers = await this.getPartNumbersForBreakoutGroups(breakoutGroups);\r\n\r\n          console.log(`Found ${breakoutGroups.length} breakout groups and ${partNumbers.length} part numbers for PQE owner ${pqeOwner}`);\r\n\r\n          return {\r\n            breakoutGroups: breakoutGroups,\r\n            partNumbers: partNumbers\r\n          };\r\n        } else {\r\n          console.error('Failed to get PQE data:', data.message);\r\n          return { breakoutGroups: [], partNumbers: [] };\r\n        }\r\n      } catch (error) {\r\n        console.error(`Error getting PQE data for ${pqeOwner}:`, error);\r\n        return { breakoutGroups: [], partNumbers: [] };\r\n      }\r\n    },\r\n\r\n    // Get part numbers for breakout groups\r\n    async getPartNumbersForBreakoutGroups(breakoutGroups) {\r\n      try {\r\n        if (!breakoutGroups || breakoutGroups.length === 0) {\r\n          return [];\r\n        }\r\n\r\n        const config = this.getAuthConfig();\r\n        const response = await fetch('/api-statit2/get_pqe_part_numbers', {\r\n          method: 'POST',\r\n          headers: {\r\n            'Content-Type': 'application/json',\r\n            ...config.headers\r\n          },\r\n          body: JSON.stringify({ breakoutGroups })\r\n        });\r\n\r\n        if (!response.ok) {\r\n          throw new Error(`Failed to fetch part numbers: ${response.status} ${response.statusText}`);\r\n        }\r\n\r\n        const data = await response.json();\r\n        if (data.status === 'success') {\r\n          return data.partNumbers || [];\r\n        } else {\r\n          console.error('Failed to get part numbers:', data.message);\r\n          return [];\r\n        }\r\n      } catch (error) {\r\n        console.error(`Error getting part numbers for breakout groups:`, error);\r\n        return [];\r\n      }\r\n    },\r\n\r\n    // Legacy methods\r\n    async get_unval() {\r\n      try {\r\n        let user_type = this.$store.getters.getUser_type;\r\n        let action = \"view\";\r\n        let startdate = \"\"\r\n        let token = this.$store.getters.getToken;\r\n        console.log(\"TOKEN\", token)\r\n        if (this.selectedRange === \"Past Month\"){\r\n          startdate = this.selectedMonthDate\r\n        } else if (this.selectedRange === \"Past Week\"){\r\n          startdate = this.selectedWeekDate\r\n        }\r\n\r\n        // Fetch data from the API\r\n        const response = await fetch(process.env.VUE_APP_API_PATH + \"get_unval\", {\r\n          method: \"POST\",\r\n          headers: {\r\n            \"Content-Type\": \"application/json\",\r\n            Authorization: \"Bearer \" + token,\r\n          },\r\n          body: JSON.stringify({ \"PN\": this.selectedPN, \"SD\": startdate, \"ED\": this.currentDate, user_type, action }),\r\n        });\r\n\r\n        if (response.status === 401) {\r\n          console.error(\"Unauthorized: Check your token or credentials.\");\r\n        }\r\n\r\n        const data = await this.handleResponse(response);\r\n\r\n        if (data.status_res === \"success\") {\r\n          this.unvalidated_fails = data.unvalidated_fails\r\n          this.validated_fails = data.validated_fails\r\n          this.unvalidated_count = data.unvalidated_count\r\n          this.validated_count = data.validated_count\r\n          this.total_fails = data.total_fails\r\n          this.perc_val = data.perc_val\r\n\r\n          console.log(\"Received data:\", data);\r\n          if(this.perc_val === null){\r\n            this.total_fails = \"No entries\"\r\n          }else{\r\n            this.gaugeActive = true;\r\n          }\r\n\r\n          this.gaugeData = [\r\n            {\r\n              group: 'value',\r\n              value: data.perc_val\r\n            }\r\n          ];\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error loading data:\", error);\r\n      }\r\n    },\r\n\r\n    get_dates() {\r\n      const currentDate = new Date();\r\n      const previousMonthDate = new Date();\r\n      const previousWeekDate = new Date();\r\n\r\n      previousMonthDate.setMonth(currentDate.getMonth() - 1);\r\n      // Subtract 7 days from the current date\r\n      previousWeekDate.setDate(currentDate.getDate() - 7);\r\n\r\n      // Format the dates (e.g., YYYY-MM-DD)\r\n      const formatDate = (date) => {\r\n        const year = date.getFullYear();\r\n        const month = String(date.getMonth() + 1).padStart(2, '0'); // Months are 0-indexed\r\n        const day = String(date.getDate()).padStart(2, '0');\r\n        return `${year}-${month}-${day}`;\r\n      };\r\n\r\n      // Create the selectedRange string\r\n      this.selectedWeekDate = formatDate(previousWeekDate)\r\n      this.selectedMonthDate = formatDate(previousMonthDate)\r\n      this.currentDate = formatDate(currentDate)\r\n    },\r\n\r\n    stepClick(pn, label) {\r\n      this.gaugeActive = false;\r\n      this.stepClicked = true; // Show the clicked info section\r\n      this.selectedPN = pn;\r\n      this.get_unval();\r\n      this.clickedStepName = `${pn} - ${label}`; // Update the clicked step's name\r\n    },\r\n\r\n    validateEach() {\r\n      console.log('Validating each for:', this.selectedPN);\r\n      // Implement logic for validating each item here\r\n    },\r\n\r\n    validateBulk() {\r\n      console.log('Validating bulk for:', this.selectedPN);\r\n      // Implement logic for bulk validation here\r\n    },\r\n\r\n    handleResponse(response) {\r\n      if (!response.ok) {\r\n        if (response.status === 401) {\r\n          this.session_expired_visible = true;\r\n        }\r\n      } else {\r\n        return response.json();\r\n      }\r\n    },\r\n\r\n    reset() {\r\n      this.stepClicked = false; // Reset the step clicked status\r\n      this.clickedStepName = 'Choose PN'; // Reset the clicked step name\r\n      this.selectedRange = 'Monthly'; // Reset the selected range\r\n      this.gaugeActive = false; // Hide the gauge chart\r\n      this.selectedPN = ''; // Reset the selected part number\r\n      // Any other reset logic can go here\r\n      console.log('Content switcher reset');\r\n    },\r\n\r\n    // Utility methods\r\n\r\n    async getPartNumbersForProcess(process) {\r\n      try {\r\n        let user_type = this.$store.getters.getUser_type;\r\n        let token = this.$store.getters.getToken;\r\n\r\n        // If \"All\" is selected, get all Metis part numbers\r\n        if (process === 'All') {\r\n          const response = await fetch(process.env.VUE_APP_API_PATH + \"get_metis_part_numbers\", {\r\n            method: \"POST\",\r\n            headers: {\r\n              \"Content-Type\": \"application/json\",\r\n              Authorization: \"Bearer \" + token,\r\n            },\r\n            body: JSON.stringify({ user_type }),\r\n          });\r\n\r\n          const data = await this.handleResponse(response);\r\n\r\n          if (data && data.status_res === \"success\" && data.pns) {\r\n            console.log(`Retrieved ${data.pns.length} part numbers from Metis file`);\r\n            return data.pns;\r\n          }\r\n        } else {\r\n          // For specific processes, use the commodity filter\r\n          const response = await fetch(process.env.VUE_APP_API_PATH + \"get_pns_from_excel\", {\r\n            method: \"POST\",\r\n            headers: {\r\n              \"Content-Type\": \"application/json\",\r\n              Authorization: \"Bearer \" + token,\r\n            },\r\n            body: JSON.stringify({ commodity: process, user_type }),\r\n          });\r\n\r\n          const data = await this.handleResponse(response);\r\n\r\n          if (data && data.status_res === \"success\" && data.pns) {\r\n            console.log(`Retrieved ${data.pns.length} part numbers for ${process} from commodity file`);\r\n            return data.pns;\r\n          }\r\n        }\r\n\r\n        // Fallback to hardcoded values if API fails\r\n        console.warn(\"Falling back to hardcoded part numbers\");\r\n        const fallbackParts = {\r\n          'All': [\"02EA657\", \"03FM185\", \"02EC799\", \"02ED368\", \"03KP588\", \"03JG497\", \"01KP123\", \"02FM456\", \"03EC789\"],\r\n          'FUL': [\"01KP123\", \"02FM456\", \"03EC789\"],\r\n          'FAB': [\"04KP321\", \"05FM654\", \"06EC987\"],\r\n          'Power': [\"02ED368\", \"03KP588\", \"03JG497\"],\r\n          'Cable': [\"02EA657\", \"03FM185\", \"02EC799\"],\r\n          'Memory': [\"07KP111\", \"08FM222\", \"09EC333\"]\r\n        };\r\n\r\n        return fallbackParts[process] || fallbackParts['All'];\r\n      } catch (error) {\r\n        console.error(\"Error fetching part numbers:\", error);\r\n        // Return fallback values in case of error\r\n        return [\"02EA657\", \"03FM185\", \"02EC799\"];\r\n      }\r\n    },\r\n\r\n    async getMetisGroupForPartNumber(partNumber) {\r\n      try {\r\n        let user_type = this.$store.getters.getUser_type;\r\n        let token = this.$store.getters.getToken;\r\n\r\n        // First try to get the Metis breakout names\r\n        const response = await fetch(process.env.VUE_APP_API_PATH + \"get_metis_breakout_names\", {\r\n          method: \"POST\",\r\n          headers: {\r\n            \"Content-Type\": \"application/json\",\r\n            Authorization: \"Bearer \" + token,\r\n          },\r\n          body: JSON.stringify({ user_type }),\r\n        });\r\n\r\n        const data = await this.handleResponse(response);\r\n\r\n        if (data && data.status_res === \"success\" && data.breakoutMap) {\r\n          // The API should return a mapping of part numbers to breakout names\r\n          const breakoutName = data.breakoutMap[partNumber];\r\n          if (breakoutName) {\r\n            return breakoutName;\r\n          }\r\n        }\r\n\r\n        // If we couldn't get the breakout name from the API, use a fallback mapping\r\n        // This is a simplified version of what would be in the Excel file\r\n        const fallbackGroupMap = {\r\n          \"02EA657\": \"SMP9 Cables\",\r\n          \"03FM185\": \"Signal Cables\",\r\n          \"02EC799\": \"CDFP Cables\",\r\n          \"02ED368\": \"Cooling Fans\",\r\n          \"03KP588\": \"Power Supply Units\",\r\n          \"03JG497\": \"Power Distribution\",\r\n          \"01KP123\": \"Memory Module A\",\r\n          \"02FM456\": \"Memory Module B\",\r\n          \"03EC789\": \"Memory Module C\",\r\n          \"04KP321\": \"Processor Module A\",\r\n          \"05FM654\": \"Processor Module B\",\r\n          \"06EC987\": \"Processor Module C\",\r\n          \"07KP111\": \"Storage Module A\",\r\n          \"08FM222\": \"Storage Module B\",\r\n          \"09EC333\": \"Storage Module C\"\r\n        };\r\n\r\n        return fallbackGroupMap[partNumber] || \"Unknown Group\";\r\n      } catch (error) {\r\n        console.error(\"Error fetching Metis group for part number:\", error);\r\n        // Return a generic group name in case of error\r\n        return \"Unknown Group\";\r\n      }\r\n    },\r\n\r\n    getStartDateFromRange(range) {\r\n      const now = new Date();\r\n      let startDate = new Date();\r\n\r\n      switch (range) {\r\n        case \"Last Month\":\r\n          startDate.setMonth(now.getMonth() - 1);\r\n          break;\r\n        case \"Last 3 Months\":\r\n          startDate.setMonth(now.getMonth() - 3);\r\n          break;\r\n        case \"Last 6 Months\":\r\n          startDate.setMonth(now.getMonth() - 6);\r\n          break;\r\n        case \"Last Year\":\r\n          startDate.setFullYear(now.getFullYear() - 1);\r\n          break;\r\n        case \"All Time\":\r\n          startDate = new Date(2000, 0, 1); // Far in the past\r\n          break;\r\n        default:\r\n          startDate.setMonth(now.getMonth() - 3); // Default to 3 months\r\n      }\r\n\r\n      // Format date as YYYY-MM-DD\r\n      return startDate.toISOString().split('T')[0];\r\n    },\r\n\r\n    filterValidations() {\r\n      // Filtering is handled by computed properties\r\n      console.log(\"Filtering with query:\", this.searchQuery);\r\n    },\r\n\r\n\r\n\r\n    async loadItemDetails(item) {\r\n      this.selectedItemDetails = [];\r\n\r\n      try {\r\n        let user_type = this.$store.getters.getUser_type;\r\n        let action = \"view\";\r\n        let startdate = this.getStartDateFromRange(this.selectedTimeRange);\r\n        let token = this.$store.getters.getToken;\r\n\r\n        console.log(`Loading details for ${item.partNumber ? 'part ' + item.partNumber : 'group ' + item.name}`);\r\n\r\n        // If it's a part number item\r\n        if (item.partNumber) {\r\n          const response = await fetch(process.env.VUE_APP_API_PATH + \"get_unval\", {\r\n            method: \"POST\",\r\n            headers: {\r\n              \"Content-Type\": \"application/json\",\r\n              Authorization: \"Bearer \" + token,\r\n            },\r\n            body: JSON.stringify({ \"PN\": item.partNumber, \"SD\": startdate, \"ED\": this.currentDate, user_type, action }),\r\n          });\r\n\r\n          const data = await this.handleResponse(response);\r\n\r\n          if (data && data.status_res === \"success\") {\r\n            // Combine validated and unvalidated fails\r\n            const validatedItems = (data.validated_fails || []).map(item => ({\r\n              ...item,\r\n              status: 'validated'\r\n            }));\r\n\r\n            const unvalidatedItems = (data.unvalidated_fails || []).map(item => ({\r\n              ...item,\r\n              status: 'unvalidated'\r\n            }));\r\n\r\n            this.selectedItemDetails = [...validatedItems, ...unvalidatedItems];\r\n            console.log(`Loaded ${this.selectedItemDetails.length} validation items for part ${item.partNumber}`);\r\n          }\r\n        }\r\n        // If it's a group item (Metis grouping)\r\n        else if (item.name) {\r\n          // First try to get part numbers for this Metis group from the API\r\n          let partNumbers = [];\r\n\r\n          try {\r\n            // Try to get part numbers for this Metis group from the API\r\n            const metisResponse = await fetch(process.env.VUE_APP_API_PATH + \"get_metis_part_numbers\", {\r\n              method: \"POST\",\r\n              headers: {\r\n                \"Content-Type\": \"application/json\",\r\n                Authorization: \"Bearer \" + token,\r\n              },\r\n              body: JSON.stringify({ breakoutName: item.name, user_type }),\r\n            });\r\n\r\n            const metisData = await this.handleResponse(metisResponse);\r\n\r\n            if (metisData && metisData.status_res === \"success\" && metisData.pns && metisData.pns.length > 0) {\r\n              partNumbers = metisData.pns;\r\n              console.log(`Retrieved ${partNumbers.length} part numbers for Metis group ${item.name} from API`);\r\n            }\r\n          } catch (error) {\r\n            console.error(`Error fetching part numbers for Metis group ${item.name}:`, error);\r\n          }\r\n\r\n          // If we couldn't get part numbers from the API, fall back to the local data\r\n          if (partNumbers.length === 0) {\r\n            partNumbers = this.validationsByPart\r\n              .filter(part => part.group === item.name)\r\n              .map(part => part.partNumber);\r\n\r\n            console.log(`Using ${partNumbers.length} part numbers for Metis group ${item.name} from local data`);\r\n          }\r\n\r\n          if (partNumbers.length === 0) {\r\n            console.warn(`No part numbers found for Metis group ${item.name}`);\r\n            return;\r\n          }\r\n\r\n          // Fetch details for each part number\r\n          let allDetails = [];\r\n\r\n          for (const pn of partNumbers) {\r\n            try {\r\n              const response = await fetch(process.env.VUE_APP_API_PATH + \"get_unval\", {\r\n                method: \"POST\",\r\n                headers: {\r\n                  \"Content-Type\": \"application/json\",\r\n                  Authorization: \"Bearer \" + token,\r\n                },\r\n                body: JSON.stringify({ \"PN\": pn, \"SD\": startdate, \"ED\": this.currentDate, user_type, action }),\r\n              });\r\n\r\n              const data = await this.handleResponse(response);\r\n\r\n              if (data && data.status_res === \"success\") {\r\n                // Combine validated and unvalidated fails\r\n                const validatedItems = (data.validated_fails || []).map(item => ({\r\n                  ...item,\r\n                  status: 'validated'\r\n                }));\r\n\r\n                const unvalidatedItems = (data.unvalidated_fails || []).map(item => ({\r\n                  ...item,\r\n                  status: 'unvalidated'\r\n                }));\r\n\r\n                allDetails = [...allDetails, ...validatedItems, ...unvalidatedItems];\r\n                console.log(`Added ${validatedItems.length + unvalidatedItems.length} items for part ${pn}`);\r\n              }\r\n            } catch (error) {\r\n              console.error(`Error fetching validation data for part ${pn}:`, error);\r\n              // Continue with other part numbers\r\n            }\r\n          }\r\n\r\n          this.selectedItemDetails = allDetails;\r\n          console.log(`Loaded ${this.selectedItemDetails.length} total validation items for group ${item.name}`);\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error loading item details:\", error);\r\n      }\r\n    },\r\n\r\n    refreshItemDetails() {\r\n      if (this.selectedItem) {\r\n        this.loadItemDetails(this.selectedItem);\r\n      }\r\n    },\r\n\r\n    validateItem(item) {\r\n      console.log(\"Validating item:\", item);\r\n      // This would typically call an API to validate the item\r\n      // For demo purposes, we'll just update the local state\r\n      item.status = 'validated';\r\n      item.root_cause_1 = this.bulkValidationRootCause || 'Manual Validation';\r\n\r\n      // Update counts\r\n      if (this.selectedItem) {\r\n        this.selectedItem.validated++;\r\n        this.selectedItem.unvalidated--;\r\n        this.selectedItem.percentage = Math.round((this.selectedItem.validated / this.selectedItem.total) * 100);\r\n      }\r\n    },\r\n\r\n    validateAllItems() {\r\n      if (!this.bulkValidationRootCause) {\r\n        alert(\"Please select a root cause before validating all items.\");\r\n        return;\r\n      }\r\n\r\n      // Find all unvalidated items\r\n      const unvalidatedItems = this.selectedItemDetails.filter(item => item.status !== 'validated');\r\n\r\n      // Validate each item\r\n      unvalidatedItems.forEach(item => {\r\n        item.status = 'validated';\r\n        item.root_cause_1 = this.bulkValidationRootCause;\r\n      });\r\n\r\n      // Update counts\r\n      if (this.selectedItem) {\r\n        this.selectedItem.validated = this.selectedItem.total;\r\n        this.selectedItem.unvalidated = 0;\r\n        this.selectedItem.percentage = 100;\r\n      }\r\n\r\n      // Clear form\r\n      this.bulkValidationRootCause = '';\r\n      this.bulkValidationComments = '';\r\n    },\r\n\r\n    getValidationTagKind(percentage) {\r\n      if (percentage >= 90) return 'green';\r\n      if (percentage >= 70) return 'teal';\r\n      if (percentage >= 50) return 'blue';\r\n      if (percentage >= 30) return 'purple';\r\n      if (percentage >= 10) return 'magenta';\r\n      return 'red';\r\n    },\r\n\r\n    formatDate(dateString) {\r\n      if (!dateString) return 'N/A';\r\n\r\n      // If it's already in a readable format, return as is\r\n      if (typeof dateString === 'string' && dateString.includes('/')) {\r\n        return dateString;\r\n      }\r\n\r\n      // Try to parse the date\r\n      try {\r\n        const date = new Date(dateString);\r\n        return date.toLocaleDateString();\r\n      } catch (e) {\r\n        return dateString;\r\n      }\r\n    },\r\n\r\n    viewData() {\r\n      console.log(\"View data clicked\");\r\n      // Implement view data functionality\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style scoped lang=\"scss\">\r\n@import \"../../styles/carbon-utils\";\r\n\r\n/* Main container */\r\n.validations-container {\r\n  min-height: 100vh;\r\n  background-color: #161616;\r\n  color: #f4f4f4;\r\n}\r\n\r\n/* Page header */\r\n.page-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 2rem;\r\n  padding: 2rem 2rem 1rem;\r\n  border-bottom: 1px solid #333333;\r\n}\r\n\r\n.page-title {\r\n  color: #f4f4f4;\r\n  font-size: 1.75rem;\r\n  font-weight: 400;\r\n  margin: 0;\r\n}\r\n\r\n.header-actions {\r\n  display: flex;\r\n  gap: 1rem;\r\n}\r\n\r\n/* Filter bar */\r\n.filter-bar {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin: 0 2rem 1.5rem;\r\n  background-color: #262626;\r\n  padding: 1rem;\r\n  border-radius: 8px;\r\n  border: 1px solid #333333;\r\n}\r\n\r\n.filter-group {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 1rem;\r\n}\r\n\r\n.filter-label {\r\n  color: #8d8d8d;\r\n  font-size: 0.875rem;\r\n}\r\n\r\n.search-box {\r\n  flex-grow: 1;\r\n  max-width: 300px;\r\n}\r\n\r\n/* Loading and error states */\r\n.loading-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 3rem;\r\n  margin: 2rem;\r\n  background-color: #262626;\r\n  border-radius: 8px;\r\n  border: 1px solid #333333;\r\n}\r\n\r\n.loading-text {\r\n  margin-top: 1rem;\r\n  color: #8d8d8d;\r\n}\r\n\r\n.error-container {\r\n  margin: 2rem;\r\n}\r\n\r\n/* Content area */\r\n.validations-content {\r\n  padding: 0 2rem 2rem;\r\n}\r\n\r\n.main-validation-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 2rem;\r\n}\r\n\r\n.validation-section {\r\n  background-color: #262626;\r\n  border: 1px solid #333333;\r\n  border-radius: 8px;\r\n  padding: 1.5rem;\r\n\r\n  h3 {\r\n    color: #f4f4f4;\r\n    font-size: 1.25rem;\r\n    font-weight: 600;\r\n    margin: 0 0 1.5rem 0;\r\n    padding-bottom: 0.75rem;\r\n    border-bottom: 1px solid #393939;\r\n  }\r\n}\r\n\r\n/* Validation counts styles */\r\n.validation-counts {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\r\n  gap: 1.5rem;\r\n  margin-bottom: 2rem;\r\n}\r\n\r\n.count-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  padding: 2rem 1rem;\r\n  background-color: #393939;\r\n  border-radius: 8px;\r\n  border: 1px solid #525252;\r\n  text-align: center;\r\n}\r\n\r\n.count-number {\r\n  font-size: 3rem;\r\n  font-weight: 700;\r\n  margin-bottom: 0.5rem;\r\n}\r\n\r\n.count-label {\r\n  font-size: 0.875rem;\r\n  color: #c6c6c6;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.16px;\r\n}\r\n\r\n.count-item.validated .count-number {\r\n  color: #42be65;\r\n}\r\n\r\n.count-item.unvalidated .count-number {\r\n  color: #fa4d56;\r\n}\r\n\r\n.count-item.total .count-number {\r\n  color: #0f62fe;\r\n}\r\n\r\n/* Part numbers and breakout groups sections */\r\n.part-numbers-section,\r\n.breakout-groups-section {\r\n  margin-bottom: 2rem;\r\n\r\n  h4 {\r\n    color: #f4f4f4;\r\n    font-size: 1rem;\r\n    font-weight: 600;\r\n    margin-bottom: 1rem;\r\n    padding-bottom: 0.5rem;\r\n    border-bottom: 1px solid #525252;\r\n  }\r\n}\r\n\r\n.part-numbers-list,\r\n.breakout-groups-list {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 0.5rem;\r\n  max-height: 200px;\r\n  overflow-y: auto;\r\n  padding: 0.5rem;\r\n  background-color: #393939;\r\n  border-radius: 8px;\r\n  border: 1px solid #525252;\r\n}\r\n\r\n.part-number-tag,\r\n.breakout-group-tag {\r\n  font-size: 0.75rem;\r\n  white-space: nowrap;\r\n}\r\n\r\n/* Empty state */\r\n.empty-state {\r\n  text-align: center;\r\n  padding: 3rem;\r\n  color: #8d8d8d;\r\n\r\n  p {\r\n    font-size: 1.125rem;\r\n    margin: 0;\r\n  }\r\n}\r\n\r\n/* Summary tiles */\r\n.validations-summary {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\r\n  gap: 1rem;\r\n  margin-bottom: 2rem;\r\n}\r\n\r\n.summary-tile {\r\n  background-color: #262626;\r\n  border: 1px solid #333333;\r\n  border-radius: 8px;\r\n  padding: 1.5rem;\r\n  text-align: center;\r\n}\r\n\r\n.tile-title {\r\n  color: #8d8d8d;\r\n  font-size: 0.875rem;\r\n  margin-bottom: 0.5rem;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.16px;\r\n}\r\n\r\n.tile-value {\r\n  color: #f4f4f4;\r\n  font-size: 2rem;\r\n  font-weight: 600;\r\n  margin-bottom: 0.25rem;\r\n\r\n  &.validated {\r\n    color: #42be65;\r\n  }\r\n\r\n  &.unvalidated {\r\n    color: #fa4d56;\r\n  }\r\n}\r\n\r\n.tile-percentage {\r\n  color: #8d8d8d;\r\n  font-size: 0.875rem;\r\n  font-weight: 400;\r\n}\r\n\r\n/* Empty states */\r\n.empty-message {\r\n  color: #8d8d8d;\r\n  text-align: center;\r\n  padding: 2rem;\r\n}\r\n\r\n/* Modal styles */\r\n.validation-details-modal {\r\n  max-width: 1000px;\r\n}\r\n\r\n.modal-content {\r\n  padding: 1.5rem 0;\r\n  color: #f4f4f4;\r\n}\r\n\r\n.modal-section {\r\n  margin-bottom: 2rem;\r\n}\r\n\r\n.section-title {\r\n  font-weight: 600;\r\n  margin-bottom: 1rem;\r\n  font-size: 1.125rem;\r\n  color: #f4f4f4;\r\n  border-bottom: 1px solid #333333;\r\n  padding-bottom: 0.5rem;\r\n}\r\n\r\n.info-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(2, 1fr);\r\n  gap: 1.5rem;\r\n  margin-bottom: 1.5rem;\r\n}\r\n\r\n.info-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.info-label {\r\n  font-size: 0.875rem;\r\n  color: #8d8d8d;\r\n  margin-bottom: 0.375rem;\r\n}\r\n\r\n.info-value {\r\n  font-size: 1rem;\r\n  color: #f4f4f4;\r\n}\r\n\r\n.status-banner {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 1rem;\r\n  border-radius: 8px;\r\n  margin-bottom: 1.5rem;\r\n  background-color: rgba(15, 98, 254, 0.1);\r\n  border: 1px solid rgba(15, 98, 254, 0.3);\r\n}\r\n\r\n.validation-summary {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n}\r\n\r\n.validation-label {\r\n  color: #f4f4f4;\r\n  font-weight: 500;\r\n}\r\n\r\n.bulk-validation-form {\r\n  background-color: #262626;\r\n  padding: 1.5rem;\r\n  border-radius: 8px;\r\n  border: 1px solid #333333;\r\n  margin-top: 1rem;\r\n}\r\n\r\n.form-group {\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.form-label {\r\n  display: block;\r\n  font-size: 0.875rem;\r\n  color: #8d8d8d;\r\n  margin-bottom: 0.5rem;\r\n}\r\n\r\n.form-actions {\r\n  margin-top: 1.5rem;\r\n  display: flex;\r\n  justify-content: flex-end;\r\n}\r\n\r\n.modal-actions {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  gap: 1rem;\r\n  margin-top: 2rem;\r\n}\r\n\r\n/* Legacy view styles */\r\n.legacy-view {\r\n  padding: 1rem;\r\n  background-color: #262626;\r\n  border-radius: 8px;\r\n  border: 1px solid #333333;\r\n}\r\n\r\n.chart-page__grid {\r\n  margin-top: $spacing-08;\r\n}\r\n\r\n.progress-section {\r\n  margin-top: 2rem;\r\n  margin-bottom: 2rem;\r\n}\r\n\r\n.button-container {\r\n  margin-top: 20px;\r\n  display: flex;\r\n  gap: 1rem; /* Adjust space between buttons */\r\n}\r\n\r\n.fail-container {\r\n  display: flex;                /* Enable flexbox */\r\n  flex-direction: column;       /* Stack items vertically */\r\n  align-items: center;          /* Center horizontally */\r\n  justify-content: center;      /* Center vertically */\r\n  text-align: center;           /* Center text */\r\n  margin: 20px;                 /* Optional: Add margin for spacing */\r\n}\r\n\r\n/* Responsive adjustments */\r\n@media (max-width: 1024px) {\r\n  .info-grid {\r\n    grid-template-columns: 1fr;\r\n  }\r\n\r\n  .filter-bar {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 1rem;\r\n  }\r\n\r\n  .filter-group {\r\n    width: 100%;\r\n    justify-content: space-between;\r\n  }\r\n\r\n  .search-box {\r\n    max-width: 100%;\r\n  }\r\n\r\n  .validations-summary {\r\n    flex-direction: column;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .page-header {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 1rem;\r\n  }\r\n\r\n  .header-actions {\r\n    width: 100%;\r\n  }\r\n}\r\n</style>"]}]}