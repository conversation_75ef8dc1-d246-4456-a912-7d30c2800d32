<template>
  <div class="validations-container">
    <!-- Inherit the MainHeader component -->
    <MainHeader :expandedSideNav="expandedSideNav" :useFixed="useFixed" />

    <!-- Page Header -->
    <div class="page-header">
      <h1 class="page-title">Defect Validations</h1>
      <div class="header-actions">
        <cv-button kind="primary" @click="refreshData">Refresh Data</cv-button>
      </div>
    </div>

    <!-- Filter Bar -->
    <div class="filter-bar">
      <div class="filter-group">
        <div class="filter-label">PQE Owner:</div>
        <cv-dropdown
          v-model="selectedPQEOwner"
          label="Filter by PQE Owner"
          :items="pqeOwnerOptions"
          @change="handlePQEOwnerChange"
        />
      </div>

      <div class="filter-group">
        <div class="filter-label">Process/Commodity:</div>
        <cv-dropdown
          v-model="selectedProcess"
          label="Filter by process"
          :items="processOptions"
          @change="loadValidationData"
        ></cv-dropdown>
      </div>

      <div class="filter-group">
        <div class="filter-label">Time Period:</div>
        <cv-dropdown
          v-model="selectedTimeRange"
          label="Filter by time period"
          :items="rangeOptions"
          @change="loadValidationData"
        ></cv-dropdown>
      </div>

      <div class="search-box">
        <cv-search
          v-model="searchQuery"
          label="Search"
          placeholder="Search by part number or group..."
          @input="filterValidations"
        ></cv-search>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading" class="loading-container">
      <cv-loading :active="true" :small="false" :withOverlay="false" />
      <p class="loading-text">Loading validation data...</p>
    </div>

    <!-- Error State -->
    <div v-else-if="loadingError" class="error-container">
      <cv-inline-notification
        kind="error"
        :title="'Error'"
        :sub-title="loadingError"
      />
    </div>

    <!-- Content when data is loaded -->
    <div v-else class="validations-content">
      <!-- Summary Tiles -->
      <div class="validations-summary">


        <cv-tile class="summary-tile">
          <h4 class="tile-title">Validated</h4>
          <p class="tile-value validated">{{ validationsSummary.validated }}</p>
          <p class="tile-percentage">{{ validationsSummary.validatedPercentage }}%</p>
        </cv-tile>

        <cv-tile class="summary-tile">
          <h4 class="tile-title">Unvalidated</h4>
          <p class="tile-value unvalidated">{{ validationsSummary.unvalidated }}</p>
          <p class="tile-percentage">{{ validationsSummary.unvalidatedPercentage }}%</p>
        </cv-tile>
      </div>

      <!-- Main Content -->
      <div class="main-validation-content">
        <!-- PQE Validation Results -->
        <div class="validation-section" v-if="selectedPQEOwner && selectedPQEOwner !== 'All'">
          <h3>Validation Results - {{ selectedPQEOwner }}</h3>

          <!-- Validation Counts -->
          <div class="validation-counts">
            <div class="count-item validated">
              <div class="count-number">{{ pqeValidationData.validated }}</div>
              <div class="count-label">Validated Defects</div>
            </div>
            <div class="count-item unvalidated">
              <div class="count-number">{{ pqeValidationData.unvalidated }}</div>
              <div class="count-label">Unvalidated Defects</div>
            </div>
            <div class="count-item total">
              <div class="count-number">{{ pqeValidationData.total }}</div>
              <div class="count-label">Total Defects</div>
            </div>
          </div>

          <!-- Part Numbers/Groupings -->
          <div class="part-numbers-section" v-if="pqePartNumbers.length > 0">
            <h4>Associated Part Numbers ({{ pqePartNumbers.length }})</h4>
            <div class="part-numbers-list">
              <cv-tag
                v-for="pn in pqePartNumbers"
                :key="pn"
                kind="gray"
                class="part-number-tag"
              >
                {{ pn }}
              </cv-tag>
            </div>
          </div>

          <!-- Breakout Groups -->
          <div class="breakout-groups-section" v-if="pqeBreakoutGroups.length > 0">
            <h4>Associated Breakout Groups ({{ pqeBreakoutGroups.length }})</h4>
            <div class="breakout-groups-list">
              <cv-tag
                v-for="group in pqeBreakoutGroups"
                :key="group"
                kind="blue"
                class="breakout-group-tag"
              >
                {{ group }}
              </cv-tag>
            </div>
          </div>
        </div>

        <!-- Default message when no PQE selected -->
        <div class="validation-section" v-else>
          <h3>Defect Validation</h3>
          <div class="empty-state">
            <p>Please select a PQE Owner to view validation data.</p>
          </div>
        </div>

        <!-- Legacy View Section -->
        <div class="validation-section legacy-view">
          <h3>Legacy Validation View</h3>
          <cv-content-switcher aria-label="Choose content" @selected='reset()'>
            <cv-content-switcher-button content-selector=".content-1" :selected="selectedIndex === 0">Cables</cv-content-switcher-button>
            <cv-content-switcher-button content-selector=".content-2" :selected="selectedIndex === 1">Power/Thermal</cv-content-switcher-button>
          </cv-content-switcher>

          <div class="progress-section content-1">
            <cv-progress>
              <cv-progress-step
                v-for="(step, index) in cableStepsStatus"
                :key="index"
                :label="step.label"
                :complete="step.complete"
                @step-clicked="stepClick(step.pn, step.label)"
              ></cv-progress-step>
            </cv-progress>
          </div>

          <div class="progress-section content-2">
            <cv-progress>
              <cv-progress-step
                v-for="(step, index) in powerThermalSteps"
                :key="index"
                :label="step.label"
                :complete="step.complete"
                @step-clicked="stepClick(step.pn, step.label)"
              ></cv-progress-step>
            </cv-progress>
          </div>

          <h3>{{ clickedStepName }}</h3>

          <div v-if="stepClicked">
            <cv-dropdown
              label="Range of Fails"
              v-model="selectedRange"
              :items="rangeOptions"
              :selected-item="selectedRange"
            ></cv-dropdown>

            <GaugeChart v-if="gaugeActive" :data="gaugeData" />

            <div class="fail-container">
              <p><strong>Unvalidated count:</strong> {{ unvalidated_count }}</p>
              <p><strong>Total number of Fails:</strong> {{ total_fails }}</p>

              <!-- Buttons under the bar chart -->
              <div class="button-container">
                <cv-button @click="viewData">View Data</cv-button>
                <cv-button @click="validateEach">Validate Each</cv-button>
                <cv-button @click="validateBulk">Validate Bulk</cv-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Validation Details Modal -->
    <cv-modal
      class="validation-details-modal"
      :visible="detailsModalVisible"
      @modal-hidden="detailsModalVisible = false"
      :size="'lg'"
    >
      <template slot="title">
        <div>{{ selectedItem ? (selectedItem.partNumber ? 'Part Number: ' + selectedItem.partNumber : 'Group: ' + selectedItem.name) : 'Validation Details' }}</div>
      </template>
      <template slot="content">
        <div class="modal-content" v-if="selectedItem">
          <!-- Status Banner -->
          <div class="status-banner status-banner-in-progress">
            <div class="validation-summary">
              <span class="validation-label">Validation Rate:</span>
              <cv-tag :kind="getValidationTagKind(selectedItem.percentage)">
                {{ selectedItem.percentage }}%
              </cv-tag>
            </div>
          </div>

          <!-- Validation Details -->
          <div class="modal-section">
            <div class="section-title">Validation Summary</div>
            <div class="info-grid">
              <div class="info-item">
                <span class="info-label">Total Validations</span>
                <span class="info-value">{{ selectedItem.total }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">Validated</span>
                <span class="info-value">{{ selectedItem.validated }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">Unvalidated</span>
                <span class="info-value">{{ selectedItem.unvalidated }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">Time Period</span>
                <span class="info-value">{{ selectedTimeRange }}</span>
              </div>
            </div>
          </div>

          <!-- Validation Items Table -->
          <div class="modal-section">
            <div class="section-title">Validation Items</div>
            <cv-data-table
              :columns="validationDetailsColumns"
              :pagination="{ pageSize: 10 }"
              :title="''"
            >
              <template slot="data">
                <cv-data-table-row
                  v-for="(item, index) in selectedItemDetails"
                  :key="index"
                >
                  <cv-data-table-cell>{{ item.defect_id }}</cv-data-table-cell>
                  <cv-data-table-cell>{{ item.pn }}</cv-data-table-cell>
                  <cv-data-table-cell>{{ item.sn }}</cv-data-table-cell>
                  <cv-data-table-cell>{{ formatDate(item.date) }}</cv-data-table-cell>
                  <cv-data-table-cell>
                    <cv-tag :kind="item.status === 'validated' ? 'green' : 'red'">
                      {{ item.status === 'validated' ? 'Validated' : 'Unvalidated' }}
                    </cv-tag>
                  </cv-data-table-cell>
                  <cv-data-table-cell>{{ item.root_cause_1 || 'N/A' }}</cv-data-table-cell>
                  <cv-data-table-cell>
                    <cv-button
                      kind="ghost"
                      size="small"
                      @click="validateItem(item)"
                      v-if="item.status !== 'validated'"
                    >
                      Validate
                    </cv-button>
                    <span v-else>-</span>
                  </cv-data-table-cell>
                </cv-data-table-row>

                <!-- Empty state -->
                <cv-data-table-row v-if="selectedItemDetails.length === 0">
                  <cv-data-table-cell colspan="7" class="empty-message">
                    No validation items found.
                  </cv-data-table-cell>
                </cv-data-table-row>
              </template>
            </cv-data-table>
          </div>

          <!-- Bulk Validation Section -->
          <div class="modal-section" v-if="hasUnvalidatedItems">
            <div class="section-title">Bulk Validation</div>
            <div class="bulk-validation-form">
              <div class="form-group">
                <label class="form-label">Root Cause</label>
                <cv-dropdown
                  v-model="bulkValidationRootCause"
                  label="Select Root Cause"
                  :items="rootCauseOptions"
                ></cv-dropdown>
              </div>

              <div class="form-group">
                <label class="form-label">Comments</label>
                <cv-text-area
                  v-model="bulkValidationComments"
                  label="Comments"
                  placeholder="Enter validation comments"
                ></cv-text-area>
              </div>

              <div class="form-actions">
                <cv-button
                  kind="primary"
                  @click="validateAllItems"
                >
                  Validate All Unvalidated Items
                </cv-button>
              </div>
            </div>
          </div>

          <!-- Modal Actions -->
          <div class="modal-actions">
            <cv-button kind="secondary" @click="detailsModalVisible = false">Close</cv-button>
            <cv-button kind="primary" @click="refreshItemDetails">Refresh</cv-button>
          </div>
        </div>
      </template>
    </cv-modal>
  </div>
</template>


<script>
import GaugeChart from '../../components/GaugeChart';
import MainHeader from '@/components/MainHeader'; // Import the MainHeader component

export default {
  name: 'DefectValidations',
  components: {
    GaugeChart,
    MainHeader
  },

  data() {
    return {
      // PQE Owner data
      selectedPQEOwner: 'All',
      pqeOwners: [],
      pqeOwnerOptions: [{ label: 'All PQE Owners', value: 'All' }],

      // PQE Validation data
      pqeValidationData: {
        total: 0,
        validated: 0,
        unvalidated: 0,
        percentage: 0
      },
      pqePartNumbers: [],
      pqeBreakoutGroups: [],

      // Legacy view data
      selectedIndex: 0, // Track the selected index
      stepClicked: false, // Track whether a step is clicked
      clickedStepName: '', // Track the clicked step's name
      selectedRange: '', // Default range of fails

      numberOfFails: 0, // Default number of fails
      cableStepsStatus: [
        { label: "SMP9", pn: "02EA657", complete: true },
        { label: "Signal", pn: "03FM185", complete: false }, //signal
        { label: "CDFP", pn: "02EC799", complete: true }, //cdfp
      ],

      powerThermalSteps: [
        { label: "Fans", pn: "02ED368", complete: true },
        { label: "PSU", pn: "03KP588", complete: false }, //signal
        { label: "PDU", pn: "03JG497", complete: true }, //cdfp
      ],
      gaugeActive: false,
      gaugeData: [
        {
          group: 'value',
          value: 0
        }
      ], // Data for the gauge chart (number of validations left)
      unvalidated_fails: [],
      validated_fails: [],
      unvalidated_count: 0,
      validated_count: 0,
      total_fails: 0,
      perc_val: 0,
      selectedPN: "",

      // New UI data
      isLoading: false,
      loadingError: null,
      searchQuery: '',
      selectedProcess: 'All',
      processOptions: ['All', 'FUL', 'FAB', 'Power', 'Cable', 'Memory'],
      selectedTimeRange: 'Last 3 Months',
      rangeOptions: ["Last Month", "Last 3 Months", "Last 6 Months", "Last Year", "All Time"],

      // Validation data
      validationColumns: ['Part Number', 'Group', 'Total', 'Validated', 'Unvalidated', 'Validation %', 'Actions'],
      validationDetailsColumns: ['Defect ID', 'Part Number', 'Serial Number', 'Date', 'Status', 'Root Cause', 'Actions'],
      validationsByGroup: [],
      validationsByPart: [],

      // Modal data
      detailsModalVisible: false,
      selectedItem: null,
      selectedItemDetails: [],
      bulkValidationRootCause: '',
      bulkValidationComments: '',
      rootCauseOptions: ['Design Issue', 'Manufacturing Defect', 'Material Issue', 'Test Error', 'Handling Damage', 'Unknown'],

      // Summary data
      validationsSummary: {
        total: 0,
        validated: 0,
        unvalidated: 0,
        validatedPercentage: 0,
        unvalidatedPercentage: 0
      },

      // Date tracking
      currentDate: '',
      selectedMonthDate: '',
      selectedWeekDate: '',
      expandedSideNav: false,
      useFixed: true
    };
  },

  computed: {
    hasUnvalidatedItems() {
      return this.selectedItemDetails.some(item => item.status !== 'validated');
    }
  },

  watch: {
    selectedRange(newRange) {
      if (newRange) {
        this.gaugeActive = false;
        this.get_unval();
      }
    }
  },

  mounted() {
    this.get_dates();
    this.loadPQEOwners();
  },

  methods: {
    // PQE Owner methods
    async loadPQEOwners() {
      try {
        const config = this.getAuthConfig();
        const response = await fetch('/api-statit2/get_pqe_owners', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...config.headers
          },
          body: JSON.stringify({})
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch PQE owners: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        if (data.status_res === 'success') {
          this.pqeOwners = data.pqe_owners || [];

          // Update dropdown options
          this.pqeOwnerOptions = [
            { label: 'All PQE Owners', value: 'All' },
            ...this.pqeOwners.map(owner => ({ label: owner, value: owner }))
          ];

          console.log(`Loaded ${this.pqeOwners.length} PQE owners`);
        } else {
          console.error('Failed to load PQE owners:', data.message);
        }
      } catch (error) {
        console.error('Error loading PQE owners:', error);
      }
    },

    // Get authentication config
    getAuthConfig() {
      return {
        headers: {
          'Authorization': 'Bearer ' + (localStorage.getItem('token') || ''),
          'X-User-ID': localStorage.getItem('userId') || ''
        }
      };
    },

    // Handle PQE owner change
    async handlePQEOwnerChange() {
      console.log(`Selected PQE owner: ${this.selectedPQEOwner}`);

      if (this.selectedPQEOwner && this.selectedPQEOwner !== 'All') {
        await this.loadPQEValidationData();
      } else {
        // Reset PQE data when "All" is selected
        this.pqeValidationData = {
          total: 0,
          validated: 0,
          unvalidated: 0,
          percentage: 0
        };
        this.pqePartNumbers = [];
        this.pqeBreakoutGroups = [];
      }

      this.updateValidationsSummary();
    },

    // Update validations summary
    updateValidationsSummary() {
      // Use PQE validation data if a specific PQE is selected
      if (this.selectedPQEOwner && this.selectedPQEOwner !== 'All') {
        this.validationsSummary = {
          total: this.pqeValidationData.total,
          validated: this.pqeValidationData.validated,
          unvalidated: this.pqeValidationData.unvalidated,
          validatedPercentage: this.pqeValidationData.percentage,
          unvalidatedPercentage: this.pqeValidationData.total > 0 ? 100 - this.pqeValidationData.percentage : 0
        };
      } else {
        // Reset summary when no specific PQE is selected
        this.validationsSummary = {
          total: 0,
          validated: 0,
          unvalidated: 0,
          validatedPercentage: 0,
          unvalidatedPercentage: 0
        };
      }
    },

    // Load PQE validation data
    async loadPQEValidationData() {
      if (!this.selectedPQEOwner || this.selectedPQEOwner === 'All') {
        return;
      }

      try {
        console.log(`Loading validation data for PQE owner: ${this.selectedPQEOwner}`);

        // Get part numbers and breakout groups for this PQE owner
        const pqeData = await this.getPQEData(this.selectedPQEOwner);
        this.pqePartNumbers = pqeData.partNumbers;
        this.pqeBreakoutGroups = pqeData.breakoutGroups;

        if (this.pqePartNumbers.length === 0) {
          console.warn(`No part numbers found for PQE owner: ${this.selectedPQEOwner}`);
          this.pqeValidationData = {
            total: 0,
            validated: 0,
            unvalidated: 0,
            percentage: 0
          };
          return;
        }

        // Query validation data for these part numbers
        let totalValidated = 0;
        let totalUnvalidated = 0;

        const user_type = this.$store.getters.getUser_type;
        const token = this.$store.getters.getToken;
        const action = "view";
        const startdate = this.getStartDateFromRange(this.selectedTimeRange);

        // Process each part number to get validation counts
        for (const pn of this.pqePartNumbers) {
          try {
            const response = await fetch(process.env.VUE_APP_API_PATH + "get_unval", {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                Authorization: "Bearer " + token,
              },
              body: JSON.stringify({ "PN": pn, "SD": startdate, "ED": this.currentDate, user_type, action }),
            });

            if (response.ok) {
              const data = await this.handleResponse(response);
              if (data && data.status_res === "success") {
                totalValidated += data.validated_count || 0;
                totalUnvalidated += data.unvalidated_count || 0;
              }
            }
          } catch (error) {
            console.error(`Error fetching validation data for part ${pn}:`, error);
          }
        }

        // Update PQE validation data
        const totalFails = totalValidated + totalUnvalidated;
        const percentage = totalFails > 0 ? Math.round((totalValidated / totalFails) * 100) : 0;

        this.pqeValidationData = {
          total: totalFails,
          validated: totalValidated,
          unvalidated: totalUnvalidated,
          percentage: percentage
        };

        console.log(`PQE ${this.selectedPQEOwner} validation data:`, this.pqeValidationData);
        console.log(`Part numbers (${this.pqePartNumbers.length}):`, this.pqePartNumbers);
        console.log(`Breakout groups (${this.pqeBreakoutGroups.length}):`, this.pqeBreakoutGroups);

      } catch (error) {
        console.error(`Error loading PQE validation data:`, error);
      }
    },

    // Get part numbers and breakout groups for a PQE owner
    async getPQEData(pqeOwner) {
      try {
        // Use the existing PQE dashboard API to get breakout groups
        const config = this.getAuthConfig();
        const response = await fetch('/api-statit2/get_pqe_breakout_groups', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...config.headers
          },
          body: JSON.stringify({ pqeOwner })
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch PQE data: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        if (data.status_res === 'success') {
          const breakoutGroups = data.breakout_groups || [];

          // Now get part numbers for these breakout groups
          const partNumbers = await this.getPartNumbersForBreakoutGroups(breakoutGroups);

          console.log(`Found ${breakoutGroups.length} breakout groups and ${partNumbers.length} part numbers for PQE owner ${pqeOwner}`);

          return {
            breakoutGroups: breakoutGroups,
            partNumbers: partNumbers
          };
        } else {
          console.error('Failed to get PQE data:', data.message);
          return { breakoutGroups: [], partNumbers: [] };
        }
      } catch (error) {
        console.error(`Error getting PQE data for ${pqeOwner}:`, error);
        return { breakoutGroups: [], partNumbers: [] };
      }
    },

    // Get part numbers for breakout groups
    async getPartNumbersForBreakoutGroups(breakoutGroups) {
      try {
        if (!breakoutGroups || breakoutGroups.length === 0) {
          return [];
        }

        const config = this.getAuthConfig();
        const response = await fetch('/api-statit2/get_pqe_part_numbers', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...config.headers
          },
          body: JSON.stringify({ breakoutGroups })
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch part numbers: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        if (data.status === 'success') {
          return data.partNumbers || [];
        } else {
          console.error('Failed to get part numbers:', data.message);
          return [];
        }
      } catch (error) {
        console.error(`Error getting part numbers for breakout groups:`, error);
        return [];
      }
    },

    // Legacy methods
    async get_unval() {
      try {
        let user_type = this.$store.getters.getUser_type;
        let action = "view";
        let startdate = ""
        let token = this.$store.getters.getToken;
        console.log("TOKEN", token)
        if (this.selectedRange === "Past Month"){
          startdate = this.selectedMonthDate
        } else if (this.selectedRange === "Past Week"){
          startdate = this.selectedWeekDate
        }

        // Fetch data from the API
        const response = await fetch(process.env.VUE_APP_API_PATH + "get_unval", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: "Bearer " + token,
          },
          body: JSON.stringify({ "PN": this.selectedPN, "SD": startdate, "ED": this.currentDate, user_type, action }),
        });

        if (response.status === 401) {
          console.error("Unauthorized: Check your token or credentials.");
        }

        const data = await this.handleResponse(response);

        if (data.status_res === "success") {
          this.unvalidated_fails = data.unvalidated_fails
          this.validated_fails = data.validated_fails
          this.unvalidated_count = data.unvalidated_count
          this.validated_count = data.validated_count
          this.total_fails = data.total_fails
          this.perc_val = data.perc_val

          console.log("Received data:", data);
          if(this.perc_val === null){
            this.total_fails = "No entries"
          }else{
            this.gaugeActive = true;
          }

          this.gaugeData = [
            {
              group: 'value',
              value: data.perc_val
            }
          ];
        }
      } catch (error) {
        console.error("Error loading data:", error);
      }
    },

    get_dates() {
      const currentDate = new Date();
      const previousMonthDate = new Date();
      const previousWeekDate = new Date();

      previousMonthDate.setMonth(currentDate.getMonth() - 1);
      // Subtract 7 days from the current date
      previousWeekDate.setDate(currentDate.getDate() - 7);

      // Format the dates (e.g., YYYY-MM-DD)
      const formatDate = (date) => {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0'); // Months are 0-indexed
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
      };

      // Create the selectedRange string
      this.selectedWeekDate = formatDate(previousWeekDate)
      this.selectedMonthDate = formatDate(previousMonthDate)
      this.currentDate = formatDate(currentDate)
    },

    stepClick(pn, label) {
      this.gaugeActive = false;
      this.stepClicked = true; // Show the clicked info section
      this.selectedPN = pn;
      this.get_unval();
      this.clickedStepName = `${pn} - ${label}`; // Update the clicked step's name
    },

    validateEach() {
      console.log('Validating each for:', this.selectedPN);
      // Implement logic for validating each item here
    },

    validateBulk() {
      console.log('Validating bulk for:', this.selectedPN);
      // Implement logic for bulk validation here
    },

    handleResponse(response) {
      if (!response.ok) {
        if (response.status === 401) {
          this.session_expired_visible = true;
        }
      } else {
        return response.json();
      }
    },

    reset() {
      this.stepClicked = false; // Reset the step clicked status
      this.clickedStepName = 'Choose PN'; // Reset the clicked step name
      this.selectedRange = 'Monthly'; // Reset the selected range
      this.gaugeActive = false; // Hide the gauge chart
      this.selectedPN = ''; // Reset the selected part number
      // Any other reset logic can go here
      console.log('Content switcher reset');
    },

    // Utility methods

    async getPartNumbersForProcess(process) {
      try {
        let user_type = this.$store.getters.getUser_type;
        let token = this.$store.getters.getToken;

        // If "All" is selected, get all Metis part numbers
        if (process === 'All') {
          const response = await fetch(process.env.VUE_APP_API_PATH + "get_metis_part_numbers", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: "Bearer " + token,
            },
            body: JSON.stringify({ user_type }),
          });

          const data = await this.handleResponse(response);

          if (data && data.status_res === "success" && data.pns) {
            console.log(`Retrieved ${data.pns.length} part numbers from Metis file`);
            return data.pns;
          }
        } else {
          // For specific processes, use the commodity filter
          const response = await fetch(process.env.VUE_APP_API_PATH + "get_pns_from_excel", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: "Bearer " + token,
            },
            body: JSON.stringify({ commodity: process, user_type }),
          });

          const data = await this.handleResponse(response);

          if (data && data.status_res === "success" && data.pns) {
            console.log(`Retrieved ${data.pns.length} part numbers for ${process} from commodity file`);
            return data.pns;
          }
        }

        // Fallback to hardcoded values if API fails
        console.warn("Falling back to hardcoded part numbers");
        const fallbackParts = {
          'All': ["02EA657", "03FM185", "02EC799", "02ED368", "03KP588", "03JG497", "01KP123", "02FM456", "03EC789"],
          'FUL': ["01KP123", "02FM456", "03EC789"],
          'FAB': ["04KP321", "05FM654", "06EC987"],
          'Power': ["02ED368", "03KP588", "03JG497"],
          'Cable': ["02EA657", "03FM185", "02EC799"],
          'Memory': ["07KP111", "08FM222", "09EC333"]
        };

        return fallbackParts[process] || fallbackParts['All'];
      } catch (error) {
        console.error("Error fetching part numbers:", error);
        // Return fallback values in case of error
        return ["02EA657", "03FM185", "02EC799"];
      }
    },

    async getMetisGroupForPartNumber(partNumber) {
      try {
        let user_type = this.$store.getters.getUser_type;
        let token = this.$store.getters.getToken;

        // First try to get the Metis breakout names
        const response = await fetch(process.env.VUE_APP_API_PATH + "get_metis_breakout_names", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: "Bearer " + token,
          },
          body: JSON.stringify({ user_type }),
        });

        const data = await this.handleResponse(response);

        if (data && data.status_res === "success" && data.breakoutMap) {
          // The API should return a mapping of part numbers to breakout names
          const breakoutName = data.breakoutMap[partNumber];
          if (breakoutName) {
            return breakoutName;
          }
        }

        // If we couldn't get the breakout name from the API, use a fallback mapping
        // This is a simplified version of what would be in the Excel file
        const fallbackGroupMap = {
          "02EA657": "SMP9 Cables",
          "03FM185": "Signal Cables",
          "02EC799": "CDFP Cables",
          "02ED368": "Cooling Fans",
          "03KP588": "Power Supply Units",
          "03JG497": "Power Distribution",
          "01KP123": "Memory Module A",
          "02FM456": "Memory Module B",
          "03EC789": "Memory Module C",
          "04KP321": "Processor Module A",
          "05FM654": "Processor Module B",
          "06EC987": "Processor Module C",
          "07KP111": "Storage Module A",
          "08FM222": "Storage Module B",
          "09EC333": "Storage Module C"
        };

        return fallbackGroupMap[partNumber] || "Unknown Group";
      } catch (error) {
        console.error("Error fetching Metis group for part number:", error);
        // Return a generic group name in case of error
        return "Unknown Group";
      }
    },

    getStartDateFromRange(range) {
      const now = new Date();
      let startDate = new Date();

      switch (range) {
        case "Last Month":
          startDate.setMonth(now.getMonth() - 1);
          break;
        case "Last 3 Months":
          startDate.setMonth(now.getMonth() - 3);
          break;
        case "Last 6 Months":
          startDate.setMonth(now.getMonth() - 6);
          break;
        case "Last Year":
          startDate.setFullYear(now.getFullYear() - 1);
          break;
        case "All Time":
          startDate = new Date(2000, 0, 1); // Far in the past
          break;
        default:
          startDate.setMonth(now.getMonth() - 3); // Default to 3 months
      }

      // Format date as YYYY-MM-DD
      return startDate.toISOString().split('T')[0];
    },

    filterValidations() {
      // Filtering is handled by computed properties
      console.log("Filtering with query:", this.searchQuery);
    },



    async loadItemDetails(item) {
      this.selectedItemDetails = [];

      try {
        let user_type = this.$store.getters.getUser_type;
        let action = "view";
        let startdate = this.getStartDateFromRange(this.selectedTimeRange);
        let token = this.$store.getters.getToken;

        console.log(`Loading details for ${item.partNumber ? 'part ' + item.partNumber : 'group ' + item.name}`);

        // If it's a part number item
        if (item.partNumber) {
          const response = await fetch(process.env.VUE_APP_API_PATH + "get_unval", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: "Bearer " + token,
            },
            body: JSON.stringify({ "PN": item.partNumber, "SD": startdate, "ED": this.currentDate, user_type, action }),
          });

          const data = await this.handleResponse(response);

          if (data && data.status_res === "success") {
            // Combine validated and unvalidated fails
            const validatedItems = (data.validated_fails || []).map(item => ({
              ...item,
              status: 'validated'
            }));

            const unvalidatedItems = (data.unvalidated_fails || []).map(item => ({
              ...item,
              status: 'unvalidated'
            }));

            this.selectedItemDetails = [...validatedItems, ...unvalidatedItems];
            console.log(`Loaded ${this.selectedItemDetails.length} validation items for part ${item.partNumber}`);
          }
        }
        // If it's a group item (Metis grouping)
        else if (item.name) {
          // First try to get part numbers for this Metis group from the API
          let partNumbers = [];

          try {
            // Try to get part numbers for this Metis group from the API
            const metisResponse = await fetch(process.env.VUE_APP_API_PATH + "get_metis_part_numbers", {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                Authorization: "Bearer " + token,
              },
              body: JSON.stringify({ breakoutName: item.name, user_type }),
            });

            const metisData = await this.handleResponse(metisResponse);

            if (metisData && metisData.status_res === "success" && metisData.pns && metisData.pns.length > 0) {
              partNumbers = metisData.pns;
              console.log(`Retrieved ${partNumbers.length} part numbers for Metis group ${item.name} from API`);
            }
          } catch (error) {
            console.error(`Error fetching part numbers for Metis group ${item.name}:`, error);
          }

          // If we couldn't get part numbers from the API, fall back to the local data
          if (partNumbers.length === 0) {
            partNumbers = this.validationsByPart
              .filter(part => part.group === item.name)
              .map(part => part.partNumber);

            console.log(`Using ${partNumbers.length} part numbers for Metis group ${item.name} from local data`);
          }

          if (partNumbers.length === 0) {
            console.warn(`No part numbers found for Metis group ${item.name}`);
            return;
          }

          // Fetch details for each part number
          let allDetails = [];

          for (const pn of partNumbers) {
            try {
              const response = await fetch(process.env.VUE_APP_API_PATH + "get_unval", {
                method: "POST",
                headers: {
                  "Content-Type": "application/json",
                  Authorization: "Bearer " + token,
                },
                body: JSON.stringify({ "PN": pn, "SD": startdate, "ED": this.currentDate, user_type, action }),
              });

              const data = await this.handleResponse(response);

              if (data && data.status_res === "success") {
                // Combine validated and unvalidated fails
                const validatedItems = (data.validated_fails || []).map(item => ({
                  ...item,
                  status: 'validated'
                }));

                const unvalidatedItems = (data.unvalidated_fails || []).map(item => ({
                  ...item,
                  status: 'unvalidated'
                }));

                allDetails = [...allDetails, ...validatedItems, ...unvalidatedItems];
                console.log(`Added ${validatedItems.length + unvalidatedItems.length} items for part ${pn}`);
              }
            } catch (error) {
              console.error(`Error fetching validation data for part ${pn}:`, error);
              // Continue with other part numbers
            }
          }

          this.selectedItemDetails = allDetails;
          console.log(`Loaded ${this.selectedItemDetails.length} total validation items for group ${item.name}`);
        }
      } catch (error) {
        console.error("Error loading item details:", error);
      }
    },

    refreshItemDetails() {
      if (this.selectedItem) {
        this.loadItemDetails(this.selectedItem);
      }
    },

    validateItem(item) {
      console.log("Validating item:", item);
      // This would typically call an API to validate the item
      // For demo purposes, we'll just update the local state
      item.status = 'validated';
      item.root_cause_1 = this.bulkValidationRootCause || 'Manual Validation';

      // Update counts
      if (this.selectedItem) {
        this.selectedItem.validated++;
        this.selectedItem.unvalidated--;
        this.selectedItem.percentage = Math.round((this.selectedItem.validated / this.selectedItem.total) * 100);
      }
    },

    validateAllItems() {
      if (!this.bulkValidationRootCause) {
        alert("Please select a root cause before validating all items.");
        return;
      }

      // Find all unvalidated items
      const unvalidatedItems = this.selectedItemDetails.filter(item => item.status !== 'validated');

      // Validate each item
      unvalidatedItems.forEach(item => {
        item.status = 'validated';
        item.root_cause_1 = this.bulkValidationRootCause;
      });

      // Update counts
      if (this.selectedItem) {
        this.selectedItem.validated = this.selectedItem.total;
        this.selectedItem.unvalidated = 0;
        this.selectedItem.percentage = 100;
      }

      // Clear form
      this.bulkValidationRootCause = '';
      this.bulkValidationComments = '';
    },

    getValidationTagKind(percentage) {
      if (percentage >= 90) return 'green';
      if (percentage >= 70) return 'teal';
      if (percentage >= 50) return 'blue';
      if (percentage >= 30) return 'purple';
      if (percentage >= 10) return 'magenta';
      return 'red';
    },

    formatDate(dateString) {
      if (!dateString) return 'N/A';

      // If it's already in a readable format, return as is
      if (typeof dateString === 'string' && dateString.includes('/')) {
        return dateString;
      }

      // Try to parse the date
      try {
        const date = new Date(dateString);
        return date.toLocaleDateString();
      } catch (e) {
        return dateString;
      }
    },

    viewData() {
      console.log("View data clicked");
      // Implement view data functionality
    }
  }
};
</script>
<style scoped lang="scss">
@import "../../styles/carbon-utils";

/* Main container */
.validations-container {
  min-height: 100vh;
  background-color: #161616;
  color: #f4f4f4;
}

/* Page header */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding: 2rem 2rem 1rem;
  border-bottom: 1px solid #333333;
}

.page-title {
  color: #f4f4f4;
  font-size: 1.75rem;
  font-weight: 400;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 1rem;
}

/* Filter bar */
.filter-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0 2rem 1.5rem;
  background-color: #262626;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #333333;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.filter-label {
  color: #8d8d8d;
  font-size: 0.875rem;
}

.search-box {
  flex-grow: 1;
  max-width: 300px;
}

/* Loading and error states */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  margin: 2rem;
  background-color: #262626;
  border-radius: 8px;
  border: 1px solid #333333;
}

.loading-text {
  margin-top: 1rem;
  color: #8d8d8d;
}

.error-container {
  margin: 2rem;
}

/* Content area */
.validations-content {
  padding: 0 2rem 2rem;
}

.main-validation-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.validation-section {
  background-color: #262626;
  border: 1px solid #333333;
  border-radius: 8px;
  padding: 1.5rem;

  h3 {
    color: #f4f4f4;
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0 0 1.5rem 0;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid #393939;
  }
}

/* Validation counts styles */
.validation-counts {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.count-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 2rem 1rem;
  background-color: #393939;
  border-radius: 8px;
  border: 1px solid #525252;
  text-align: center;
}

.count-number {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.count-label {
  font-size: 0.875rem;
  color: #c6c6c6;
  text-transform: uppercase;
  letter-spacing: 0.16px;
}

.count-item.validated .count-number {
  color: #42be65;
}

.count-item.unvalidated .count-number {
  color: #fa4d56;
}

.count-item.total .count-number {
  color: #0f62fe;
}

/* Part numbers and breakout groups sections */
.part-numbers-section,
.breakout-groups-section {
  margin-bottom: 2rem;

  h4 {
    color: #f4f4f4;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #525252;
  }
}

.part-numbers-list,
.breakout-groups-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  max-height: 200px;
  overflow-y: auto;
  padding: 0.5rem;
  background-color: #393939;
  border-radius: 8px;
  border: 1px solid #525252;
}

.part-number-tag,
.breakout-group-tag {
  font-size: 0.75rem;
  white-space: nowrap;
}

/* Empty state */
.empty-state {
  text-align: center;
  padding: 3rem;
  color: #8d8d8d;

  p {
    font-size: 1.125rem;
    margin: 0;
  }
}

/* Summary tiles */
.validations-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.summary-tile {
  background-color: #262626;
  border: 1px solid #333333;
  border-radius: 8px;
  padding: 1.5rem;
  text-align: center;
}

.tile-title {
  color: #8d8d8d;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.16px;
}

.tile-value {
  color: #f4f4f4;
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 0.25rem;

  &.validated {
    color: #42be65;
  }

  &.unvalidated {
    color: #fa4d56;
  }
}

.tile-percentage {
  color: #8d8d8d;
  font-size: 0.875rem;
  font-weight: 400;
}

/* Empty states */
.empty-message {
  color: #8d8d8d;
  text-align: center;
  padding: 2rem;
}

/* Modal styles */
.validation-details-modal {
  max-width: 1000px;
}

.modal-content {
  padding: 1.5rem 0;
  color: #f4f4f4;
}

.modal-section {
  margin-bottom: 2rem;
}

.section-title {
  font-weight: 600;
  margin-bottom: 1rem;
  font-size: 1.125rem;
  color: #f4f4f4;
  border-bottom: 1px solid #333333;
  padding-bottom: 0.5rem;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.info-item {
  display: flex;
  flex-direction: column;
}

.info-label {
  font-size: 0.875rem;
  color: #8d8d8d;
  margin-bottom: 0.375rem;
}

.info-value {
  font-size: 1rem;
  color: #f4f4f4;
}

.status-banner {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
  background-color: rgba(15, 98, 254, 0.1);
  border: 1px solid rgba(15, 98, 254, 0.3);
}

.validation-summary {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.validation-label {
  color: #f4f4f4;
  font-weight: 500;
}

.bulk-validation-form {
  background-color: #262626;
  padding: 1.5rem;
  border-radius: 8px;
  border: 1px solid #333333;
  margin-top: 1rem;
}

.form-group {
  margin-bottom: 1rem;
}

.form-label {
  display: block;
  font-size: 0.875rem;
  color: #8d8d8d;
  margin-bottom: 0.5rem;
}

.form-actions {
  margin-top: 1.5rem;
  display: flex;
  justify-content: flex-end;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
}

/* Legacy view styles */
.legacy-view {
  padding: 1rem;
  background-color: #262626;
  border-radius: 8px;
  border: 1px solid #333333;
}

.chart-page__grid {
  margin-top: $spacing-08;
}

.progress-section {
  margin-top: 2rem;
  margin-bottom: 2rem;
}

.button-container {
  margin-top: 20px;
  display: flex;
  gap: 1rem; /* Adjust space between buttons */
}

.fail-container {
  display: flex;                /* Enable flexbox */
  flex-direction: column;       /* Stack items vertically */
  align-items: center;          /* Center horizontally */
  justify-content: center;      /* Center vertically */
  text-align: center;           /* Center text */
  margin: 20px;                 /* Optional: Add margin for spacing */
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .info-grid {
    grid-template-columns: 1fr;
  }

  .filter-bar {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .filter-group {
    width: 100%;
    justify-content: space-between;
  }

  .search-box {
    max-width: 100%;
  }

  .validations-summary {
    flex-direction: column;
  }
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .header-actions {
    width: 100%;
  }
}
</style>