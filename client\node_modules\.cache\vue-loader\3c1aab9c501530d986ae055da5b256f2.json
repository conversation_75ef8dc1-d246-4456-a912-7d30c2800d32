{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\ActionTracker\\ActionTracker.vue?vue&type=style&index=0&id=55f84a0b&scoped=true&lang=scss", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\ActionTracker\\ActionTracker.vue", "mtime": 1748988452772}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\@vue\\cli-service\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["ActionTracker.vue"], "names": [], "mappings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file": "ActionTracker.vue", "sourceRoot": "src/views/ActionTracker", "sourcesContent": ["<style scoped lang=\"scss\">\r\n@import \"../../styles/carbon-utils\";\r\n\r\n.dashboard-container {\r\n  min-height: 100vh;\r\n  background-color: #161616;\r\n}\r\n\r\n.main-content {\r\n  padding: 2rem;\r\n  max-width: 1400px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.page-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 2rem;\r\n  padding-bottom: 1rem;\r\n  border-bottom: 1px solid #333333;\r\n}\r\n\r\n.page-title {\r\n  color: #f4f4f4;\r\n  font-size: 1.75rem;\r\n  font-weight: 400;\r\n  margin: 0;\r\n}\r\n\r\n.action-controls {\r\n  display: flex;\r\n  gap: 1rem;\r\n}\r\n\r\n.filter-bar {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 1.5rem;\r\n  background-color: #262626;\r\n  padding: 1rem;\r\n  border-radius: 8px;\r\n  border: 1px solid #333333;\r\n}\r\n\r\n.filter-group {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 1rem;\r\n}\r\n\r\n.filter-label {\r\n  color: #8d8d8d;\r\n  font-size: 0.875rem;\r\n}\r\n\r\n.search-box {\r\n  flex-grow: 1;\r\n  max-width: 300px;\r\n}\r\n\r\n/* Table styling */\r\n.action-table {\r\n  background-color: #262626;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n  border: 1px solid #333333;\r\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.table-header {\r\n  background-color: #333333;\r\n  padding: 1rem;\r\n  border-bottom: 1px solid #444444;\r\n}\r\n\r\n.table-title {\r\n  color: #f4f4f4;\r\n  font-size: 1.25rem;\r\n  font-weight: 400;\r\n  margin: 0;\r\n}\r\n\r\n/* Cell styling */\r\n.edit-icon {\r\n  margin-left: 8px;\r\n  cursor: pointer;\r\n  color: #0f62fe;\r\n  opacity: 0.8;\r\n  transition: opacity 0.2s ease;\r\n}\r\n\r\n.edit-icon:hover {\r\n  opacity: 1;\r\n}\r\n\r\n.editable-field {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.editable-field input {\r\n  background-color: #333333;\r\n  border: 1px solid #0f62fe;\r\n  color: #f4f4f4;\r\n  padding: 0.25rem 0.5rem;\r\n  border-radius: 4px;\r\n  font-size: 0.875rem;\r\n}\r\n\r\n.action-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n}\r\n\r\n.action-text {\r\n  flex-grow: 1;\r\n}\r\n\r\n.see-more-button {\r\n  background-color: #0f62fe;\r\n  color: #ffffff;\r\n  padding: 0.25rem 0.5rem;\r\n  border-radius: 4px;\r\n  font-size: 0.75rem;\r\n  border: none;\r\n  cursor: pointer;\r\n  transition: background-color 0.2s ease;\r\n  white-space: nowrap;\r\n}\r\n\r\n.see-more-button:hover {\r\n  background-color: #0353e9;\r\n}\r\n\r\n/* Status indicators */\r\n.status-indicator {\r\n  display: inline-block;\r\n  width: 8px;\r\n  height: 8px;\r\n  border-radius: 50%;\r\n  margin-right: 0.5rem;\r\n}\r\n\r\n.status-in-progress {\r\n  background-color: #0f62fe;\r\n}\r\n\r\n.status-completed {\r\n  background-color: #42be65;\r\n}\r\n\r\n.status-blocked {\r\n  background-color: #fa4d56;\r\n}\r\n\r\n/* Status badges */\r\n.status-badge {\r\n  display: inline-flex;\r\n  align-items: center;\r\n  padding: 0.25rem 0.5rem;\r\n  border-radius: 4px;\r\n  font-size: 0.75rem;\r\n  font-weight: 600;\r\n  text-transform: uppercase;\r\n}\r\n\r\n.status-badge.in-progress {\r\n  background-color: rgba(15, 98, 254, 0.2);\r\n  color: #78a9ff;\r\n}\r\n\r\n.status-badge.completed {\r\n  background-color: rgba(66, 190, 101, 0.2);\r\n  color: #6fdc8c;\r\n}\r\n\r\n.status-badge.blocked {\r\n  background-color: rgba(250, 77, 86, 0.2);\r\n  color: #ff8389;\r\n}\r\n\r\n/* Status banner */\r\n.status-banner {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 1rem;\r\n  border-radius: 8px;\r\n  margin-bottom: 1.5rem;\r\n}\r\n\r\n.status-banner-in-progress {\r\n  background-color: rgba(15, 98, 254, 0.1);\r\n  border: 1px solid rgba(15, 98, 254, 0.3);\r\n}\r\n\r\n.status-banner-completed {\r\n  background-color: rgba(66, 190, 101, 0.1);\r\n  border: 1px solid rgba(66, 190, 101, 0.3);\r\n}\r\n\r\n.status-banner-blocked {\r\n  background-color: rgba(250, 77, 86, 0.1);\r\n  border: 1px solid rgba(250, 77, 86, 0.3);\r\n}\r\n\r\n.assignee-info {\r\n  color: #f4f4f4;\r\n  font-size: 0.875rem;\r\n}\r\n\r\n/* Modal actions */\r\n.modal-actions {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  gap: 1rem;\r\n  margin-top: 2rem;\r\n}\r\n\r\n/* Form styling */\r\n.form-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(2, 1fr);\r\n  gap: 1.5rem;\r\n  margin-bottom: 1.5rem;\r\n}\r\n\r\n.form-group {\r\n  display: flex;\r\n  flex-direction: column;\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.form-group.full-width {\r\n  grid-column: span 2;\r\n}\r\n\r\n.form-label {\r\n  font-size: 0.875rem;\r\n  color: #8d8d8d;\r\n  margin-bottom: 0.5rem;\r\n}\r\n\r\n/* Modal styles */\r\n.action-modal {\r\n  max-width: 800px;\r\n}\r\n\r\n.modal-content {\r\n  padding: 1.5rem 0;\r\n  color: #f4f4f4;\r\n}\r\n\r\n.modal-section {\r\n  margin-bottom: 2rem;\r\n}\r\n\r\n.section-title {\r\n  font-weight: 600;\r\n  margin-bottom: 1rem;\r\n  font-size: 1.125rem;\r\n  color: #f4f4f4;\r\n  border-bottom: 1px solid #333333;\r\n  padding-bottom: 0.5rem;\r\n}\r\n\r\n.info-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(2, 1fr);\r\n  gap: 1.5rem;\r\n  margin-bottom: 1.5rem;\r\n}\r\n\r\n.info-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.info-label {\r\n  font-size: 0.875rem;\r\n  color: #8d8d8d;\r\n  margin-bottom: 0.375rem;\r\n}\r\n\r\n.info-value {\r\n  font-size: 1rem;\r\n  color: #f4f4f4;\r\n}\r\n\r\n.action-details {\r\n  background-color: #333333;\r\n  padding: 1.5rem;\r\n  border-radius: 8px;\r\n  margin-top: 1rem;\r\n  border: 1px solid #444444;\r\n}\r\n\r\n.action-title {\r\n  font-weight: 600;\r\n  margin-bottom: 0.75rem;\r\n  color: #f4f4f4;\r\n  font-size: 1rem;\r\n}\r\n\r\n.action-description {\r\n  white-space: pre-line;\r\n  margin-bottom: 1.5rem;\r\n  color: #f4f4f4;\r\n  line-height: 1.5;\r\n}\r\n\r\n/* Required field indicator */\r\n.required-field {\r\n  color: #fa4d56;\r\n  margin-left: 4px;\r\n  font-weight: bold;\r\n}\r\n\r\n/* Form validation styles */\r\n.form-error {\r\n  color: #fa4d56;\r\n  font-size: 0.75rem;\r\n  margin-top: 0.25rem;\r\n}\r\n\r\n/* Form note */\r\n.form-note {\r\n  color: #8d8d8d;\r\n  font-size: 0.875rem;\r\n  margin-bottom: 1rem;\r\n  font-style: italic;\r\n}\r\n\r\n/* Responsive adjustments */\r\n@media (max-width: 1024px) {\r\n  .info-grid {\r\n    grid-template-columns: 1fr;\r\n  }\r\n\r\n  .filter-bar {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 1rem;\r\n  }\r\n\r\n  .filter-group {\r\n    width: 100%;\r\n    justify-content: space-between;\r\n  }\r\n\r\n  .search-box {\r\n    max-width: 100%;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .main-content {\r\n    padding: 1.5rem 1rem;\r\n  }\r\n\r\n  .page-header {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 1rem;\r\n  }\r\n\r\n  .action-controls {\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n/* Loading and error states */\r\n.loading-indicator, .loading-message {\r\n  color: #0f62fe;\r\n  font-size: 14px;\r\n  margin-top: 10px;\r\n}\r\n\r\n.error-message {\r\n  color: #da1e28;\r\n  font-size: 14px;\r\n  margin-top: 10px;\r\n}\r\n\r\n.empty-message {\r\n  color: #8d8d8d;\r\n  font-size: 14px;\r\n  text-align: center;\r\n  padding: 20px;\r\n}\r\n\r\n.table-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n/* Progress bar styling */\r\n.progress-container {\r\n  width: 100%;\r\n  padding: 0.5rem 0;\r\n}\r\n\r\n.detail-progress-bar {\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.progress-update-controls {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 0.5rem;\r\n  margin-top: 1rem;\r\n  background-color: #333333;\r\n  padding: 1rem;\r\n  border-radius: 4px;\r\n}\r\n\r\n/* Updates styling */\r\n.updates-list {\r\n  margin-top: 1rem;\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 1rem;\r\n}\r\n\r\n.update-item {\r\n  background-color: #333333;\r\n  padding: 1rem;\r\n  border-radius: 4px;\r\n  border-left: 3px solid #0f62fe;\r\n}\r\n\r\n.update-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 0.5rem;\r\n  font-size: 0.875rem;\r\n  color: #8d8d8d;\r\n}\r\n\r\n.update-content {\r\n  color: #f4f4f4;\r\n  line-height: 1.5;\r\n}\r\n\r\n.update-date {\r\n  font-weight: 600;\r\n}\r\n\r\n.update-by {\r\n  font-style: italic;\r\n}\r\n\r\n.add-update-form {\r\n  margin-top: 1rem;\r\n}\r\n\r\n.update-form-actions {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  margin-top: 1rem;\r\n}\r\n\r\n.expected-improvements {\r\n  margin-top: 1.5rem;\r\n  margin-bottom: 1.5rem;\r\n}\r\n\r\n/* Progress indicator in table */\r\n.cv-progress {\r\n  width: 100%;\r\n}\r\n\r\n/* Modal form styling */\r\n.modal-dropdown {\r\n  width: 100%;\r\n}\r\n\r\n.info-item .cv-text-input,\r\n.info-item .cv-dropdown {\r\n  margin-top: 0.25rem;\r\n}\r\n\r\n.action-textarea {\r\n  margin-bottom: 1.5rem;\r\n}\r\n\r\n.info-grid {\r\n  row-gap: 1.5rem;\r\n}\r\n\r\n/* Tab styling */\r\n.tab-content {\r\n  padding: 1.5rem 0;\r\n}\r\n\r\n/* Status tabs styling */\r\n.cv-tabs {\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.cv-tabs .cv-tab {\r\n  color: #f4f4f4;\r\n}\r\n\r\n.cv-tabs .cv-tab.cv-tab--selected {\r\n  border-bottom-color: #0f62fe;\r\n}\r\n\r\n/* Tab content styling */\r\n.tab-content {\r\n  min-height: 400px;\r\n}\r\n\r\n/* Tracking Modal Styles */\r\n.tracking-modal {\r\n  .cv-modal-container {\r\n    max-width: 95vw;\r\n    width: 1200px;\r\n  }\r\n}\r\n\r\n.tracking-modal-content {\r\n  padding: 1rem;\r\n  min-height: 600px;\r\n}\r\n\r\n.tracking-tab-content {\r\n  padding: 1.5rem;\r\n  min-height: 500px;\r\n}\r\n\r\n.tracking-section {\r\n  background-color: #333333;\r\n  border-radius: 8px;\r\n  padding: 1.5rem;\r\n  border: 1px solid #444444;\r\n}\r\n\r\n.tracking-section-title {\r\n  color: #f4f4f4;\r\n  font-size: 1.125rem;\r\n  font-weight: 600;\r\n  margin: 0 0 1rem 0;\r\n  border-bottom: 1px solid #444444;\r\n  padding-bottom: 0.5rem;\r\n}\r\n\r\n.action-summary {\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr;\r\n  gap: 0.75rem;\r\n}\r\n\r\n.summary-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 0.25rem;\r\n}\r\n\r\n.summary-label {\r\n  color: #8d8d8d;\r\n  font-size: 0.875rem;\r\n  font-weight: 500;\r\n}\r\n\r\n.summary-value {\r\n  color: #f4f4f4;\r\n  font-size: 1rem;\r\n  font-weight: 400;\r\n}\r\n\r\n.summary-value.priority-badge {\r\n  display: inline-flex;\r\n  align-items: center;\r\n  padding: 0.25rem 0.5rem;\r\n  border-radius: 4px;\r\n  font-size: 0.75rem;\r\n  font-weight: 600;\r\n  text-transform: uppercase;\r\n  width: fit-content;\r\n}\r\n\r\n.summary-value.priority-badge.high {\r\n  background-color: rgba(250, 77, 86, 0.2);\r\n  color: #ff8389;\r\n}\r\n\r\n.summary-value.priority-badge.medium {\r\n  background-color: rgba(255, 196, 0, 0.2);\r\n  color: #ffcc00;\r\n}\r\n\r\n.summary-value.priority-badge.low {\r\n  background-color: rgba(66, 190, 101, 0.2);\r\n  color: #6fdc8c;\r\n}\r\n\r\n.updates-list {\r\n  max-height: 300px;\r\n  overflow-y: auto;\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 1rem;\r\n}\r\n\r\n.update-item {\r\n  background-color: #262626;\r\n  border-radius: 6px;\r\n  padding: 1rem;\r\n  border-left: 3px solid #0f62fe;\r\n}\r\n\r\n.update-date {\r\n  color: #8d8d8d;\r\n  font-size: 0.875rem;\r\n  font-weight: 500;\r\n  margin-bottom: 0.5rem;\r\n}\r\n\r\n.update-content {\r\n  color: #f4f4f4;\r\n  font-size: 0.875rem;\r\n  line-height: 1.4;\r\n  margin-bottom: 0.5rem;\r\n}\r\n\r\n.update-author {\r\n  color: #8d8d8d;\r\n  font-size: 0.75rem;\r\n  font-style: italic;\r\n}\r\n\r\n.no-updates {\r\n  color: #8d8d8d;\r\n  font-style: italic;\r\n  text-align: center;\r\n  padding: 2rem;\r\n}\r\n\r\n/* Action Items List */\r\n.action-items-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 1.5rem;\r\n}\r\n\r\n.action-item-card {\r\n  background-color: #262626;\r\n  border-radius: 8px;\r\n  padding: 1.5rem;\r\n  border: 1px solid #444444;\r\n  transition: all 0.2s ease;\r\n}\r\n\r\n.action-item-card.completed {\r\n  border-left: 4px solid #42be65;\r\n  background-color: rgba(66, 190, 101, 0.05);\r\n}\r\n\r\n.action-item-header {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 0.5rem;\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.completion-date {\r\n  color: #42be65;\r\n  font-size: 0.875rem;\r\n  font-weight: 500;\r\n}\r\n\r\n.last-updated {\r\n  color: #8d8d8d;\r\n  font-size: 0.875rem;\r\n  font-style: italic;\r\n}\r\n\r\n.action-item-description {\r\n  color: #f4f4f4;\r\n  font-size: 0.875rem;\r\n  line-height: 1.4;\r\n  margin-bottom: 1rem;\r\n  padding: 0.75rem;\r\n  background-color: #333333;\r\n  border-radius: 4px;\r\n  border-left: 3px solid #0f62fe;\r\n}\r\n\r\n.action-item-notes {\r\n  margin-top: 1rem;\r\n}\r\n\r\n.notes-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 0.75rem;\r\n}\r\n\r\n.notes-header h5 {\r\n  color: #f4f4f4;\r\n  font-size: 0.875rem;\r\n  font-weight: 500;\r\n  margin: 0;\r\n}\r\n\r\n.add-note-inline {\r\n  background-color: #333333;\r\n  border-radius: 6px;\r\n  padding: 1rem;\r\n  margin-bottom: 1rem;\r\n  border: 1px solid #444444;\r\n}\r\n\r\n.note-actions {\r\n  display: flex;\r\n  gap: 0.5rem;\r\n  margin-top: 0.75rem;\r\n}\r\n\r\n.no-action-items {\r\n  text-align: center;\r\n  padding: 2rem;\r\n  color: #8d8d8d;\r\n}\r\n\r\n.no-action-items p {\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n/* Action Notes Section */\r\n.action-notes-section {\r\n  margin-bottom: 1.5rem;\r\n}\r\n\r\n.action-notes-section h4 {\r\n  color: #f4f4f4;\r\n  font-size: 1rem;\r\n  font-weight: 500;\r\n  margin: 0 0 1rem 0;\r\n}\r\n\r\n.notes-list {\r\n  max-height: 200px;\r\n  overflow-y: auto;\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.note-item {\r\n  background-color: #262626;\r\n  border-radius: 6px;\r\n  padding: 1rem;\r\n  margin-bottom: 0.75rem;\r\n  border-left: 3px solid #42be65;\r\n}\r\n\r\n.note-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 0.5rem;\r\n}\r\n\r\n.note-date {\r\n  color: #8d8d8d;\r\n  font-size: 0.875rem;\r\n  font-weight: 500;\r\n}\r\n\r\n.note-author {\r\n  color: #8d8d8d;\r\n  font-size: 0.75rem;\r\n  font-style: italic;\r\n}\r\n\r\n.note-content {\r\n  color: #f4f4f4;\r\n  font-size: 0.875rem;\r\n  line-height: 1.4;\r\n}\r\n\r\n.no-notes {\r\n  color: #8d8d8d;\r\n  font-style: italic;\r\n  text-align: center;\r\n  padding: 1rem;\r\n}\r\n\r\n.add-note-section {\r\n  background-color: #262626;\r\n  border-radius: 6px;\r\n  padding: 1rem;\r\n  border: 1px solid #444444;\r\n}\r\n\r\n/* Updates History Section */\r\n.updates-history-section h4 {\r\n  color: #f4f4f4;\r\n  font-size: 1rem;\r\n  font-weight: 500;\r\n  margin: 0 0 1rem 0;\r\n}\r\n\r\n.add-update-section {\r\n  background-color: #262626;\r\n  border-radius: 6px;\r\n  padding: 1rem;\r\n  border: 1px solid #444444;\r\n}\r\n\r\n.add-update-section h4 {\r\n  color: #f4f4f4;\r\n  font-size: 1rem;\r\n  font-weight: 500;\r\n  margin: 0 0 1rem 0;\r\n}\r\n\r\n.chart-container {\r\n  background-color: #262626;\r\n  border-radius: 6px;\r\n  padding: 1rem;\r\n  min-height: 350px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.no-chart-data {\r\n  text-align: center;\r\n  color: #8d8d8d;\r\n}\r\n\r\n.no-chart-data p {\r\n  margin: 0.5rem 0;\r\n}\r\n\r\n.chart-note {\r\n  font-size: 0.875rem;\r\n  font-style: italic;\r\n}\r\n\r\n/* Alert History Styles */\r\n.alert-history-section {\r\n  padding: 1.5rem;\r\n}\r\n\r\n.section-title {\r\n  color: #f4f4f4;\r\n  font-size: 1.25rem;\r\n  font-weight: 500;\r\n  margin: 0 0 0.5rem 0;\r\n}\r\n\r\n.section-description {\r\n  color: #8d8d8d;\r\n  font-size: 0.875rem;\r\n  margin: 0 0 1.5rem 0;\r\n  line-height: 1.4;\r\n}\r\n\r\n.alert-history-table {\r\n  background-color: #262626;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.alert-history-row.alert-row {\r\n  background-color: rgba(250, 77, 86, 0.1);\r\n  border-left: 3px solid #fa4d56;\r\n}\r\n\r\n.alert-history-row.normal-row {\r\n  background-color: rgba(66, 190, 101, 0.05);\r\n  border-left: 3px solid #42be65;\r\n}\r\n\r\n.status-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n}\r\n\r\n.status-indicator {\r\n  width: 8px;\r\n  height: 8px;\r\n  border-radius: 50%;\r\n  display: inline-block;\r\n}\r\n\r\n.status-indicator.alert {\r\n  background-color: #fa4d56;\r\n  animation: pulse-red 2s infinite;\r\n}\r\n\r\n.status-indicator.normal {\r\n  background-color: #42be65;\r\n}\r\n\r\n.status-text.alert {\r\n  color: #ff8389;\r\n  font-weight: 500;\r\n}\r\n\r\n.status-text.normal {\r\n  color: #6fdc8c;\r\n  font-weight: 500;\r\n}\r\n\r\n@keyframes pulse-red {\r\n  0% {\r\n    box-shadow: 0 0 0 0 rgba(250, 77, 86, 0.7);\r\n  }\r\n  70% {\r\n    box-shadow: 0 0 0 10px rgba(250, 77, 86, 0);\r\n  }\r\n  100% {\r\n    box-shadow: 0 0 0 0 rgba(250, 77, 86, 0);\r\n  }\r\n}\r\n\r\n.no-alert-data {\r\n  text-align: center;\r\n  padding: 3rem;\r\n  color: #8d8d8d;\r\n}\r\n\r\n.no-alert-data p {\r\n  margin: 0.5rem 0;\r\n}\r\n\r\n.no-alert-data .note {\r\n  font-size: 0.875rem;\r\n  font-style: italic;\r\n}\r\n\r\n/* New Alert Functionality Styles */\r\n.new-alerts-section {\r\n  padding: 1.5rem;\r\n}\r\n\r\n.subsection-title {\r\n  color: #f4f4f4;\r\n  font-size: 1rem;\r\n  font-weight: 500;\r\n  margin: 0 0 1rem 0;\r\n  border-bottom: 1px solid #444444;\r\n  padding-bottom: 0.5rem;\r\n}\r\n\r\n.ai-insight-section {\r\n  background-color: #333333;\r\n  border-radius: 8px;\r\n  padding: 1.5rem;\r\n  margin-bottom: 2rem;\r\n  border: 1px solid #444444;\r\n}\r\n\r\n.ai-insight-content {\r\n  background-color: #262626;\r\n  border-radius: 6px;\r\n  padding: 1rem;\r\n  border: 1px solid #525252;\r\n  font-size: 0.875rem;\r\n  line-height: 1.5;\r\n  color: #f4f4f4;\r\n}\r\n\r\n.add-alert-section {\r\n  background-color: #333333;\r\n  border-radius: 8px;\r\n  padding: 1.5rem;\r\n  margin-bottom: 2rem;\r\n  border: 1px solid #444444;\r\n}\r\n\r\n.alert-updates-section {\r\n  background-color: #333333;\r\n  border-radius: 8px;\r\n  padding: 1.5rem;\r\n  border: 1px solid #444444;\r\n}\r\n\r\n.alert-updates-table {\r\n  background-color: #262626;\r\n  border-radius: 6px;\r\n  overflow: hidden;\r\n  border: 1px solid #525252;\r\n}\r\n\r\n.updates-header {\r\n  display: grid;\r\n  grid-template-columns: 120px 1fr 150px;\r\n  background-color: #393939;\r\n  border-bottom: 1px solid #525252;\r\n}\r\n\r\n.update-column {\r\n  padding: 0.75rem;\r\n  font-weight: 600;\r\n  color: #f4f4f4;\r\n  border-right: 1px solid #525252;\r\n}\r\n\r\n.update-column:last-child {\r\n  border-right: none;\r\n}\r\n\r\n.update-row {\r\n  display: grid;\r\n  grid-template-columns: 120px 1fr 150px;\r\n  border-bottom: 1px solid #525252;\r\n}\r\n\r\n.update-row:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.update-cell {\r\n  padding: 0.75rem;\r\n  color: #f4f4f4;\r\n  border-right: 1px solid #525252;\r\n  font-size: 0.875rem;\r\n}\r\n\r\n.update-cell:last-child {\r\n  border-right: none;\r\n}\r\n\r\n.no-updates-message {\r\n  text-align: center;\r\n  color: #8d8d8d;\r\n  font-style: italic;\r\n  padding: 2rem;\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .updates-header,\r\n  .update-row {\r\n    grid-template-columns: 1fr;\r\n  }\r\n\r\n  .update-column,\r\n  .update-cell {\r\n    border-right: none;\r\n    border-bottom: 1px solid #525252;\r\n  }\r\n}\r\n\r\n/* Performance Chart & History Tab Styles */\r\n.chart-section {\r\n  background-color: #333333;\r\n  border-radius: 8px;\r\n  padding: 1.5rem;\r\n  margin-bottom: 2rem;\r\n  border: 1px solid #444444;\r\n}\r\n\r\n.performance-history-section {\r\n  background-color: #333333;\r\n  border-radius: 8px;\r\n  padding: 1.5rem;\r\n  border: 1px solid #444444;\r\n}\r\n\r\n.section-description {\r\n  color: #c6c6c6;\r\n  font-size: 0.875rem;\r\n  margin-bottom: 2rem;\r\n}\r\n</style>\r\n\r\n\r\n<template>\r\n  <div class=\"dashboard-container\">\r\n    <!-- Inherit the MainHeader component -->\r\n    <MainHeader :expandedSideNav=\"expandedSideNav\" :useFixed=\"useFixed\" />\r\n\r\n    <main class=\"main-content\">\r\n      <!-- Page Header -->\r\n      <div class=\"page-header\">\r\n        <h1 class=\"page-title\">Action Tracker</h1>\r\n        <div class=\"action-controls\">\r\n          <cv-button kind=\"primary\" @click=\"openNewActionModal\">Add New Action</cv-button>\r\n          <cv-button kind=\"secondary\" @click=\"exportData\">Export</cv-button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Filter Bar -->\r\n      <div class=\"filter-bar\">\r\n        <div class=\"filter-group\">\r\n          <div class=\"filter-label\">Commodity:</div>\r\n          <cv-dropdown\r\n            v-model=\"commodityFilter\"\r\n            label=\"Filter by commodity\"\r\n            :items=\"commodityOptions\"\r\n          ></cv-dropdown>\r\n        </div>\r\n\r\n        <div class=\"filter-group\">\r\n          <div class=\"filter-label\">Assignee:</div>\r\n          <cv-dropdown\r\n            v-model=\"assigneeFilter\"\r\n            label=\"Filter by assignee\"\r\n            :items=\"assigneeOptions\"\r\n          ></cv-dropdown>\r\n        </div>\r\n\r\n        <div class=\"search-box\">\r\n          <cv-search\r\n            v-model=\"searchQuery\"\r\n            label=\"Search\"\r\n            placeholder=\"Search actions...\"\r\n          ></cv-search>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Status Tabs -->\r\n      <cv-tabs @tab-selected=\"onTabSelected\" :selected=\"selectedTab\">\r\n        <cv-tab id=\"in-progress\" label=\"In-Progress Items\">\r\n          <div class=\"tab-content\">\r\n            <ActionItemsTable\r\n              :items=\"inProgressItems\"\r\n              :columns=\"inProgressColumns\"\r\n              :isLoading=\"isLoading\"\r\n              :loadingError=\"loadingError\"\r\n              @item-selected=\"handleItemSelected\"\r\n              @update-item=\"handleUpdateItem\"\r\n              @track-item=\"handleTrackItem\"\r\n              status-type=\"in-progress\"\r\n            />\r\n          </div>\r\n        </cv-tab>\r\n\r\n        <cv-tab id=\"monitored\" label=\"Monitored Items\">\r\n          <div class=\"tab-content\">\r\n            <ActionItemsTable\r\n              :items=\"monitoredItems\"\r\n              :columns=\"monitoredColumns\"\r\n              :isLoading=\"isLoading\"\r\n              :loadingError=\"loadingError\"\r\n              @item-selected=\"handleItemSelected\"\r\n              @update-item=\"handleUpdateItem\"\r\n              @track-item=\"handleTrackItem\"\r\n              status-type=\"monitored\"\r\n            />\r\n          </div>\r\n        </cv-tab>\r\n\r\n        <cv-tab id=\"resolved\" label=\"Resolved Items\">\r\n          <div class=\"tab-content\">\r\n            <ActionItemsTable\r\n              :items=\"resolvedItems\"\r\n              :columns=\"resolvedColumns\"\r\n              :isLoading=\"isLoading\"\r\n              :loadingError=\"loadingError\"\r\n              @item-selected=\"handleItemSelected\"\r\n              @update-item=\"handleUpdateItem\"\r\n              @track-item=\"handleTrackItem\"\r\n              status-type=\"resolved\"\r\n            />\r\n          </div>\r\n        </cv-tab>\r\n      </cv-tabs>\r\n    </main>\r\n\r\n    <!-- Action Details Modal -->\r\n    <cv-modal\r\n\r\n      :visible=\"modalVisible\"\r\n      @modal-hidden=\"modalVisible = false\"\r\n\r\n    >\r\n      <template slot=\"title\">\r\n        <div>Action Details - {{ selectedRow ? selectedRow.pn : '' }}</div>\r\n      </template>\r\n      <template slot=\"content\">\r\n        <div class=\"modal-content\" v-if=\"selectedRow\">\r\n          <!-- Status Banner -->\r\n          <div class=\"status-banner\" :class=\"'status-banner-' + selectedRow.status.toLowerCase()\">\r\n            <div class=\"status-badge\" :class=\"selectedRow.status.toLowerCase()\">\r\n              <span class=\"status-indicator\" :class=\"'status-' + selectedRow.status.toLowerCase()\"></span>\r\n              {{ selectedRow.status }}\r\n            </div>\r\n            <div class=\"assignee-info\">Assigned to: <strong>{{ selectedRow.assignee }}</strong></div>\r\n          </div>\r\n\r\n          <!-- Basic Information Section -->\r\n          <div class=\"modal-section\">\r\n            <div class=\"section-title\">Basic Information</div>\r\n            <div class=\"info-grid\">\r\n              <div class=\"info-item\">\r\n                <span class=\"info-label\">Commodity</span>\r\n                <cv-dropdown\r\n                  v-model=\"selectedRow.commodity\"\r\n                  label=\"Commodity\"\r\n                  :items=\"commodityOptions.filter(item => item !== 'All')\"\r\n                  class=\"modal-dropdown\"\r\n                ></cv-dropdown>\r\n              </div>\r\n              <div class=\"info-item\">\r\n                <span class=\"info-label\">Group</span>\r\n                <cv-text-input\r\n                  v-model=\"selectedRow.group\"\r\n                  label=\"Group\"\r\n                ></cv-text-input>\r\n              </div>\r\n              <div class=\"info-item\">\r\n                <span class=\"info-label\">Part Number</span>\r\n                <cv-text-input\r\n                  v-model=\"selectedRow.pn\"\r\n                  label=\"Part Number\"\r\n                ></cv-text-input>\r\n              </div>\r\n              <div class=\"info-item\">\r\n                <span class=\"info-label\">Test</span>\r\n                <cv-text-input\r\n                  v-model=\"selectedRow.editableTest\"\r\n                  label=\"Test\"\r\n                ></cv-text-input>\r\n              </div>\r\n              <div class=\"info-item\">\r\n                <span class=\"info-label\">Deadline</span>\r\n                <cv-text-input\r\n                  type=\"date\"\r\n                  v-model=\"selectedRow.deadline\"\r\n                  label=\"Deadline\"\r\n                ></cv-text-input>\r\n              </div>\r\n              <div class=\"info-item\">\r\n                <span class=\"info-label\">Expected Resolution</span>\r\n                <cv-text-input\r\n                  type=\"date\"\r\n                  v-model=\"selectedRow.expectedResolution\"\r\n                  label=\"Expected Resolution\"\r\n                ></cv-text-input>\r\n              </div>\r\n              <div class=\"info-item\">\r\n                <span class=\"info-label\">Status</span>\r\n                <cv-dropdown\r\n                  v-model=\"selectedRow.status\"\r\n                  label=\"Status\"\r\n                  :items=\"statusOptions.filter(item => item !== 'All')\"\r\n                  class=\"modal-dropdown\"\r\n                ></cv-dropdown>\r\n              </div>\r\n              <div class=\"info-item\">\r\n                <span class=\"info-label\">Assignee</span>\r\n                <cv-text-input\r\n                  v-model=\"selectedRow.assignee\"\r\n                  label=\"Assignee\"\r\n                ></cv-text-input>\r\n              </div>\r\n              <div class=\"info-item\">\r\n                <span class=\"info-label\">Created</span>\r\n                <span class=\"info-value\">{{ formatDate(selectedRow.createdAt) }}</span>\r\n              </div>\r\n              <div class=\"info-item\">\r\n                <span class=\"info-label\">Last Updated</span>\r\n                <span class=\"info-value\">{{ formatDate(selectedRow.updatedAt) }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Action Details Section -->\r\n          <div class=\"modal-section\">\r\n            <div class=\"section-title\">Action Details</div>\r\n            <div class=\"action-details\">\r\n              <div class=\"action-title\">Current Action</div>\r\n              <cv-text-area\r\n                v-model=\"selectedRow.action\"\r\n                label=\"Action Description\"\r\n                class=\"action-textarea\"\r\n              ></cv-text-area>\r\n\r\n              <div class=\"expected-improvements\">\r\n                <div class=\"action-title\">Expected Improvements</div>\r\n                <cv-text-area\r\n                  v-model=\"selectedRow.expectedImprovements\"\r\n                  label=\"Expected Improvements\"\r\n                  placeholder=\"Describe the expected improvements (e.g., 30% increase in yield, 15% reduction in defects)\"\r\n                  class=\"action-textarea\"\r\n                ></cv-text-area>\r\n              </div>\r\n\r\n              <div class=\"progress-section\">\r\n                <div class=\"action-title\">Progress</div>\r\n                <cv-progress\r\n                  :value=\"selectedRow.progress || 0\"\r\n                  :label-text=\"`${selectedRow.progress || 0}%`\"\r\n                  class=\"detail-progress-bar\"\r\n                />\r\n                <div class=\"progress-update-controls\">\r\n                  <cv-slider\r\n                    v-model=\"selectedRow.progress\"\r\n                    :min=\"0\"\r\n                    :max=\"100\"\r\n                    :step=\"5\"\r\n                    :label=\"'Update Progress'\"\r\n                  ></cv-slider>\r\n                  <cv-button\r\n                    kind=\"primary\"\r\n                    size=\"small\"\r\n                    @click=\"updateProgress(selectedRow)\"\r\n                  >Update Progress</cv-button>\r\n                </div>\r\n              </div>\r\n\r\n              <div v-if=\"selectedRow.updates && selectedRow.updates.length > 0\">\r\n                <div class=\"action-title\">Updates History</div>\r\n                <div class=\"updates-list\">\r\n                  <div v-for=\"(update, index) in selectedRow.updates\" :key=\"index\" class=\"update-item\">\r\n                    <div class=\"update-header\">\r\n                      <span class=\"update-date\">{{ formatDate(update.date) }}</span>\r\n                      <span class=\"update-by\">by {{ update.updatedBy }}</span>\r\n                    </div>\r\n                    <div class=\"update-content\">{{ update.content }}</div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Add Update Section -->\r\n          <div class=\"modal-section\">\r\n            <div class=\"section-title\">Add Update</div>\r\n            <div class=\"add-update-form\">\r\n              <cv-text-area\r\n                v-model=\"newUpdate\"\r\n                label=\"Update Content\"\r\n                placeholder=\"Enter update details...\"\r\n              ></cv-text-area>\r\n              <div class=\"update-form-actions\">\r\n                <cv-button\r\n                  kind=\"primary\"\r\n                  @click=\"addUpdate(selectedRow)\"\r\n                >Add Update</cv-button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Issues Section -->\r\n          <div class=\"modal-section\" v-if=\"selectedRow.issues && selectedRow.issues.length > 0\">\r\n            <div class=\"section-title\">Related Issues</div>\r\n            <cv-data-table\r\n              :columns=\"issueColumns\"\r\n              :data=\"selectedRow.issues\"\r\n              :title=\"''\"\r\n            ></cv-data-table>\r\n          </div>\r\n\r\n          <!-- Notes Section -->\r\n          <div class=\"modal-section\">\r\n            <div class=\"section-title\">Notes</div>\r\n            <cv-text-area\r\n              v-model=\"selectedRow.notes\"\r\n              label=\"Additional Notes\"\r\n              placeholder=\"Enter any additional notes\"\r\n            ></cv-text-area>\r\n          </div>\r\n\r\n          <!-- Action Buttons -->\r\n          <div class=\"modal-actions\">\r\n            <cv-button kind=\"secondary\" @click=\"modalVisible = false\">Cancel</cv-button>\r\n            <cv-button kind=\"primary\" @click=\"updateEntireAction(selectedRow)\">Update Action</cv-button>\r\n          </div>\r\n        </div>\r\n      </template>\r\n    </cv-modal>\r\n\r\n    <!-- New Action Modal -->\r\n    <cv-modal\r\n      class=\"action-modal\"\r\n      :visible=\"newActionModalVisible\"\r\n      @modal-hidden=\"newActionModalVisible = false\"\r\n      :size=\"'lg'\"\r\n    >\r\n      <template slot=\"title\">\r\n        <div>Create New Action</div>\r\n      </template>\r\n      <template slot=\"content\">\r\n        <div class=\"modal-content\">\r\n          <!-- Basic Information Section -->\r\n          <div class=\"modal-section\">\r\n            <div class=\"section-title\">Basic Information</div>\r\n            <p class=\"form-note\">Fields marked with <span class=\"required-field\">*</span> are required</p>\r\n            <div class=\"form-grid\">\r\n              <div class=\"form-group\">\r\n                <label class=\"form-label\">Process/Commodity <span class=\"required-field\">*</span></label>\r\n                <cv-dropdown\r\n                  v-model=\"newAction.commodity\"\r\n                  label=\"Select Process/Commodity\"\r\n                  :items=\"commodityOptions.filter(item => item !== 'All')\"\r\n                ></cv-dropdown>\r\n              </div>\r\n\r\n              <div class=\"form-group\">\r\n                <label class=\"form-label\">Part Group <span class=\"required-field\">*</span></label>\r\n                <cv-text-input\r\n                  v-model=\"newAction.group\"\r\n                  label=\"Part Group\"\r\n                  placeholder=\"Enter part group name\"\r\n                ></cv-text-input>\r\n              </div>\r\n\r\n              <div class=\"form-group\">\r\n                <label class=\"form-label\">Part Number</label>\r\n                <cv-text-input\r\n                  v-model=\"newAction.pn\"\r\n                  label=\"Part Number\"\r\n                  placeholder=\"Enter part number\"\r\n                ></cv-text-input>\r\n              </div>\r\n\r\n              <div class=\"form-group\">\r\n                <label class=\"form-label\">Test</label>\r\n                <cv-text-input\r\n                  v-model=\"newAction.test\"\r\n                  label=\"Test\"\r\n                  placeholder=\"Enter test name\"\r\n                ></cv-text-input>\r\n              </div>\r\n\r\n              <div class=\"form-group\">\r\n                <label class=\"form-label\">Deadline</label>\r\n                <cv-text-input\r\n                  type=\"date\"\r\n                  v-model=\"newAction.deadline\"\r\n                  label=\"Deadline\"\r\n                ></cv-text-input>\r\n              </div>\r\n\r\n              <div class=\"form-group\">\r\n                <label class=\"form-label\">Expected Resolution Date</label>\r\n                <cv-text-input\r\n                  type=\"date\"\r\n                  v-model=\"newAction.expectedResolution\"\r\n                  label=\"Expected Resolution\"\r\n                ></cv-text-input>\r\n              </div>\r\n\r\n              <div class=\"form-group\">\r\n                <label class=\"form-label\">Status</label>\r\n                <cv-dropdown\r\n                  v-model=\"newAction.status\"\r\n                  label=\"Select Status\"\r\n                  :items=\"['Current', 'In-Progress', 'Monitored', 'Resolved']\"\r\n                ></cv-dropdown>\r\n              </div>\r\n\r\n              <div class=\"form-group\">\r\n                <label class=\"form-label\">Priority</label>\r\n                <cv-dropdown\r\n                  v-model=\"newAction.priority\"\r\n                  label=\"Select Priority\"\r\n                  :items=\"['High', 'Medium', 'Low']\"\r\n                ></cv-dropdown>\r\n              </div>\r\n\r\n              <div class=\"form-group\">\r\n                <label class=\"form-label\">Progress (%)</label>\r\n                <cv-slider\r\n                  v-model=\"newAction.progress\"\r\n                  :min=\"0\"\r\n                  :max=\"100\"\r\n                  :step=\"5\"\r\n                  :label=\"'Progress'\"\r\n                ></cv-slider>\r\n              </div>\r\n\r\n              <div class=\"form-group\">\r\n                <label class=\"form-label\">Assignee</label>\r\n                <cv-text-input\r\n                  v-model=\"newAction.assignee\"\r\n                  label=\"Assignee\"\r\n                  placeholder=\"Enter assignee name\"\r\n                ></cv-text-input>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Action Details Section -->\r\n          <div class=\"modal-section\">\r\n            <div class=\"section-title\">Action Details</div>\r\n            <div class=\"form-group full-width\">\r\n              <label class=\"form-label\">Action Description <span class=\"required-field\">*</span></label>\r\n              <cv-text-area\r\n                v-model=\"newAction.action\"\r\n                label=\"Action Description\"\r\n                placeholder=\"Describe the action to be taken\"\r\n              ></cv-text-area>\r\n            </div>\r\n\r\n            <div class=\"form-group full-width\">\r\n              <label class=\"form-label\">Expected Improvements</label>\r\n              <cv-text-area\r\n                v-model=\"newAction.expectedImprovements\"\r\n                label=\"Expected Improvements\"\r\n                placeholder=\"Describe the expected improvements (e.g., 30% increase in yield, 15% reduction in defects)\"\r\n              ></cv-text-area>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Notes Section -->\r\n          <div class=\"modal-section\">\r\n            <div class=\"section-title\">Notes</div>\r\n            <div class=\"form-group full-width\">\r\n              <label class=\"form-label\">Additional Notes</label>\r\n              <cv-text-area\r\n                v-model=\"newAction.notes\"\r\n                label=\"Notes\"\r\n                placeholder=\"Enter any additional notes\"\r\n              ></cv-text-area>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Action Buttons -->\r\n          <div class=\"modal-actions\">\r\n            <cv-button kind=\"secondary\" @click=\"newActionModalVisible = false\">Cancel</cv-button>\r\n            <cv-button kind=\"primary\" @click=\"createNewAction\">Create Action</cv-button>\r\n          </div>\r\n        </div>\r\n      </template>\r\n    </cv-modal>\r\n\r\n    <!-- Tracking Modal -->\r\n    <cv-modal\r\n      class=\"tracking-modal\"\r\n      :visible=\"trackingModalVisible\"\r\n      @modal-hidden=\"trackingModalVisible = false\"\r\n      :size=\"'xl'\"\r\n    >\r\n      <template slot=\"title\">\r\n        <div>Action Tracking - {{ selectedTrackingItem ? selectedTrackingItem.pn : '' }}</div>\r\n      </template>\r\n      <template slot=\"content\">\r\n        <div class=\"tracking-modal-content\" v-if=\"selectedTrackingItem\">\r\n          <!-- Tracking Tabs -->\r\n          <cv-tabs>\r\n            <cv-tab id=\"action-items-tab\" label=\"Action Items\">\r\n              <div class=\"tracking-tab-content\">\r\n                <div class=\"tracking-section\">\r\n                  <h3 class=\"tracking-section-title\">Action Details</h3>\r\n                  <div class=\"action-summary\">\r\n                    <div class=\"summary-item\">\r\n                      <span class=\"summary-label\">Part Group:</span>\r\n                      <span class=\"summary-value\">{{ selectedTrackingItem.group }}</span>\r\n                    </div>\r\n                    <div class=\"summary-item\">\r\n                      <span class=\"summary-label\">Part Number:</span>\r\n                      <span class=\"summary-value\">{{ selectedTrackingItem.pn }}</span>\r\n                    </div>\r\n                    <div class=\"summary-item\">\r\n                      <span class=\"summary-label\">Status:</span>\r\n                      <span class=\"summary-value\">{{ selectedTrackingItem.status }}</span>\r\n                    </div>\r\n                    <div class=\"summary-item\">\r\n                      <span class=\"summary-label\">Priority:</span>\r\n                      <span class=\"summary-value priority-badge\" :class=\"selectedTrackingItem.priority.toLowerCase()\">\r\n                        {{ selectedTrackingItem.priority }}\r\n                      </span>\r\n                    </div>\r\n                    <div class=\"summary-item\">\r\n                      <span class=\"summary-label\">Progress:</span>\r\n                      <span class=\"summary-value\">{{ selectedTrackingItem.progress }}%</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n              <div class=\"tracking-section\">\r\n                <h3 class=\"tracking-section-title\">Action Items & Progress</h3>\r\n\r\n                <!-- Action Items List -->\r\n                <div class=\"action-items-list\">\r\n                  <div\r\n                    v-for=\"(actionItem, index) in selectedTrackingItem.actionItems\"\r\n                    :key=\"index\"\r\n                    class=\"action-item-card\"\r\n                    :class=\"{ 'completed': actionItem.completed }\"\r\n                  >\r\n                    <!-- Action Item Header -->\r\n                    <div class=\"action-item-header\">\r\n                      <cv-checkbox\r\n                        v-model=\"actionItem.completed\"\r\n                        :label=\"actionItem.title\"\r\n                        @change=\"updateActionItemCompletion(actionItem, index)\"\r\n                      />\r\n                      <span class=\"completion-date\" v-if=\"actionItem.completed && actionItem.completedDate\">\r\n                        Completed on {{ formatDate(actionItem.completedDate) }}\r\n                      </span>\r\n                      <span class=\"last-updated\" v-if=\"actionItem.lastUpdated\">\r\n                        Last updated: {{ formatDate(actionItem.lastUpdated) }}\r\n                      </span>\r\n                    </div>\r\n\r\n                    <!-- Action Item Description -->\r\n                    <div class=\"action-item-description\" v-if=\"actionItem.description\">\r\n                      {{ actionItem.description }}\r\n                    </div>\r\n\r\n                    <!-- Action Item Notes -->\r\n                    <div class=\"action-item-notes\">\r\n                      <div class=\"notes-header\">\r\n                        <h5>Notes</h5>\r\n                        <cv-button\r\n                          size=\"small\"\r\n                          kind=\"ghost\"\r\n                          @click=\"toggleAddNote(index)\"\r\n                        >\r\n                          Add Note\r\n                        </cv-button>\r\n                      </div>\r\n\r\n                      <!-- Add Note Section -->\r\n                      <div v-if=\"actionItem.showAddNote\" class=\"add-note-inline\">\r\n                        <cv-text-area\r\n                          v-model=\"actionItem.newNote\"\r\n                          placeholder=\"Enter note for this action item...\"\r\n                          rows=\"2\"\r\n                        ></cv-text-area>\r\n                        <div class=\"note-actions\">\r\n                          <cv-button\r\n                            kind=\"primary\"\r\n                            size=\"small\"\r\n                            @click=\"addNoteToActionItem(actionItem, index)\"\r\n                            :disabled=\"!actionItem.newNote || !actionItem.newNote.trim()\"\r\n                          >\r\n                            Save Note\r\n                          </cv-button>\r\n                          <cv-button\r\n                            kind=\"secondary\"\r\n                            size=\"small\"\r\n                            @click=\"cancelAddNote(index)\"\r\n                          >\r\n                            Cancel\r\n                          </cv-button>\r\n                        </div>\r\n                      </div>\r\n\r\n                      <!-- Notes List -->\r\n                      <div class=\"notes-list\">\r\n                        <div\r\n                          v-for=\"(note, noteIndex) in actionItem.notes\"\r\n                          :key=\"noteIndex\"\r\n                          class=\"note-item\"\r\n                        >\r\n                          <div class=\"note-header\">\r\n                            <span class=\"note-date\">{{ formatDate(note.date) }}</span>\r\n                            <span class=\"note-author\">by {{ note.author }}</span>\r\n                          </div>\r\n                          <div class=\"note-content\">{{ note.content }}</div>\r\n                        </div>\r\n                        <div v-if=\"!actionItem.notes || actionItem.notes.length === 0\" class=\"no-notes\">\r\n                          No notes for this item yet\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <!-- No Action Items -->\r\n                  <div v-if=\"!selectedTrackingItem.actionItems || selectedTrackingItem.actionItems.length === 0\" class=\"no-action-items\">\r\n                    <p>No action items defined for this tracking item.</p>\r\n                    <cv-button\r\n                      kind=\"primary\"\r\n                      size=\"small\"\r\n                      @click=\"addDefaultActionItems\"\r\n                    >\r\n                      Create Default Action Items\r\n                    </cv-button>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            </cv-tab>\r\n\r\n            <cv-tab id=\"new-alerts-tab\" label=\"New Alerts\">\r\n              <div class=\"tracking-tab-content\">\r\n                <div class=\"new-alerts-section\">\r\n                  <h3 class=\"section-title\">Alert Management</h3>\r\n                  <p class=\"section-description\">Manage alerts and updates for {{ selectedTrackingItem.pn }}</p>\r\n\r\n                  <!-- AI Insight Section -->\r\n                  <div class=\"ai-insight-section\">\r\n                    <h4 class=\"subsection-title\">AI Insight</h4>\r\n                    <div v-if=\"isLoadingAiInsight\" class=\"loading-message\">\r\n                      Generating AI insight...\r\n                    </div>\r\n                    <div v-else class=\"ai-insight-content\">\r\n                      {{ aiInsight || 'No AI insight available for this alert.' }}\r\n                    </div>\r\n                  </div>\r\n\r\n                  <!-- Add New Alert Update -->\r\n                  <div class=\"add-alert-section\">\r\n                    <h4 class=\"subsection-title\">Add Alert Update</h4>\r\n                    <div class=\"add-update-form\">\r\n                      <cv-text-area\r\n                        v-model=\"newAlertUpdate\"\r\n                        label=\"Update Content\"\r\n                        placeholder=\"Enter update details...\"\r\n                        rows=\"4\"\r\n                      ></cv-text-area>\r\n                      <div class=\"update-form-actions\">\r\n                        <cv-button\r\n                          kind=\"primary\"\r\n                          @click=\"addAlertUpdate\"\r\n                          :disabled=\"!newAlertUpdate.trim()\"\r\n                        >\r\n                          Add Update\r\n                        </cv-button>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <!-- Alert History Table -->\r\n                  <div class=\"alert-updates-section\">\r\n                    <h4 class=\"subsection-title\">Alert History</h4>\r\n                    <div v-if=\"alertUpdates.length > 0\" class=\"alert-updates-table\">\r\n                      <div class=\"updates-header\">\r\n                        <div class=\"update-column\">Date</div>\r\n                        <div class=\"update-column\">Update</div>\r\n                        <div class=\"update-column\">Updated By</div>\r\n                      </div>\r\n                      <div\r\n                        v-for=\"(update, index) in alertUpdates\"\r\n                        :key=\"index\"\r\n                        class=\"update-row\"\r\n                      >\r\n                        <div class=\"update-cell\">{{ update.date }}</div>\r\n                        <div class=\"update-cell\">{{ update.update }}</div>\r\n                        <div class=\"update-cell\">{{ update.updatedBy }}</div>\r\n                      </div>\r\n                    </div>\r\n                    <div v-else class=\"no-updates-message\">\r\n                      No alert updates available.\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </cv-tab>\r\n\r\n            <cv-tab id=\"performance-tab\" label=\"Performance Chart & History\">\r\n              <div class=\"tracking-tab-content\">\r\n                <!-- Performance Chart Section -->\r\n                <div class=\"chart-section\">\r\n                  <h3 class=\"section-title\">Performance Chart</h3>\r\n                  <PerformanceChart :trackingItem=\"selectedTrackingItem\" />\r\n                </div>\r\n\r\n                <!-- Performance History Table Section -->\r\n                <div class=\"performance-history-section\">\r\n                  <h3 class=\"section-title\">Monthly Performance History</h3>\r\n                  <p class=\"section-description\">Historical record of performance and status for {{ selectedTrackingItem.pn }}</p>\r\n\r\n                  <cv-data-table\r\n                    :columns=\"alertHistoryColumns\"\r\n                    :title=\"''\"\r\n                    class=\"alert-history-table\"\r\n                  >\r\n                    <template slot=\"data\">\r\n                      <cv-data-table-row\r\n                        v-for=\"(record, index) in alertHistoryData\"\r\n                        :key=\"index\"\r\n                        :class=\"getAlertRowClass(record)\"\r\n                      >\r\n                        <cv-data-table-cell>{{ record.month }}</cv-data-table-cell>\r\n                        <cv-data-table-cell>{{ record.year }}</cv-data-table-cell>\r\n                        <cv-data-table-cell>\r\n                          <div class=\"status-cell\">\r\n                            <span class=\"status-indicator\" :class=\"record.status.toLowerCase()\"></span>\r\n                            <span class=\"status-text\" :class=\"record.status.toLowerCase()\">{{ record.status }}</span>\r\n                          </div>\r\n                        </cv-data-table-cell>\r\n                        <cv-data-table-cell>{{ record.actualRate }}%</cv-data-table-cell>\r\n                        <cv-data-table-cell>{{ record.targetRate }}%</cv-data-table-cell>\r\n                        <cv-data-table-cell>{{ record.xFactor }}</cv-data-table-cell>\r\n                        <cv-data-table-cell>{{ record.volume }}</cv-data-table-cell>\r\n                        <cv-data-table-cell>{{ record.defects }}</cv-data-table-cell>\r\n                        <cv-data-table-cell>{{ record.notes || 'N/A' }}</cv-data-table-cell>\r\n                      </cv-data-table-row>\r\n                    </template>\r\n                  </cv-data-table>\r\n\r\n                  <div v-if=\"!alertHistoryData || alertHistoryData.length === 0\" class=\"no-alert-data\">\r\n                    <p>No performance history available for this part</p>\r\n                    <p class=\"note\">Performance history will be populated as data becomes available</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </cv-tab>\r\n          </cv-tabs>\r\n        </div>\r\n      </template>\r\n    </cv-modal>\r\n  </div>\r\n</template>\r\n\r\n\r\n\r\n<script>\r\nimport MainHeader from '@/components/MainHeader'; // Import the MainHeader component\r\nimport ActionItemsTable from '@/components/ActionTracker/ActionItemsTable.vue'; // Import the ActionItemsTable component\r\n// import LineChart from '@/components/LineChart/LineChart.vue'; // Import the LineChart component\r\nimport PerformanceChart from '@/components/PerformanceChart/PerformanceChart.vue'; // Import the PerformanceChart component\r\n\r\nexport default {\r\n  name: 'ActionTracker',\r\n  components: {\r\n    MainHeader,\r\n    ActionItemsTable,\r\n    // LineChart,\r\n    PerformanceChart\r\n  },\r\n  data() {\r\n    return {\r\n      // Dynamic columns based on status type\r\n      inProgressColumns: ['Priority', 'Commodity', 'Group', 'PN', 'Test', 'Deadline', 'Date Started', 'Assignee', 'Tracking'],\r\n      monitoredColumns: ['Priority', 'Commodity', 'Group', 'PN', 'Test', 'Monitor Date', 'Date Started', 'Assignee', 'Tracking'],\r\n      resolvedColumns: ['Priority', 'Commodity', 'Group', 'PN', 'Test', 'Resolution Date', 'Date Started', 'Assignee', 'Tracking'],\r\n      issueColumns: ['Issue ID', 'Description', 'Severity', 'Date Reported'],\r\n      historyColumns: ['Date', 'Action', 'Updated By'],\r\n      alertHistoryColumns: ['Month', 'Year', 'Status', 'Actual Rate', 'Target Rate', 'X-Factor', 'Volume', 'Defects', 'Notes'],\r\n      modalVisible: false,\r\n      newActionModalVisible: false,\r\n      trackingModalVisible: false,\r\n      selectedRow: null,\r\n      selectedTrackingItem: null,\r\n      searchQuery: '',\r\n      commodityFilter: 'All',\r\n      assigneeFilter: 'All',\r\n      commodityOptions: ['All', 'FUL', 'FAB', 'Power', 'Cable', 'Memory'],\r\n      assigneeOptions: ['All'],\r\n      selectedTab: 'in-progress',\r\n      expandedSideNav: false,\r\n      useFixed: true,\r\n      isLoading: false,\r\n      loadingError: null,\r\n      newAction: {\r\n        commodity: '',\r\n        group: '',\r\n        pn: '',\r\n        test: '',\r\n        deadline: '',\r\n        expectedResolution: '',\r\n        expectedImprovements: '',\r\n        progress: 0,\r\n        status: 'Current',\r\n        priority: 'Medium',\r\n        assignee: '',\r\n        action: '',\r\n        notes: '',\r\n        source: 'manual' // 'manual' or 'pqe'\r\n      },\r\n      newUpdate: '',\r\n      newTrackingUpdate: '',\r\n      newActionNote: '',\r\n      performanceChartData: [],\r\n      alertHistoryData: [],\r\n\r\n      // New Alert functionality\r\n      newAlertUpdate: '',\r\n      alertUpdates: [],\r\n      aiInsight: '',\r\n      isLoadingAiInsight: false,\r\n      performanceChartOptions: {\r\n        title: 'Performance vs Target',\r\n        axes: {\r\n          bottom: {\r\n            title: 'Date',\r\n            mapsTo: 'date',\r\n            scaleType: 'time'\r\n          },\r\n          left: {\r\n            title: 'Performance (%)',\r\n            mapsTo: 'value'\r\n          }\r\n        },\r\n        curve: 'curveMonotoneX',\r\n        height: '300px'\r\n      },\r\n      rows: [],\r\n    };\r\n  },\r\n  computed: {\r\n    // Filter rows based on search and filters\r\n    filteredRows() {\r\n      return this.rows.filter(row => {\r\n        // Filter by commodity\r\n        if (this.commodityFilter !== 'All' && row.commodity !== this.commodityFilter) {\r\n          return false;\r\n        }\r\n\r\n        // Filter by assignee\r\n        if (this.assigneeFilter !== 'All' && row.assignee !== this.assigneeFilter) {\r\n          return false;\r\n        }\r\n\r\n        // Filter by search query\r\n        if (this.searchQuery) {\r\n          const query = this.searchQuery.toLowerCase();\r\n          return (\r\n            row.commodity.toLowerCase().includes(query) ||\r\n            row.group.toLowerCase().includes(query) ||\r\n            row.pn.toLowerCase().includes(query) ||\r\n            row.action.toLowerCase().includes(query) ||\r\n            row.assignee.toLowerCase().includes(query)\r\n          );\r\n        }\r\n\r\n        return true;\r\n      });\r\n    },\r\n\r\n    // In-progress items - actively being worked on (includes current items)\r\n    inProgressItems() {\r\n      return this.filteredRows.filter(row =>\r\n        row.status === 'In-Progress' || row.status === 'Active' || row.status === 'Working' ||\r\n        row.status === 'Current' || row.status === 'New' || row.status === 'Open'\r\n      );\r\n    },\r\n\r\n    // Monitored items - items being tracked but not actively worked\r\n    monitoredItems() {\r\n      return this.filteredRows.filter(row =>\r\n        row.status === 'Monitored' || row.status === 'Watching' || row.status === 'Blocked'\r\n      );\r\n    },\r\n\r\n    // Resolved items - completed or closed items\r\n    resolvedItems() {\r\n      return this.filteredRows.filter(row =>\r\n        row.status === 'Resolved' || row.status === 'Completed' || row.status === 'Closed'\r\n      );\r\n    }\r\n  },\r\n  mounted() {\r\n    // Load action tracker data from API\r\n    this.loadActionTrackerData();\r\n\r\n    // Check if we have query parameters to create a new action\r\n    const query = this.$route.query;\r\n    if (query.createAction === 'true') {\r\n      // Populate the new action form with data from query parameters\r\n      this.newAction = {\r\n        commodity: query.commodity || this.commodityOptions.filter(item => item !== 'All')[0] || '',\r\n        group: query.group || '',\r\n        pn: query.pn || '',\r\n        test: query.test || '',\r\n        deadline: query.deadline || new Date().toISOString().split('T')[0],\r\n        status: query.status || 'Current',\r\n        priority: query.priority || 'Medium',\r\n        assignee: query.assignee || '',\r\n        action: query.action || '',\r\n        notes: query.notes || '',\r\n        source: 'pqe' // Mark as coming from PQE dashboard\r\n      };\r\n\r\n      // Open the new action modal\r\n      this.newActionModalVisible = true;\r\n    }\r\n\r\n    // Check if we have query parameters for alert navigation\r\n    if (query.actionTrackerId && query.tab === 'alerts') {\r\n      // Find the action tracker item and open the tracking modal with alerts tab\r\n      this.$nextTick(() => {\r\n        this.openAlertForActionTracker(query.actionTrackerId, query.alertId);\r\n      });\r\n    }\r\n  },\r\n  methods: {\r\n    // Get authentication config for API calls\r\n    getAuthConfig() {\r\n      const token = localStorage.getItem('token');\r\n      return {\r\n        headers: {\r\n          'Authorization': `Bearer ${token}`\r\n        }\r\n      };\r\n    },\r\n\r\n    // Handle tab selection\r\n    onTabSelected(tabId) {\r\n      this.selectedTab = tabId;\r\n      console.log('Tab selected:', tabId);\r\n    },\r\n\r\n    // Handle item selection from table\r\n    handleItemSelected(item) {\r\n      this.selectedRow = item;\r\n      this.modalVisible = true;\r\n      console.log('Item selected:', item);\r\n    },\r\n\r\n    // Handle item update from table\r\n    handleUpdateItem(item) {\r\n      console.log('Update item:', item);\r\n      this.updateActionItem(item);\r\n    },\r\n\r\n    // Handle tracking item from table\r\n    handleTrackItem(item) {\r\n      console.log('Track item:', item);\r\n      this.selectedTrackingItem = item;\r\n      this.loadPerformanceData(item);\r\n      this.loadAlertHistoryData(item);\r\n      this.loadAlertUpdates(item);\r\n      this.loadAiInsight(item);\r\n      this.trackingModalVisible = true;\r\n    },\r\n\r\n    // Export data functionality\r\n    exportData() {\r\n      console.log('Exporting action tracker data...');\r\n      // TODO: Implement export functionality\r\n      alert('Export functionality will be implemented soon.');\r\n    },\r\n\r\n    // Update assignee options based on loaded data\r\n    updateAssigneeOptions() {\r\n      const assignees = [...new Set(this.rows.map(row => row.assignee).filter(assignee => assignee))];\r\n      this.assigneeOptions = ['All', ...assignees.sort()];\r\n    },\r\n\r\n    // Load performance data for tracking\r\n    async loadPerformanceData(item) {\r\n      try {\r\n        console.log('Loading performance data for:', item.pn);\r\n\r\n        // Generate sample performance data for demonstration\r\n        // In a real implementation, this would fetch actual performance data from the API\r\n        const sampleData = this.generateSamplePerformanceData();\r\n        this.performanceChartData = sampleData;\r\n\r\n      } catch (error) {\r\n        console.error('Error loading performance data:', error);\r\n        this.performanceChartData = [];\r\n      }\r\n    },\r\n\r\n    // Generate sample performance data\r\n    generateSamplePerformanceData() {\r\n      const data = [];\r\n      const today = new Date();\r\n      const target = 95; // Target performance percentage\r\n\r\n      // Generate 30 days of sample data\r\n      for (let i = 29; i >= 0; i--) {\r\n        const date = new Date(today);\r\n        date.setDate(date.getDate() - i);\r\n\r\n        // Generate realistic performance data with some variation\r\n        const basePerformance = 85 + Math.random() * 20; // 85-105%\r\n        const actualPerformance = Math.max(70, Math.min(100, basePerformance + (Math.random() - 0.5) * 10));\r\n\r\n        data.push({\r\n          group: 'Actual Performance',\r\n          date: date.toISOString().split('T')[0],\r\n          value: Math.round(actualPerformance * 100) / 100\r\n        });\r\n\r\n        data.push({\r\n          group: 'Target',\r\n          date: date.toISOString().split('T')[0],\r\n          value: target\r\n        });\r\n      }\r\n\r\n      return data;\r\n    },\r\n\r\n    // Load alert history data for tracking\r\n    async loadAlertHistoryData(item) {\r\n      try {\r\n        console.log('Loading alert history data for:', item.pn);\r\n\r\n        // Generate sample alert history data for demonstration\r\n        // In a real implementation, this would fetch actual alert history from the API\r\n        const historyData = this.generateAlertHistoryData();\r\n        this.alertHistoryData = historyData;\r\n\r\n      } catch (error) {\r\n        console.error('Error loading alert history data:', error);\r\n        this.alertHistoryData = [];\r\n      }\r\n    },\r\n\r\n    // Generate sample alert history data\r\n    generateAlertHistoryData() {\r\n      const data = [];\r\n      const currentDate = new Date();\r\n      const targetRate = 2.5; // Default target rate percentage\r\n\r\n      // Generate 12 months of historical data\r\n      for (let i = 11; i >= 0; i--) {\r\n        const date = new Date(currentDate);\r\n        date.setMonth(date.getMonth() - i);\r\n\r\n        const month = date.toLocaleString('default', { month: 'long' });\r\n        const year = date.getFullYear();\r\n\r\n        // Generate realistic data with some variation\r\n        const volume = Math.floor(Math.random() * 5000) + 1000; // 1000-6000 volume\r\n        const baseDefectRate = targetRate + (Math.random() - 0.5) * 4; // Vary around target\r\n        const actualRate = Math.max(0.1, Math.min(8.0, baseDefectRate)); // 0.1% to 8.0%\r\n        const defects = Math.floor((actualRate / 100) * volume);\r\n        const xFactor = (actualRate / targetRate).toFixed(2);\r\n\r\n        // Determine status based on whether actual rate exceeds target\r\n        const status = actualRate > targetRate ? 'Alert' : 'Normal';\r\n\r\n        // Generate notes for alert conditions\r\n        let notes = null;\r\n        if (status === 'Alert') {\r\n          const alertReasons = [\r\n            'Exceeded target threshold',\r\n            'Process variation detected',\r\n            'Quality issue identified',\r\n            'Supplier batch issue',\r\n            'Equipment calibration needed',\r\n            'Environmental factors'\r\n          ];\r\n          notes = alertReasons[Math.floor(Math.random() * alertReasons.length)];\r\n        }\r\n\r\n        data.push({\r\n          month,\r\n          year,\r\n          status,\r\n          actualRate: actualRate.toFixed(2),\r\n          targetRate: targetRate.toFixed(2),\r\n          xFactor,\r\n          volume: volume.toLocaleString(),\r\n          defects,\r\n          notes\r\n        });\r\n      }\r\n\r\n      return data.reverse(); // Most recent first\r\n    },\r\n\r\n    // Get CSS class for alert history rows\r\n    getAlertRowClass(record) {\r\n      const classes = ['alert-history-row'];\r\n      if (record.status === 'Alert') {\r\n        classes.push('alert-row');\r\n      } else {\r\n        classes.push('normal-row');\r\n      }\r\n      return classes.join(' ');\r\n    },\r\n\r\n    // Update individual action item completion status\r\n    async updateActionItemCompletion(actionItem) {\r\n      try {\r\n        const currentDate = new Date().toISOString().split('T')[0];\r\n\r\n        // Update completion date and last updated\r\n        if (actionItem.completed && !actionItem.completedDate) {\r\n          actionItem.completedDate = currentDate;\r\n        } else if (!actionItem.completed) {\r\n          actionItem.completedDate = null;\r\n        }\r\n        actionItem.lastUpdated = currentDate;\r\n\r\n        // Sort action items by last updated (most recent first)\r\n        this.selectedTrackingItem.actionItems.sort((a, b) => {\r\n          const dateA = new Date(a.lastUpdated || a.completedDate || '1970-01-01');\r\n          const dateB = new Date(b.lastUpdated || b.completedDate || '1970-01-01');\r\n          return dateB - dateA;\r\n        });\r\n\r\n        // Update the action in the API\r\n        const updateData = {\r\n          id: this.selectedTrackingItem.id,\r\n          actionItems: this.selectedTrackingItem.actionItems\r\n        };\r\n        await this.updateActionItem(updateData);\r\n\r\n        // Add an update about the completion status change\r\n        const statusMessage = actionItem.completed ?\r\n          `Action item \"${actionItem.title}\" marked as completed` :\r\n          `Action item \"${actionItem.title}\" marked as incomplete`;\r\n\r\n        await this.addUpdate(this.selectedTrackingItem, statusMessage);\r\n\r\n        console.log('Action item completion status updated:', actionItem);\r\n\r\n      } catch (error) {\r\n        console.error('Error updating action item completion:', error);\r\n        alert(`Failed to update completion status: ${error.message}`);\r\n        // Revert the checkbox state on error\r\n        actionItem.completed = !actionItem.completed;\r\n      }\r\n    },\r\n\r\n    // Toggle add note section for action item\r\n    toggleAddNote(index) {\r\n      const actionItem = this.selectedTrackingItem.actionItems[index];\r\n      actionItem.showAddNote = !actionItem.showAddNote;\r\n      if (actionItem.showAddNote) {\r\n        actionItem.newNote = '';\r\n      }\r\n    },\r\n\r\n    // Cancel add note\r\n    cancelAddNote(index) {\r\n      const actionItem = this.selectedTrackingItem.actionItems[index];\r\n      actionItem.showAddNote = false;\r\n      actionItem.newNote = '';\r\n    },\r\n\r\n    // Add note to specific action item\r\n    async addNoteToActionItem(actionItem) {\r\n      try {\r\n        if (!actionItem.newNote || !actionItem.newNote.trim()) {\r\n          alert('Please enter a note');\r\n          return;\r\n        }\r\n\r\n        const noteData = {\r\n          date: new Date().toISOString().split('T')[0],\r\n          content: actionItem.newNote.trim(),\r\n          author: 'Current User' // In real implementation, get from auth\r\n        };\r\n\r\n        // Initialize notes array if it doesn't exist\r\n        if (!actionItem.notes) {\r\n          actionItem.notes = [];\r\n        }\r\n\r\n        // Add note to the local data (newest first)\r\n        actionItem.notes.unshift(noteData);\r\n\r\n        // Update last updated timestamp\r\n        actionItem.lastUpdated = noteData.date;\r\n\r\n        // Sort action items by last updated (most recent first)\r\n        this.selectedTrackingItem.actionItems.sort((a, b) => {\r\n          const dateA = new Date(a.lastUpdated || a.completedDate || '1970-01-01');\r\n          const dateB = new Date(b.lastUpdated || b.completedDate || '1970-01-01');\r\n          return dateB - dateA;\r\n        });\r\n\r\n        // Save to API\r\n        const updateData = {\r\n          id: this.selectedTrackingItem.id,\r\n          actionItems: this.selectedTrackingItem.actionItems\r\n        };\r\n        await this.updateActionItem(updateData);\r\n\r\n        // Add general update\r\n        await this.addUpdate(this.selectedTrackingItem, `Note added to \"${actionItem.title}\": ${actionItem.newNote.trim()}`);\r\n\r\n        // Clear the input and hide add note section\r\n        actionItem.newNote = '';\r\n        actionItem.showAddNote = false;\r\n\r\n        console.log('Note added to action item:', noteData);\r\n\r\n      } catch (error) {\r\n        console.error('Error adding note to action item:', error);\r\n        alert(`Failed to add note: ${error.message}`);\r\n      }\r\n    },\r\n\r\n    // Add default action items\r\n    addDefaultActionItems() {\r\n      if (!this.selectedTrackingItem.actionItems) {\r\n        this.selectedTrackingItem.actionItems = [];\r\n      }\r\n\r\n      const defaultItems = [\r\n        {\r\n          title: 'Initial Analysis',\r\n          description: 'Perform initial analysis and assessment',\r\n          completed: false,\r\n          completedDate: null,\r\n          lastUpdated: new Date().toISOString().split('T')[0],\r\n          notes: [],\r\n          showAddNote: false,\r\n          newNote: ''\r\n        },\r\n        {\r\n          title: 'Implementation',\r\n          description: 'Implement the proposed solution',\r\n          completed: false,\r\n          completedDate: null,\r\n          lastUpdated: new Date().toISOString().split('T')[0],\r\n          notes: [],\r\n          showAddNote: false,\r\n          newNote: ''\r\n        },\r\n        {\r\n          title: 'Testing & Validation',\r\n          description: 'Test and validate the implementation',\r\n          completed: false,\r\n          completedDate: null,\r\n          lastUpdated: new Date().toISOString().split('T')[0],\r\n          notes: [],\r\n          showAddNote: false,\r\n          newNote: ''\r\n        }\r\n      ];\r\n\r\n      this.selectedTrackingItem.actionItems.push(...defaultItems);\r\n    },\r\n\r\n    // Add tracking update (kept for backward compatibility)\r\n    async addTrackingUpdate() {\r\n      try {\r\n        if (!this.newTrackingUpdate.trim()) {\r\n          alert('Please enter an update description');\r\n          return;\r\n        }\r\n\r\n        const updateData = {\r\n          id: this.selectedTrackingItem.id,\r\n          update: {\r\n            date: new Date().toISOString().split('T')[0],\r\n            content: this.newTrackingUpdate.trim(),\r\n            updatedBy: 'Current User' // In real implementation, get from auth\r\n          }\r\n        };\r\n\r\n        // Add update to the API\r\n        await this.addUpdate(this.selectedTrackingItem, this.newTrackingUpdate.trim());\r\n\r\n        // Update the local data\r\n        if (!this.selectedTrackingItem.updates) {\r\n          this.selectedTrackingItem.updates = [];\r\n        }\r\n        this.selectedTrackingItem.updates.unshift(updateData.update);\r\n\r\n        // Clear the input\r\n        this.newTrackingUpdate = '';\r\n\r\n        // Show success message\r\n        alert('Update added successfully!');\r\n\r\n      } catch (error) {\r\n        console.error('Error adding tracking update:', error);\r\n        alert(`Failed to add update: ${error.message}`);\r\n      }\r\n    },\r\n\r\n    // Load action tracker data from API\r\n    async loadActionTrackerData() {\r\n      try {\r\n        this.isLoading = true;\r\n        this.loadingError = null;\r\n        console.log('Loading action tracker data from API...');\r\n\r\n        // Make API call to get action tracker data\r\n        const response = await fetch('/api-statit2/get_action_tracker_data', {\r\n          method: 'POST',\r\n          headers: {\r\n            'Content-Type': 'application/json'\r\n          },\r\n          body: JSON.stringify({})\r\n        });\r\n\r\n        // Check if response is ok\r\n        if (!response.ok) {\r\n          throw new Error(`Failed to fetch action tracker data: ${response.status} ${response.statusText}`);\r\n        }\r\n\r\n        // Parse response data\r\n        const data = await response.json();\r\n        console.log('Action tracker data loaded:', data);\r\n\r\n        // Check if data has items property\r\n        if (data.items) {\r\n          // Format the data for display\r\n          this.rows = data.items.map(item => ({\r\n            id: item.id,\r\n            commodity: item.commodity,\r\n            group: item.group,\r\n            pn: item.pn,\r\n            editableTest: item.test || 'N/A',\r\n            deadline: item.deadline || '',\r\n            expectedResolution: item.expectedResolution || '',\r\n            expectedImprovements: item.expectedImprovements || '',\r\n            dateStarted: item.dateStarted || '',\r\n            monitorDate: item.monitorDate || '',\r\n            resolutionDate: item.resolutionDate || '',\r\n            progress: item.progress || 0,\r\n            priority: item.priority || 'Medium',\r\n            source: item.source || 'manual',\r\n            completed: item.completed || false,\r\n            completedDate: item.completedDate || null,\r\n            isEditingTest: false,\r\n            isEditingDL: false,\r\n            isEditingER: false,\r\n            action: item.action,\r\n            status: item.status,\r\n            assignee: item.assignee,\r\n            notes: item.actionNotes || [], // Action-specific notes\r\n            actionItems: item.actionItems || [], // Individual action items\r\n            issues: item.issues || [],\r\n            updates: item.updates || [],\r\n            createdAt: item.createdAt,\r\n            updatedAt: item.updatedAt\r\n          }));\r\n\r\n          // Update assignee options\r\n          this.updateAssigneeOptions();\r\n        } else {\r\n          this.rows = [];\r\n          console.warn('No items found in action tracker data');\r\n        }\r\n      } catch (error) {\r\n        console.error('Error loading action tracker data:', error);\r\n        this.loadingError = error.message;\r\n        this.rows = [];\r\n      } finally {\r\n        this.isLoading = false;\r\n      }\r\n    },\r\n\r\n    // Save action tracker data to API\r\n    async saveActionTrackerData(action) {\r\n      try {\r\n        console.log('Saving action tracker data to API:', action);\r\n\r\n        // Make API call to save action tracker data\r\n        const response = await fetch('/api-statit2/save_action_tracker_data', {\r\n          method: 'POST',\r\n          headers: {\r\n            'Content-Type': 'application/json',\r\n            'Authorization': this.getAuthConfig().headers.Authorization\r\n          },\r\n          body: JSON.stringify(action)\r\n        });\r\n\r\n        // Check if response is ok\r\n        if (!response.ok) {\r\n          throw new Error(`Failed to save action tracker data: ${response.status} ${response.statusText}`);\r\n        }\r\n\r\n        // Parse response data\r\n        const data = await response.json();\r\n        console.log('Action tracker data saved:', data);\r\n\r\n        return data;\r\n      } catch (error) {\r\n        console.error('Error saving action tracker data:', error);\r\n        throw error;\r\n      }\r\n    },\r\n\r\n    handleSeeMore(row) {\r\n      this.selectedRow = row;\r\n      this.modalVisible = true;\r\n      console.log('See more clicked:', row);\r\n    },\r\n\r\n    editTest(row) {\r\n      row.isEditingTest = true;\r\n    },\r\n\r\n    async stopEditingTest(row) {\r\n      row.isEditingTest = false;\r\n      console.log('Test name updated:', row.editableTest);\r\n\r\n      try {\r\n        // Update the action in the API\r\n        await this.saveActionTrackerData({\r\n          id: row.id,\r\n          test: row.editableTest\r\n        });\r\n      } catch (error) {\r\n        alert(`Failed to update test: ${error.message}`);\r\n      }\r\n    },\r\n\r\n    editDL(row) {\r\n      row.isEditingDL = true;\r\n    },\r\n\r\n    async stopEditingDL(row) {\r\n      row.isEditingDL = false;\r\n      console.log('Deadline updated:', row.deadline);\r\n\r\n      try {\r\n        // Update the action in the API\r\n        await this.updateActionItem({\r\n          id: row.id,\r\n          deadline: row.deadline\r\n        });\r\n      } catch (error) {\r\n        alert(`Failed to update deadline: ${error.message}`);\r\n      }\r\n    },\r\n\r\n    editER(row) {\r\n      row.isEditingER = true;\r\n    },\r\n\r\n    async stopEditingER(row) {\r\n      row.isEditingER = false;\r\n      console.log('Expected resolution updated:', row.expectedResolution);\r\n\r\n      try {\r\n        // Update the action in the API\r\n        await this.updateActionItem({\r\n          id: row.id,\r\n          expectedResolution: row.expectedResolution\r\n        });\r\n      } catch (error) {\r\n        alert(`Failed to update expected resolution: ${error.message}`);\r\n      }\r\n    },\r\n\r\n    // Format date for display\r\n    formatDate(dateString) {\r\n      if (!dateString) return 'N/A';\r\n\r\n      // Check if the date is in YYYY-MM-DD format\r\n      if (/^\\d{4}-\\d{2}-\\d{2}$/.test(dateString)) {\r\n        const [year, month, day] = dateString.split('-');\r\n        return `${month}/${day}/${year}`;\r\n      }\r\n\r\n      // If it's in MM/DD/YYYY format, return as is\r\n      return dateString;\r\n    },\r\n\r\n    // Update an action item\r\n    async updateActionItem(actionData) {\r\n      try {\r\n        console.log('Updating action item:', actionData);\r\n\r\n        // Make API call to update action tracker data\r\n        const response = await fetch('/api-statit2/update_action_tracker_data', {\r\n          method: 'POST',\r\n          headers: {\r\n            'Content-Type': 'application/json',\r\n            'Authorization': this.getAuthConfig().headers.Authorization\r\n          },\r\n          body: JSON.stringify(actionData)\r\n        });\r\n\r\n        // Check if response is ok\r\n        if (!response.ok) {\r\n          throw new Error(`Failed to update action item: ${response.status} ${response.statusText}`);\r\n        }\r\n\r\n        // Parse response data\r\n        const data = await response.json();\r\n        console.log('Action item updated:', data);\r\n\r\n        // Reload the action tracker data to get the updated list\r\n        await this.loadActionTrackerData();\r\n\r\n        return data;\r\n      } catch (error) {\r\n        console.error('Error updating action item:', error);\r\n        throw error;\r\n      }\r\n    },\r\n\r\n    truncateText(text, maxLength) {\r\n      if (!text) return '';\r\n      if (text.length <= maxLength) return text;\r\n      return text.slice(0, maxLength) + '...';\r\n    },\r\n\r\n    // Update progress for an action item\r\n    async updateProgress(row) {\r\n      try {\r\n        console.log(`Updating progress for ${row.id} to ${row.progress}%`);\r\n\r\n        // Update the action in the API\r\n        await this.updateActionItem({\r\n          id: row.id,\r\n          progress: row.progress\r\n        });\r\n\r\n        // Add an update about the progress change\r\n        await this.addUpdate(row, `Progress updated to ${row.progress}%`);\r\n\r\n        // Show success message\r\n        alert('Progress updated successfully!');\r\n      } catch (error) {\r\n        console.error('Error updating progress:', error);\r\n        alert(`Failed to update progress: ${error.message}`);\r\n      }\r\n    },\r\n\r\n    // Update the entire action item\r\n    async updateEntireAction(row) {\r\n      try {\r\n        console.log('Updating entire action item:', row);\r\n\r\n        // Create an update object with all editable fields\r\n        const updateData = {\r\n          id: row.id,\r\n          commodity: row.commodity,\r\n          group: row.group,\r\n          pn: row.pn,\r\n          test: row.editableTest,\r\n          deadline: row.deadline,\r\n          expectedResolution: row.expectedResolution,\r\n          expectedImprovements: row.expectedImprovements,\r\n          progress: row.progress,\r\n          status: row.status,\r\n          assignee: row.assignee,\r\n          action: row.action,\r\n          notes: row.notes\r\n        };\r\n\r\n        // Update the action in the API\r\n        await this.updateActionItem(updateData);\r\n\r\n        // Add an update about the action update\r\n        await this.addUpdate(row, 'Action details updated');\r\n\r\n        // Close the modal\r\n        this.modalVisible = false;\r\n\r\n        // Show success message\r\n        alert('Action updated successfully!');\r\n      } catch (error) {\r\n        console.error('Error updating action:', error);\r\n        alert(`Failed to update action: ${error.message}`);\r\n      }\r\n    },\r\n\r\n    // Add an update to an action item\r\n    async addUpdate(row, content) {\r\n      try {\r\n        const updateContent = content || this.newUpdate;\r\n\r\n        if (!updateContent) {\r\n          alert('Please enter update content');\r\n          return;\r\n        }\r\n\r\n        console.log(`Adding update to ${row.id}: ${updateContent}`);\r\n\r\n        // Make API call to add update\r\n        const response = await fetch('/api-statit2/add_action_update', {\r\n          method: 'POST',\r\n          headers: {\r\n            'Content-Type': 'application/json',\r\n            'Authorization': this.getAuthConfig().headers.Authorization\r\n          },\r\n          body: JSON.stringify({\r\n            id: row.id,\r\n            update: {\r\n              content: updateContent,\r\n              updatedBy: 'Current User' // In a real app, this would be the logged-in user\r\n            }\r\n          })\r\n        });\r\n\r\n        // Check if response is ok\r\n        if (!response.ok) {\r\n          throw new Error(`Failed to add update: ${response.status} ${response.statusText}`);\r\n        }\r\n\r\n        // Parse response data\r\n        const data = await response.json();\r\n        console.log('Update added:', data);\r\n\r\n        // Clear the update input\r\n        this.newUpdate = '';\r\n\r\n        // Reload the action tracker data to get the updated list\r\n        await this.loadActionTrackerData();\r\n\r\n        // Update the selected row with the latest data\r\n        if (this.selectedRow) {\r\n          const updatedRow = this.rows.find(r => r.id === this.selectedRow.id);\r\n          if (updatedRow) {\r\n            this.selectedRow = updatedRow;\r\n          }\r\n        }\r\n\r\n        // Show success message if this was a manual update\r\n        if (!content) {\r\n          alert('Update added successfully!');\r\n        }\r\n\r\n        return data;\r\n      } catch (error) {\r\n        console.error('Error adding update:', error);\r\n        alert(`Failed to add update: ${error.message}`);\r\n        throw error;\r\n      }\r\n    },\r\n\r\n    openNewActionModal() {\r\n      // Reset the form\r\n      this.newAction = {\r\n        commodity: this.commodityOptions.filter(item => item !== 'All')[0] || '',\r\n        group: '',\r\n        pn: '',\r\n        test: '',\r\n        deadline: new Date().toISOString().split('T')[0],\r\n        status: 'In-Progress',\r\n        assignee: '',\r\n        action: '',\r\n        notes: ''\r\n      };\r\n      // Open the modal\r\n      this.newActionModalVisible = true;\r\n    },\r\n\r\n    async createNewAction() {\r\n      // Validate required fields\r\n      const requiredFields = ['commodity', 'group', 'action'];\r\n      const missingFields = requiredFields.filter(field => !this.newAction[field]);\r\n\r\n      if (missingFields.length > 0) {\r\n        // Show error message for missing fields\r\n        const fieldNames = missingFields.map(field => {\r\n          switch(field) {\r\n            case 'commodity': return 'Process/Commodity';\r\n            case 'group': return 'Part Group';\r\n            case 'action': return 'Action Description';\r\n            default: return field;\r\n          }\r\n        });\r\n\r\n        alert(`Please fill in the required fields: ${fieldNames.join(', ')}`);\r\n        return;\r\n      }\r\n\r\n      try {\r\n        // Ensure deadline is set\r\n        if (!this.newAction.deadline) {\r\n          // Set default deadline to 30 days from now if not provided\r\n          const defaultDate = new Date();\r\n          defaultDate.setDate(defaultDate.getDate() + 30);\r\n          this.newAction.deadline = `${defaultDate.getFullYear()}-${String(defaultDate.getMonth() + 1).padStart(2, '0')}-${String(defaultDate.getDate()).padStart(2, '0')}`;\r\n        }\r\n\r\n        // Format the expected resolution date\r\n        let formattedExpectedResolution = '';\r\n        if (this.newAction.expectedResolution) {\r\n          try {\r\n            const expectedResolutionDate = new Date(this.newAction.expectedResolution);\r\n            formattedExpectedResolution = `${expectedResolutionDate.getFullYear()}-${String(expectedResolutionDate.getMonth() + 1).padStart(2, '0')}-${String(expectedResolutionDate.getDate()).padStart(2, '0')}`;\r\n          } catch (error) {\r\n            console.error('Error formatting expected resolution date:', error);\r\n            formattedExpectedResolution = this.newAction.expectedResolution; // Use as-is if there's an error\r\n          }\r\n        }\r\n\r\n        // Create the new action object\r\n        const newActionItem = {\r\n          commodity: this.newAction.commodity,\r\n          group: this.newAction.group,\r\n          pn: this.newAction.pn || `${this.newAction.group}-${Date.now().toString().slice(-6)}`, // Generate a PN if not provided\r\n          test: this.newAction.test || 'N/A',\r\n          deadline: this.newAction.deadline,\r\n          expectedResolution: formattedExpectedResolution,\r\n          expectedImprovements: this.newAction.expectedImprovements || '',\r\n          progress: this.newAction.progress || 0,\r\n          action: this.newAction.action,\r\n          status: this.newAction.status || 'In-Progress',\r\n          assignee: this.newAction.assignee || 'Unassigned',\r\n          notes: this.newAction.notes || ''\r\n        };\r\n\r\n        // Save the new action to the API\r\n        const savedAction = await this.saveActionTrackerData(newActionItem);\r\n        console.log('New action saved to API:', savedAction);\r\n\r\n        // Reload the action tracker data to get the updated list\r\n        await this.loadActionTrackerData();\r\n\r\n        // Close the modal\r\n        this.newActionModalVisible = false;\r\n\r\n        // Show a success message\r\n        alert('Action item created successfully!');\r\n      } catch (error) {\r\n        console.error('Error creating new action:', error);\r\n        alert(`Failed to create action: ${error.message}`);\r\n      }\r\n    },\r\n\r\n    // Open alert for specific action tracker item (called from PQE dashboard navigation)\r\n    async openAlertForActionTracker(actionTrackerId, alertId) {\r\n      try {\r\n        // Wait for data to load\r\n        await this.loadActionTrackerData();\r\n\r\n        // Find the action tracker item\r\n        const item = this.rows.find(row => row.id === actionTrackerId);\r\n        if (item) {\r\n          // Open the tracking modal with the alerts tab\r\n          this.selectedTrackingItem = item;\r\n          this.loadPerformanceData(item);\r\n          this.loadAlertHistoryData(item);\r\n          this.loadAlertUpdates(item);\r\n          this.loadAiInsight(item);\r\n          this.trackingModalVisible = true;\r\n\r\n          // Switch to the alerts tab after modal opens\r\n          this.$nextTick(() => {\r\n            const alertsTab = document.getElementById('new-alerts-tab');\r\n            if (alertsTab) {\r\n              alertsTab.click();\r\n            }\r\n          });\r\n        } else {\r\n          console.error('Action tracker item not found:', actionTrackerId);\r\n        }\r\n      } catch (error) {\r\n        console.error('Error opening alert for action tracker:', error);\r\n      }\r\n    },\r\n\r\n    // Load alert updates for the selected item\r\n    async loadAlertUpdates(item) {\r\n      try {\r\n        const response = await fetch('/api-statit2/get_pqe_alert_history', {\r\n          method: 'POST',\r\n          headers: {\r\n            'Content-Type': 'application/json',\r\n            ...this.getAuthConfig().headers\r\n          },\r\n          body: JSON.stringify({\r\n            actionTrackerId: item.id,\r\n            pqeOwner: this.$route.query.pqeOwner || 'Unknown'\r\n          })\r\n        });\r\n\r\n        if (!response.ok) {\r\n          throw new Error(`Failed to fetch alert history: ${response.status}`);\r\n        }\r\n\r\n        const data = await response.json();\r\n        if (data.status_res === 'success') {\r\n          this.alertUpdates = data.alert_history || [];\r\n        } else {\r\n          console.error('Failed to load alert updates:', data.message);\r\n          this.alertUpdates = [];\r\n        }\r\n      } catch (error) {\r\n        console.error('Error loading alert updates:', error);\r\n        this.alertUpdates = [];\r\n      }\r\n    },\r\n\r\n    // Load AI insight for the selected item\r\n    async loadAiInsight(item) {\r\n      this.isLoadingAiInsight = true;\r\n      try {\r\n        const alertData = {\r\n          category: item.group || 'Unknown',\r\n          severity: 'Medium', // Default severity\r\n          xFactor: '1.5', // Default X-factor\r\n          status: item.status || 'Unknown'\r\n        };\r\n\r\n        const response = await fetch('/api-statit2/get_pqe_alert_ai_insight', {\r\n          method: 'POST',\r\n          headers: {\r\n            'Content-Type': 'application/json',\r\n            ...this.getAuthConfig().headers\r\n          },\r\n          body: JSON.stringify({\r\n            actionTrackerId: item.id,\r\n            alertData: alertData,\r\n            pqeOwner: this.$route.query.pqeOwner || 'Unknown'\r\n          })\r\n        });\r\n\r\n        if (!response.ok) {\r\n          throw new Error(`Failed to fetch AI insight: ${response.status}`);\r\n        }\r\n\r\n        const data = await response.json();\r\n        if (data.status_res === 'success') {\r\n          this.aiInsight = data.ai_insight || 'No insight available';\r\n        } else {\r\n          console.error('Failed to load AI insight:', data.message);\r\n          this.aiInsight = 'Unable to generate insight at this time';\r\n        }\r\n      } catch (error) {\r\n        console.error('Error loading AI insight:', error);\r\n        this.aiInsight = 'Unable to generate insight at this time';\r\n      } finally {\r\n        this.isLoadingAiInsight = false;\r\n      }\r\n    },\r\n\r\n    // Add alert update\r\n    async addAlertUpdate() {\r\n      if (!this.newAlertUpdate.trim()) {\r\n        alert('Please enter an update');\r\n        return;\r\n      }\r\n\r\n      try {\r\n        const response = await fetch('/api-statit2/add_pqe_alert_update', {\r\n          method: 'POST',\r\n          headers: {\r\n            'Content-Type': 'application/json',\r\n            ...this.getAuthConfig().headers\r\n          },\r\n          body: JSON.stringify({\r\n            actionTrackerId: this.selectedTrackingItem.id,\r\n            update: this.newAlertUpdate,\r\n            updatedBy: this.$route.query.pqeOwner || 'Unknown',\r\n            alertType: 'PQE Update'\r\n          })\r\n        });\r\n\r\n        if (!response.ok) {\r\n          throw new Error(`Failed to add alert update: ${response.status}`);\r\n        }\r\n\r\n        const data = await response.json();\r\n        if (data.status_res === 'success') {\r\n          // Add the new alert to the history\r\n          this.alertUpdates.push(data.alert);\r\n          this.newAlertUpdate = '';\r\n          console.log('Alert update added successfully');\r\n        } else {\r\n          throw new Error(data.message || 'Failed to add alert update');\r\n        }\r\n      } catch (error) {\r\n        console.error('Error adding alert update:', error);\r\n        alert('Failed to add alert update: ' + error.message);\r\n      }\r\n    }\r\n  },\r\n};\r\n\r\n</script>\r\n"]}]}