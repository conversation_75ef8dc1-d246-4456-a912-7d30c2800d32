{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\PQEDashboard\\PQEDashboardPage.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\PQEDashboard\\PQEDashboardPage.vue", "mtime": 1748987610870}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["PQEDashboardPage.vue"], "names": [], "mappings": ";AA4DA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "PQEDashboardPage.vue", "sourceRoot": "src/views/PQEDashboard", "sourcesContent": ["<template>\n  <div class=\"dashboard-container\">\n    <!-- Inherit the MainHeader component -->\n    <MainHeader :expandedSideNav=\"expandedSideNav\" :useFixed=\"useFixed\" />\n\n    <main class=\"main-content\">\n      <!-- Page Header -->\n      <div class=\"page-header\">\n        <h1 class=\"page-title\">PQE Dashboard</h1>\n      </div>\n\n      <!-- Tabs for PQE Owner and QE Dashboards -->\n      <div class=\"dashboard-tabs\">\n        <cv-tabs aria-label=\"PQE Dashboard Tabs\">\n          <cv-tab id=\"pqe-owner-tab\" label=\"PQE Owner Dashboard\">\n            <div class=\"tab-content\">\n              <!-- PQE Owner Selection -->\n              <div class=\"pqe-selection-container\">\n                <label for=\"pqe-owner-dropdown\" class=\"selection-label\">Select PQE Owner:</label>\n                <cv-dropdown\n                  id=\"pqe-owner-dropdown\"\n                  v-model=\"selectedPQEOwner\"\n                  @change=\"handlePQEOwnerChange\"\n                  class=\"pqe-dropdown\"\n                >\n                  <cv-dropdown-item\n                    v-for=\"owner in pqeOwners\"\n                    :key=\"owner\"\n                    :value=\"owner\"\n                  >\n                    {{ owner }}\n                  </cv-dropdown-item>\n                </cv-dropdown>\n              </div>\n\n              <!-- PQE Owner Dashboard Component -->\n              <PQEOwnerDashboard\n                v-if=\"selectedPQEOwner\"\n                :pqeOwner=\"selectedPQEOwner\"\n                @update-action-tracker=\"updateActionTracker\"\n                @navigate-to-action-tracker=\"navigateToActionTracker\"\n              />\n              <div v-else class=\"select-pqe-message\">\n                Please select a PQE owner to view their dashboard.\n              </div>\n            </div>\n          </cv-tab>\n          <cv-tab id=\"qe-tab\" label=\"QE Dashboard\">\n            <div class=\"tab-content\">\n              <!-- QE Dashboard Component -->\n              <QEDashboard @select-pqe=\"selectPQEFromQEDashboard\" />\n            </div>\n          </cv-tab>\n        </cv-tabs>\n      </div>\n    </main>\n  </div>\n</template>\n\n<script>\nimport MainHeader from '@/components/MainHeader/MainHeader';\nimport PQEOwnerDashboard from './PQEOwnerDashboard';\nimport QEDashboard from './QEDashboard';\nimport { CvTabs, CvTab, CvDropdown, CvDropdownItem } from '@carbon/vue';\n\nexport default {\n  name: 'PQEDashboardPage',\n  components: {\n    MainHeader,\n    PQEOwnerDashboard,\n    QEDashboard,\n    CvTabs,\n    CvTab,\n    CvDropdown,\n    CvDropdownItem\n  },\n  data() {\n    return {\n      expandedSideNav: false,\n      useFixed: false,\n      selectedPQEOwner: '',\n      pqeOwners: [],\n      isLoading: false\n    };\n  },\n  mounted() {\n    this.loadPQEOwners();\n  },\n  methods: {\n    async loadPQEOwners() {\n      this.isLoading = true;\n      try {\n        // Get authentication config\n        const config = this.getAuthConfig();\n\n        // Fetch PQE owners from the API\n        const response = await fetch('/api-statit2/get_pqe_owners', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({})\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch PQE owners: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success') {\n          this.pqeOwners = data.pqe_owners || [];\n          console.log(`Loaded ${this.pqeOwners.length} PQE owners`);\n\n          // If Albert G. is in the list, select him by default\n          const defaultOwner = this.pqeOwners.find(owner => owner === 'Albert G.');\n          if (defaultOwner) {\n            this.selectedPQEOwner = defaultOwner;\n          } else if (this.pqeOwners.length > 0) {\n            // Otherwise select the first owner\n            this.selectedPQEOwner = this.pqeOwners[0];\n          }\n        } else {\n          console.error('Failed to load PQE owners:', data.message);\n        }\n      } catch (error) {\n        console.error('Error loading PQE owners:', error);\n      } finally {\n        this.isLoading = false;\n      }\n    },\n\n    handlePQEOwnerChange() {\n      console.log(`Selected PQE owner: ${this.selectedPQEOwner}`);\n    },\n\n    updateActionTracker(actionData) {\n      console.log('Updating action tracker with:', actionData);\n\n      // Call the API to update the action tracker\n      this.isLoading = true;\n\n      fetch('/api-statit2/update_pqe_action', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          ...this.getAuthConfig().headers\n        },\n        body: JSON.stringify(actionData)\n      })\n        .then(response => {\n          if (!response.ok) {\n            throw new Error(`Failed to update action tracker: ${response.status} ${response.statusText}`);\n          }\n          return response.json();\n        })\n        .then(data => {\n          if (data.status_res === 'success') {\n            console.log('Action tracker updated successfully');\n          } else {\n            console.error('Failed to update action tracker:', data.message);\n          }\n        })\n        .catch(error => {\n          console.error('Error updating action tracker:', error);\n        })\n        .finally(() => {\n          this.isLoading = false;\n        });\n    },\n\n    selectPQEFromQEDashboard(pqeOwner) {\n      console.log(`Selecting PQE owner from QE Dashboard: ${pqeOwner}`);\n\n      // Set the selected PQE owner\n      this.selectedPQEOwner = pqeOwner;\n\n      // Switch to the PQE Owner Dashboard tab\n      // Find the tab element and click it\n      this.$nextTick(() => {\n        const tabElement = document.getElementById('pqe-owner-tab');\n        if (tabElement) {\n          tabElement.click();\n        }\n      });\n    },\n\n    navigateToActionTracker(alertData) {\n      console.log('Navigating to Action Tracker with alert data:', alertData);\n\n      // Navigate to the Action Tracker page\n      // You can use Vue Router to navigate to the action tracker page\n      this.$router.push({\n        name: 'ActionTracker', // Assuming you have a route named 'ActionTracker'\n        query: {\n          alertId: alertData.alertId,\n          actionTrackerId: alertData.actionTrackerId,\n          tab: alertData.tab || 'alerts',\n          pqeOwner: this.selectedPQEOwner\n        }\n      });\n    },\n\n    getAuthConfig() {\n      // Get authentication token from localStorage or Vuex store\n      const token = localStorage.getItem('token') || this.$store.state.token;\n\n      return {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      };\n    }\n  }\n};\n</script>\n\n<style scoped>\n.dashboard-container {\n  min-height: 100vh;\n  background-color: #161616;\n}\n\n.main-content {\n  padding: 2rem;\n  max-width: 1400px;\n  margin: 0 auto;\n}\n\n.page-header {\n  margin-bottom: 2rem;\n}\n\n.page-title {\n  color: #f4f4f4;\n  font-size: 1.75rem;\n  font-weight: 400;\n  margin: 0;\n}\n\n.dashboard-tabs {\n  background-color: #262626;\n  border-radius: 8px;\n  overflow: hidden;\n  border: 1px solid #333333;\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);\n}\n\n.tab-content {\n  padding: 1.5rem;\n}\n\n.pqe-selection-container {\n  display: flex;\n  align-items: center;\n  margin-bottom: 1.5rem;\n  background-color: #333333;\n  padding: 1rem;\n  border-radius: 8px;\n}\n\n.selection-label {\n  color: #f4f4f4;\n  margin-right: 1rem;\n  font-size: 0.875rem;\n  white-space: nowrap;\n}\n\n.pqe-dropdown {\n  width: 300px;\n}\n\n.select-pqe-message {\n  color: #8d8d8d;\n  font-size: 1rem;\n  text-align: center;\n  padding: 2rem;\n  background-color: #333333;\n  border-radius: 8px;\n}\n</style>\n"]}]}