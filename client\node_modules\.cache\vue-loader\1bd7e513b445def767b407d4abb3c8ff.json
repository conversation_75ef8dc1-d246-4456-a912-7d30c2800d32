{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\ActionTracker\\ActionTracker.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\ActionTracker\\ActionTracker.vue", "mtime": 1748988452772}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["ActionTracker.vue"], "names": [], "mappings": ";AA4wDA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "ActionTracker.vue", "sourceRoot": "src/views/ActionTracker", "sourcesContent": ["<style scoped lang=\"scss\">\r\n@import \"../../styles/carbon-utils\";\r\n\r\n.dashboard-container {\r\n  min-height: 100vh;\r\n  background-color: #161616;\r\n}\r\n\r\n.main-content {\r\n  padding: 2rem;\r\n  max-width: 1400px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.page-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 2rem;\r\n  padding-bottom: 1rem;\r\n  border-bottom: 1px solid #333333;\r\n}\r\n\r\n.page-title {\r\n  color: #f4f4f4;\r\n  font-size: 1.75rem;\r\n  font-weight: 400;\r\n  margin: 0;\r\n}\r\n\r\n.action-controls {\r\n  display: flex;\r\n  gap: 1rem;\r\n}\r\n\r\n.filter-bar {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 1.5rem;\r\n  background-color: #262626;\r\n  padding: 1rem;\r\n  border-radius: 8px;\r\n  border: 1px solid #333333;\r\n}\r\n\r\n.filter-group {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 1rem;\r\n}\r\n\r\n.filter-label {\r\n  color: #8d8d8d;\r\n  font-size: 0.875rem;\r\n}\r\n\r\n.search-box {\r\n  flex-grow: 1;\r\n  max-width: 300px;\r\n}\r\n\r\n/* Table styling */\r\n.action-table {\r\n  background-color: #262626;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n  border: 1px solid #333333;\r\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.table-header {\r\n  background-color: #333333;\r\n  padding: 1rem;\r\n  border-bottom: 1px solid #444444;\r\n}\r\n\r\n.table-title {\r\n  color: #f4f4f4;\r\n  font-size: 1.25rem;\r\n  font-weight: 400;\r\n  margin: 0;\r\n}\r\n\r\n/* Cell styling */\r\n.edit-icon {\r\n  margin-left: 8px;\r\n  cursor: pointer;\r\n  color: #0f62fe;\r\n  opacity: 0.8;\r\n  transition: opacity 0.2s ease;\r\n}\r\n\r\n.edit-icon:hover {\r\n  opacity: 1;\r\n}\r\n\r\n.editable-field {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.editable-field input {\r\n  background-color: #333333;\r\n  border: 1px solid #0f62fe;\r\n  color: #f4f4f4;\r\n  padding: 0.25rem 0.5rem;\r\n  border-radius: 4px;\r\n  font-size: 0.875rem;\r\n}\r\n\r\n.action-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n}\r\n\r\n.action-text {\r\n  flex-grow: 1;\r\n}\r\n\r\n.see-more-button {\r\n  background-color: #0f62fe;\r\n  color: #ffffff;\r\n  padding: 0.25rem 0.5rem;\r\n  border-radius: 4px;\r\n  font-size: 0.75rem;\r\n  border: none;\r\n  cursor: pointer;\r\n  transition: background-color 0.2s ease;\r\n  white-space: nowrap;\r\n}\r\n\r\n.see-more-button:hover {\r\n  background-color: #0353e9;\r\n}\r\n\r\n/* Status indicators */\r\n.status-indicator {\r\n  display: inline-block;\r\n  width: 8px;\r\n  height: 8px;\r\n  border-radius: 50%;\r\n  margin-right: 0.5rem;\r\n}\r\n\r\n.status-in-progress {\r\n  background-color: #0f62fe;\r\n}\r\n\r\n.status-completed {\r\n  background-color: #42be65;\r\n}\r\n\r\n.status-blocked {\r\n  background-color: #fa4d56;\r\n}\r\n\r\n/* Status badges */\r\n.status-badge {\r\n  display: inline-flex;\r\n  align-items: center;\r\n  padding: 0.25rem 0.5rem;\r\n  border-radius: 4px;\r\n  font-size: 0.75rem;\r\n  font-weight: 600;\r\n  text-transform: uppercase;\r\n}\r\n\r\n.status-badge.in-progress {\r\n  background-color: rgba(15, 98, 254, 0.2);\r\n  color: #78a9ff;\r\n}\r\n\r\n.status-badge.completed {\r\n  background-color: rgba(66, 190, 101, 0.2);\r\n  color: #6fdc8c;\r\n}\r\n\r\n.status-badge.blocked {\r\n  background-color: rgba(250, 77, 86, 0.2);\r\n  color: #ff8389;\r\n}\r\n\r\n/* Status banner */\r\n.status-banner {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 1rem;\r\n  border-radius: 8px;\r\n  margin-bottom: 1.5rem;\r\n}\r\n\r\n.status-banner-in-progress {\r\n  background-color: rgba(15, 98, 254, 0.1);\r\n  border: 1px solid rgba(15, 98, 254, 0.3);\r\n}\r\n\r\n.status-banner-completed {\r\n  background-color: rgba(66, 190, 101, 0.1);\r\n  border: 1px solid rgba(66, 190, 101, 0.3);\r\n}\r\n\r\n.status-banner-blocked {\r\n  background-color: rgba(250, 77, 86, 0.1);\r\n  border: 1px solid rgba(250, 77, 86, 0.3);\r\n}\r\n\r\n.assignee-info {\r\n  color: #f4f4f4;\r\n  font-size: 0.875rem;\r\n}\r\n\r\n/* Modal actions */\r\n.modal-actions {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  gap: 1rem;\r\n  margin-top: 2rem;\r\n}\r\n\r\n/* Form styling */\r\n.form-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(2, 1fr);\r\n  gap: 1.5rem;\r\n  margin-bottom: 1.5rem;\r\n}\r\n\r\n.form-group {\r\n  display: flex;\r\n  flex-direction: column;\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.form-group.full-width {\r\n  grid-column: span 2;\r\n}\r\n\r\n.form-label {\r\n  font-size: 0.875rem;\r\n  color: #8d8d8d;\r\n  margin-bottom: 0.5rem;\r\n}\r\n\r\n/* Modal styles */\r\n.action-modal {\r\n  max-width: 800px;\r\n}\r\n\r\n.modal-content {\r\n  padding: 1.5rem 0;\r\n  color: #f4f4f4;\r\n}\r\n\r\n.modal-section {\r\n  margin-bottom: 2rem;\r\n}\r\n\r\n.section-title {\r\n  font-weight: 600;\r\n  margin-bottom: 1rem;\r\n  font-size: 1.125rem;\r\n  color: #f4f4f4;\r\n  border-bottom: 1px solid #333333;\r\n  padding-bottom: 0.5rem;\r\n}\r\n\r\n.info-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(2, 1fr);\r\n  gap: 1.5rem;\r\n  margin-bottom: 1.5rem;\r\n}\r\n\r\n.info-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.info-label {\r\n  font-size: 0.875rem;\r\n  color: #8d8d8d;\r\n  margin-bottom: 0.375rem;\r\n}\r\n\r\n.info-value {\r\n  font-size: 1rem;\r\n  color: #f4f4f4;\r\n}\r\n\r\n.action-details {\r\n  background-color: #333333;\r\n  padding: 1.5rem;\r\n  border-radius: 8px;\r\n  margin-top: 1rem;\r\n  border: 1px solid #444444;\r\n}\r\n\r\n.action-title {\r\n  font-weight: 600;\r\n  margin-bottom: 0.75rem;\r\n  color: #f4f4f4;\r\n  font-size: 1rem;\r\n}\r\n\r\n.action-description {\r\n  white-space: pre-line;\r\n  margin-bottom: 1.5rem;\r\n  color: #f4f4f4;\r\n  line-height: 1.5;\r\n}\r\n\r\n/* Required field indicator */\r\n.required-field {\r\n  color: #fa4d56;\r\n  margin-left: 4px;\r\n  font-weight: bold;\r\n}\r\n\r\n/* Form validation styles */\r\n.form-error {\r\n  color: #fa4d56;\r\n  font-size: 0.75rem;\r\n  margin-top: 0.25rem;\r\n}\r\n\r\n/* Form note */\r\n.form-note {\r\n  color: #8d8d8d;\r\n  font-size: 0.875rem;\r\n  margin-bottom: 1rem;\r\n  font-style: italic;\r\n}\r\n\r\n/* Responsive adjustments */\r\n@media (max-width: 1024px) {\r\n  .info-grid {\r\n    grid-template-columns: 1fr;\r\n  }\r\n\r\n  .filter-bar {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 1rem;\r\n  }\r\n\r\n  .filter-group {\r\n    width: 100%;\r\n    justify-content: space-between;\r\n  }\r\n\r\n  .search-box {\r\n    max-width: 100%;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .main-content {\r\n    padding: 1.5rem 1rem;\r\n  }\r\n\r\n  .page-header {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 1rem;\r\n  }\r\n\r\n  .action-controls {\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n/* Loading and error states */\r\n.loading-indicator, .loading-message {\r\n  color: #0f62fe;\r\n  font-size: 14px;\r\n  margin-top: 10px;\r\n}\r\n\r\n.error-message {\r\n  color: #da1e28;\r\n  font-size: 14px;\r\n  margin-top: 10px;\r\n}\r\n\r\n.empty-message {\r\n  color: #8d8d8d;\r\n  font-size: 14px;\r\n  text-align: center;\r\n  padding: 20px;\r\n}\r\n\r\n.table-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n/* Progress bar styling */\r\n.progress-container {\r\n  width: 100%;\r\n  padding: 0.5rem 0;\r\n}\r\n\r\n.detail-progress-bar {\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.progress-update-controls {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 0.5rem;\r\n  margin-top: 1rem;\r\n  background-color: #333333;\r\n  padding: 1rem;\r\n  border-radius: 4px;\r\n}\r\n\r\n/* Updates styling */\r\n.updates-list {\r\n  margin-top: 1rem;\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 1rem;\r\n}\r\n\r\n.update-item {\r\n  background-color: #333333;\r\n  padding: 1rem;\r\n  border-radius: 4px;\r\n  border-left: 3px solid #0f62fe;\r\n}\r\n\r\n.update-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 0.5rem;\r\n  font-size: 0.875rem;\r\n  color: #8d8d8d;\r\n}\r\n\r\n.update-content {\r\n  color: #f4f4f4;\r\n  line-height: 1.5;\r\n}\r\n\r\n.update-date {\r\n  font-weight: 600;\r\n}\r\n\r\n.update-by {\r\n  font-style: italic;\r\n}\r\n\r\n.add-update-form {\r\n  margin-top: 1rem;\r\n}\r\n\r\n.update-form-actions {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  margin-top: 1rem;\r\n}\r\n\r\n.expected-improvements {\r\n  margin-top: 1.5rem;\r\n  margin-bottom: 1.5rem;\r\n}\r\n\r\n/* Progress indicator in table */\r\n.cv-progress {\r\n  width: 100%;\r\n}\r\n\r\n/* Modal form styling */\r\n.modal-dropdown {\r\n  width: 100%;\r\n}\r\n\r\n.info-item .cv-text-input,\r\n.info-item .cv-dropdown {\r\n  margin-top: 0.25rem;\r\n}\r\n\r\n.action-textarea {\r\n  margin-bottom: 1.5rem;\r\n}\r\n\r\n.info-grid {\r\n  row-gap: 1.5rem;\r\n}\r\n\r\n/* Tab styling */\r\n.tab-content {\r\n  padding: 1.5rem 0;\r\n}\r\n\r\n/* Status tabs styling */\r\n.cv-tabs {\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.cv-tabs .cv-tab {\r\n  color: #f4f4f4;\r\n}\r\n\r\n.cv-tabs .cv-tab.cv-tab--selected {\r\n  border-bottom-color: #0f62fe;\r\n}\r\n\r\n/* Tab content styling */\r\n.tab-content {\r\n  min-height: 400px;\r\n}\r\n\r\n/* Tracking Modal Styles */\r\n.tracking-modal {\r\n  .cv-modal-container {\r\n    max-width: 95vw;\r\n    width: 1200px;\r\n  }\r\n}\r\n\r\n.tracking-modal-content {\r\n  padding: 1rem;\r\n  min-height: 600px;\r\n}\r\n\r\n.tracking-tab-content {\r\n  padding: 1.5rem;\r\n  min-height: 500px;\r\n}\r\n\r\n.tracking-section {\r\n  background-color: #333333;\r\n  border-radius: 8px;\r\n  padding: 1.5rem;\r\n  border: 1px solid #444444;\r\n}\r\n\r\n.tracking-section-title {\r\n  color: #f4f4f4;\r\n  font-size: 1.125rem;\r\n  font-weight: 600;\r\n  margin: 0 0 1rem 0;\r\n  border-bottom: 1px solid #444444;\r\n  padding-bottom: 0.5rem;\r\n}\r\n\r\n.action-summary {\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr;\r\n  gap: 0.75rem;\r\n}\r\n\r\n.summary-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 0.25rem;\r\n}\r\n\r\n.summary-label {\r\n  color: #8d8d8d;\r\n  font-size: 0.875rem;\r\n  font-weight: 500;\r\n}\r\n\r\n.summary-value {\r\n  color: #f4f4f4;\r\n  font-size: 1rem;\r\n  font-weight: 400;\r\n}\r\n\r\n.summary-value.priority-badge {\r\n  display: inline-flex;\r\n  align-items: center;\r\n  padding: 0.25rem 0.5rem;\r\n  border-radius: 4px;\r\n  font-size: 0.75rem;\r\n  font-weight: 600;\r\n  text-transform: uppercase;\r\n  width: fit-content;\r\n}\r\n\r\n.summary-value.priority-badge.high {\r\n  background-color: rgba(250, 77, 86, 0.2);\r\n  color: #ff8389;\r\n}\r\n\r\n.summary-value.priority-badge.medium {\r\n  background-color: rgba(255, 196, 0, 0.2);\r\n  color: #ffcc00;\r\n}\r\n\r\n.summary-value.priority-badge.low {\r\n  background-color: rgba(66, 190, 101, 0.2);\r\n  color: #6fdc8c;\r\n}\r\n\r\n.updates-list {\r\n  max-height: 300px;\r\n  overflow-y: auto;\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 1rem;\r\n}\r\n\r\n.update-item {\r\n  background-color: #262626;\r\n  border-radius: 6px;\r\n  padding: 1rem;\r\n  border-left: 3px solid #0f62fe;\r\n}\r\n\r\n.update-date {\r\n  color: #8d8d8d;\r\n  font-size: 0.875rem;\r\n  font-weight: 500;\r\n  margin-bottom: 0.5rem;\r\n}\r\n\r\n.update-content {\r\n  color: #f4f4f4;\r\n  font-size: 0.875rem;\r\n  line-height: 1.4;\r\n  margin-bottom: 0.5rem;\r\n}\r\n\r\n.update-author {\r\n  color: #8d8d8d;\r\n  font-size: 0.75rem;\r\n  font-style: italic;\r\n}\r\n\r\n.no-updates {\r\n  color: #8d8d8d;\r\n  font-style: italic;\r\n  text-align: center;\r\n  padding: 2rem;\r\n}\r\n\r\n/* Action Items List */\r\n.action-items-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 1.5rem;\r\n}\r\n\r\n.action-item-card {\r\n  background-color: #262626;\r\n  border-radius: 8px;\r\n  padding: 1.5rem;\r\n  border: 1px solid #444444;\r\n  transition: all 0.2s ease;\r\n}\r\n\r\n.action-item-card.completed {\r\n  border-left: 4px solid #42be65;\r\n  background-color: rgba(66, 190, 101, 0.05);\r\n}\r\n\r\n.action-item-header {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 0.5rem;\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.completion-date {\r\n  color: #42be65;\r\n  font-size: 0.875rem;\r\n  font-weight: 500;\r\n}\r\n\r\n.last-updated {\r\n  color: #8d8d8d;\r\n  font-size: 0.875rem;\r\n  font-style: italic;\r\n}\r\n\r\n.action-item-description {\r\n  color: #f4f4f4;\r\n  font-size: 0.875rem;\r\n  line-height: 1.4;\r\n  margin-bottom: 1rem;\r\n  padding: 0.75rem;\r\n  background-color: #333333;\r\n  border-radius: 4px;\r\n  border-left: 3px solid #0f62fe;\r\n}\r\n\r\n.action-item-notes {\r\n  margin-top: 1rem;\r\n}\r\n\r\n.notes-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 0.75rem;\r\n}\r\n\r\n.notes-header h5 {\r\n  color: #f4f4f4;\r\n  font-size: 0.875rem;\r\n  font-weight: 500;\r\n  margin: 0;\r\n}\r\n\r\n.add-note-inline {\r\n  background-color: #333333;\r\n  border-radius: 6px;\r\n  padding: 1rem;\r\n  margin-bottom: 1rem;\r\n  border: 1px solid #444444;\r\n}\r\n\r\n.note-actions {\r\n  display: flex;\r\n  gap: 0.5rem;\r\n  margin-top: 0.75rem;\r\n}\r\n\r\n.no-action-items {\r\n  text-align: center;\r\n  padding: 2rem;\r\n  color: #8d8d8d;\r\n}\r\n\r\n.no-action-items p {\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n/* Action Notes Section */\r\n.action-notes-section {\r\n  margin-bottom: 1.5rem;\r\n}\r\n\r\n.action-notes-section h4 {\r\n  color: #f4f4f4;\r\n  font-size: 1rem;\r\n  font-weight: 500;\r\n  margin: 0 0 1rem 0;\r\n}\r\n\r\n.notes-list {\r\n  max-height: 200px;\r\n  overflow-y: auto;\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.note-item {\r\n  background-color: #262626;\r\n  border-radius: 6px;\r\n  padding: 1rem;\r\n  margin-bottom: 0.75rem;\r\n  border-left: 3px solid #42be65;\r\n}\r\n\r\n.note-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 0.5rem;\r\n}\r\n\r\n.note-date {\r\n  color: #8d8d8d;\r\n  font-size: 0.875rem;\r\n  font-weight: 500;\r\n}\r\n\r\n.note-author {\r\n  color: #8d8d8d;\r\n  font-size: 0.75rem;\r\n  font-style: italic;\r\n}\r\n\r\n.note-content {\r\n  color: #f4f4f4;\r\n  font-size: 0.875rem;\r\n  line-height: 1.4;\r\n}\r\n\r\n.no-notes {\r\n  color: #8d8d8d;\r\n  font-style: italic;\r\n  text-align: center;\r\n  padding: 1rem;\r\n}\r\n\r\n.add-note-section {\r\n  background-color: #262626;\r\n  border-radius: 6px;\r\n  padding: 1rem;\r\n  border: 1px solid #444444;\r\n}\r\n\r\n/* Updates History Section */\r\n.updates-history-section h4 {\r\n  color: #f4f4f4;\r\n  font-size: 1rem;\r\n  font-weight: 500;\r\n  margin: 0 0 1rem 0;\r\n}\r\n\r\n.add-update-section {\r\n  background-color: #262626;\r\n  border-radius: 6px;\r\n  padding: 1rem;\r\n  border: 1px solid #444444;\r\n}\r\n\r\n.add-update-section h4 {\r\n  color: #f4f4f4;\r\n  font-size: 1rem;\r\n  font-weight: 500;\r\n  margin: 0 0 1rem 0;\r\n}\r\n\r\n.chart-container {\r\n  background-color: #262626;\r\n  border-radius: 6px;\r\n  padding: 1rem;\r\n  min-height: 350px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.no-chart-data {\r\n  text-align: center;\r\n  color: #8d8d8d;\r\n}\r\n\r\n.no-chart-data p {\r\n  margin: 0.5rem 0;\r\n}\r\n\r\n.chart-note {\r\n  font-size: 0.875rem;\r\n  font-style: italic;\r\n}\r\n\r\n/* Alert History Styles */\r\n.alert-history-section {\r\n  padding: 1.5rem;\r\n}\r\n\r\n.section-title {\r\n  color: #f4f4f4;\r\n  font-size: 1.25rem;\r\n  font-weight: 500;\r\n  margin: 0 0 0.5rem 0;\r\n}\r\n\r\n.section-description {\r\n  color: #8d8d8d;\r\n  font-size: 0.875rem;\r\n  margin: 0 0 1.5rem 0;\r\n  line-height: 1.4;\r\n}\r\n\r\n.alert-history-table {\r\n  background-color: #262626;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.alert-history-row.alert-row {\r\n  background-color: rgba(250, 77, 86, 0.1);\r\n  border-left: 3px solid #fa4d56;\r\n}\r\n\r\n.alert-history-row.normal-row {\r\n  background-color: rgba(66, 190, 101, 0.05);\r\n  border-left: 3px solid #42be65;\r\n}\r\n\r\n.status-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n}\r\n\r\n.status-indicator {\r\n  width: 8px;\r\n  height: 8px;\r\n  border-radius: 50%;\r\n  display: inline-block;\r\n}\r\n\r\n.status-indicator.alert {\r\n  background-color: #fa4d56;\r\n  animation: pulse-red 2s infinite;\r\n}\r\n\r\n.status-indicator.normal {\r\n  background-color: #42be65;\r\n}\r\n\r\n.status-text.alert {\r\n  color: #ff8389;\r\n  font-weight: 500;\r\n}\r\n\r\n.status-text.normal {\r\n  color: #6fdc8c;\r\n  font-weight: 500;\r\n}\r\n\r\n@keyframes pulse-red {\r\n  0% {\r\n    box-shadow: 0 0 0 0 rgba(250, 77, 86, 0.7);\r\n  }\r\n  70% {\r\n    box-shadow: 0 0 0 10px rgba(250, 77, 86, 0);\r\n  }\r\n  100% {\r\n    box-shadow: 0 0 0 0 rgba(250, 77, 86, 0);\r\n  }\r\n}\r\n\r\n.no-alert-data {\r\n  text-align: center;\r\n  padding: 3rem;\r\n  color: #8d8d8d;\r\n}\r\n\r\n.no-alert-data p {\r\n  margin: 0.5rem 0;\r\n}\r\n\r\n.no-alert-data .note {\r\n  font-size: 0.875rem;\r\n  font-style: italic;\r\n}\r\n\r\n/* New Alert Functionality Styles */\r\n.new-alerts-section {\r\n  padding: 1.5rem;\r\n}\r\n\r\n.subsection-title {\r\n  color: #f4f4f4;\r\n  font-size: 1rem;\r\n  font-weight: 500;\r\n  margin: 0 0 1rem 0;\r\n  border-bottom: 1px solid #444444;\r\n  padding-bottom: 0.5rem;\r\n}\r\n\r\n.ai-insight-section {\r\n  background-color: #333333;\r\n  border-radius: 8px;\r\n  padding: 1.5rem;\r\n  margin-bottom: 2rem;\r\n  border: 1px solid #444444;\r\n}\r\n\r\n.ai-insight-content {\r\n  background-color: #262626;\r\n  border-radius: 6px;\r\n  padding: 1rem;\r\n  border: 1px solid #525252;\r\n  font-size: 0.875rem;\r\n  line-height: 1.5;\r\n  color: #f4f4f4;\r\n}\r\n\r\n.add-alert-section {\r\n  background-color: #333333;\r\n  border-radius: 8px;\r\n  padding: 1.5rem;\r\n  margin-bottom: 2rem;\r\n  border: 1px solid #444444;\r\n}\r\n\r\n.alert-updates-section {\r\n  background-color: #333333;\r\n  border-radius: 8px;\r\n  padding: 1.5rem;\r\n  border: 1px solid #444444;\r\n}\r\n\r\n.alert-updates-table {\r\n  background-color: #262626;\r\n  border-radius: 6px;\r\n  overflow: hidden;\r\n  border: 1px solid #525252;\r\n}\r\n\r\n.updates-header {\r\n  display: grid;\r\n  grid-template-columns: 120px 1fr 150px;\r\n  background-color: #393939;\r\n  border-bottom: 1px solid #525252;\r\n}\r\n\r\n.update-column {\r\n  padding: 0.75rem;\r\n  font-weight: 600;\r\n  color: #f4f4f4;\r\n  border-right: 1px solid #525252;\r\n}\r\n\r\n.update-column:last-child {\r\n  border-right: none;\r\n}\r\n\r\n.update-row {\r\n  display: grid;\r\n  grid-template-columns: 120px 1fr 150px;\r\n  border-bottom: 1px solid #525252;\r\n}\r\n\r\n.update-row:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.update-cell {\r\n  padding: 0.75rem;\r\n  color: #f4f4f4;\r\n  border-right: 1px solid #525252;\r\n  font-size: 0.875rem;\r\n}\r\n\r\n.update-cell:last-child {\r\n  border-right: none;\r\n}\r\n\r\n.no-updates-message {\r\n  text-align: center;\r\n  color: #8d8d8d;\r\n  font-style: italic;\r\n  padding: 2rem;\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .updates-header,\r\n  .update-row {\r\n    grid-template-columns: 1fr;\r\n  }\r\n\r\n  .update-column,\r\n  .update-cell {\r\n    border-right: none;\r\n    border-bottom: 1px solid #525252;\r\n  }\r\n}\r\n\r\n/* Performance Chart & History Tab Styles */\r\n.chart-section {\r\n  background-color: #333333;\r\n  border-radius: 8px;\r\n  padding: 1.5rem;\r\n  margin-bottom: 2rem;\r\n  border: 1px solid #444444;\r\n}\r\n\r\n.performance-history-section {\r\n  background-color: #333333;\r\n  border-radius: 8px;\r\n  padding: 1.5rem;\r\n  border: 1px solid #444444;\r\n}\r\n\r\n.section-description {\r\n  color: #c6c6c6;\r\n  font-size: 0.875rem;\r\n  margin-bottom: 2rem;\r\n}\r\n</style>\r\n\r\n\r\n<template>\r\n  <div class=\"dashboard-container\">\r\n    <!-- Inherit the MainHeader component -->\r\n    <MainHeader :expandedSideNav=\"expandedSideNav\" :useFixed=\"useFixed\" />\r\n\r\n    <main class=\"main-content\">\r\n      <!-- Page Header -->\r\n      <div class=\"page-header\">\r\n        <h1 class=\"page-title\">Action Tracker</h1>\r\n        <div class=\"action-controls\">\r\n          <cv-button kind=\"primary\" @click=\"openNewActionModal\">Add New Action</cv-button>\r\n          <cv-button kind=\"secondary\" @click=\"exportData\">Export</cv-button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Filter Bar -->\r\n      <div class=\"filter-bar\">\r\n        <div class=\"filter-group\">\r\n          <div class=\"filter-label\">Commodity:</div>\r\n          <cv-dropdown\r\n            v-model=\"commodityFilter\"\r\n            label=\"Filter by commodity\"\r\n            :items=\"commodityOptions\"\r\n          ></cv-dropdown>\r\n        </div>\r\n\r\n        <div class=\"filter-group\">\r\n          <div class=\"filter-label\">Assignee:</div>\r\n          <cv-dropdown\r\n            v-model=\"assigneeFilter\"\r\n            label=\"Filter by assignee\"\r\n            :items=\"assigneeOptions\"\r\n          ></cv-dropdown>\r\n        </div>\r\n\r\n        <div class=\"search-box\">\r\n          <cv-search\r\n            v-model=\"searchQuery\"\r\n            label=\"Search\"\r\n            placeholder=\"Search actions...\"\r\n          ></cv-search>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Status Tabs -->\r\n      <cv-tabs @tab-selected=\"onTabSelected\" :selected=\"selectedTab\">\r\n        <cv-tab id=\"in-progress\" label=\"In-Progress Items\">\r\n          <div class=\"tab-content\">\r\n            <ActionItemsTable\r\n              :items=\"inProgressItems\"\r\n              :columns=\"inProgressColumns\"\r\n              :isLoading=\"isLoading\"\r\n              :loadingError=\"loadingError\"\r\n              @item-selected=\"handleItemSelected\"\r\n              @update-item=\"handleUpdateItem\"\r\n              @track-item=\"handleTrackItem\"\r\n              status-type=\"in-progress\"\r\n            />\r\n          </div>\r\n        </cv-tab>\r\n\r\n        <cv-tab id=\"monitored\" label=\"Monitored Items\">\r\n          <div class=\"tab-content\">\r\n            <ActionItemsTable\r\n              :items=\"monitoredItems\"\r\n              :columns=\"monitoredColumns\"\r\n              :isLoading=\"isLoading\"\r\n              :loadingError=\"loadingError\"\r\n              @item-selected=\"handleItemSelected\"\r\n              @update-item=\"handleUpdateItem\"\r\n              @track-item=\"handleTrackItem\"\r\n              status-type=\"monitored\"\r\n            />\r\n          </div>\r\n        </cv-tab>\r\n\r\n        <cv-tab id=\"resolved\" label=\"Resolved Items\">\r\n          <div class=\"tab-content\">\r\n            <ActionItemsTable\r\n              :items=\"resolvedItems\"\r\n              :columns=\"resolvedColumns\"\r\n              :isLoading=\"isLoading\"\r\n              :loadingError=\"loadingError\"\r\n              @item-selected=\"handleItemSelected\"\r\n              @update-item=\"handleUpdateItem\"\r\n              @track-item=\"handleTrackItem\"\r\n              status-type=\"resolved\"\r\n            />\r\n          </div>\r\n        </cv-tab>\r\n      </cv-tabs>\r\n    </main>\r\n\r\n    <!-- Action Details Modal -->\r\n    <cv-modal\r\n\r\n      :visible=\"modalVisible\"\r\n      @modal-hidden=\"modalVisible = false\"\r\n\r\n    >\r\n      <template slot=\"title\">\r\n        <div>Action Details - {{ selectedRow ? selectedRow.pn : '' }}</div>\r\n      </template>\r\n      <template slot=\"content\">\r\n        <div class=\"modal-content\" v-if=\"selectedRow\">\r\n          <!-- Status Banner -->\r\n          <div class=\"status-banner\" :class=\"'status-banner-' + selectedRow.status.toLowerCase()\">\r\n            <div class=\"status-badge\" :class=\"selectedRow.status.toLowerCase()\">\r\n              <span class=\"status-indicator\" :class=\"'status-' + selectedRow.status.toLowerCase()\"></span>\r\n              {{ selectedRow.status }}\r\n            </div>\r\n            <div class=\"assignee-info\">Assigned to: <strong>{{ selectedRow.assignee }}</strong></div>\r\n          </div>\r\n\r\n          <!-- Basic Information Section -->\r\n          <div class=\"modal-section\">\r\n            <div class=\"section-title\">Basic Information</div>\r\n            <div class=\"info-grid\">\r\n              <div class=\"info-item\">\r\n                <span class=\"info-label\">Commodity</span>\r\n                <cv-dropdown\r\n                  v-model=\"selectedRow.commodity\"\r\n                  label=\"Commodity\"\r\n                  :items=\"commodityOptions.filter(item => item !== 'All')\"\r\n                  class=\"modal-dropdown\"\r\n                ></cv-dropdown>\r\n              </div>\r\n              <div class=\"info-item\">\r\n                <span class=\"info-label\">Group</span>\r\n                <cv-text-input\r\n                  v-model=\"selectedRow.group\"\r\n                  label=\"Group\"\r\n                ></cv-text-input>\r\n              </div>\r\n              <div class=\"info-item\">\r\n                <span class=\"info-label\">Part Number</span>\r\n                <cv-text-input\r\n                  v-model=\"selectedRow.pn\"\r\n                  label=\"Part Number\"\r\n                ></cv-text-input>\r\n              </div>\r\n              <div class=\"info-item\">\r\n                <span class=\"info-label\">Test</span>\r\n                <cv-text-input\r\n                  v-model=\"selectedRow.editableTest\"\r\n                  label=\"Test\"\r\n                ></cv-text-input>\r\n              </div>\r\n              <div class=\"info-item\">\r\n                <span class=\"info-label\">Deadline</span>\r\n                <cv-text-input\r\n                  type=\"date\"\r\n                  v-model=\"selectedRow.deadline\"\r\n                  label=\"Deadline\"\r\n                ></cv-text-input>\r\n              </div>\r\n              <div class=\"info-item\">\r\n                <span class=\"info-label\">Expected Resolution</span>\r\n                <cv-text-input\r\n                  type=\"date\"\r\n                  v-model=\"selectedRow.expectedResolution\"\r\n                  label=\"Expected Resolution\"\r\n                ></cv-text-input>\r\n              </div>\r\n              <div class=\"info-item\">\r\n                <span class=\"info-label\">Status</span>\r\n                <cv-dropdown\r\n                  v-model=\"selectedRow.status\"\r\n                  label=\"Status\"\r\n                  :items=\"statusOptions.filter(item => item !== 'All')\"\r\n                  class=\"modal-dropdown\"\r\n                ></cv-dropdown>\r\n              </div>\r\n              <div class=\"info-item\">\r\n                <span class=\"info-label\">Assignee</span>\r\n                <cv-text-input\r\n                  v-model=\"selectedRow.assignee\"\r\n                  label=\"Assignee\"\r\n                ></cv-text-input>\r\n              </div>\r\n              <div class=\"info-item\">\r\n                <span class=\"info-label\">Created</span>\r\n                <span class=\"info-value\">{{ formatDate(selectedRow.createdAt) }}</span>\r\n              </div>\r\n              <div class=\"info-item\">\r\n                <span class=\"info-label\">Last Updated</span>\r\n                <span class=\"info-value\">{{ formatDate(selectedRow.updatedAt) }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Action Details Section -->\r\n          <div class=\"modal-section\">\r\n            <div class=\"section-title\">Action Details</div>\r\n            <div class=\"action-details\">\r\n              <div class=\"action-title\">Current Action</div>\r\n              <cv-text-area\r\n                v-model=\"selectedRow.action\"\r\n                label=\"Action Description\"\r\n                class=\"action-textarea\"\r\n              ></cv-text-area>\r\n\r\n              <div class=\"expected-improvements\">\r\n                <div class=\"action-title\">Expected Improvements</div>\r\n                <cv-text-area\r\n                  v-model=\"selectedRow.expectedImprovements\"\r\n                  label=\"Expected Improvements\"\r\n                  placeholder=\"Describe the expected improvements (e.g., 30% increase in yield, 15% reduction in defects)\"\r\n                  class=\"action-textarea\"\r\n                ></cv-text-area>\r\n              </div>\r\n\r\n              <div class=\"progress-section\">\r\n                <div class=\"action-title\">Progress</div>\r\n                <cv-progress\r\n                  :value=\"selectedRow.progress || 0\"\r\n                  :label-text=\"`${selectedRow.progress || 0}%`\"\r\n                  class=\"detail-progress-bar\"\r\n                />\r\n                <div class=\"progress-update-controls\">\r\n                  <cv-slider\r\n                    v-model=\"selectedRow.progress\"\r\n                    :min=\"0\"\r\n                    :max=\"100\"\r\n                    :step=\"5\"\r\n                    :label=\"'Update Progress'\"\r\n                  ></cv-slider>\r\n                  <cv-button\r\n                    kind=\"primary\"\r\n                    size=\"small\"\r\n                    @click=\"updateProgress(selectedRow)\"\r\n                  >Update Progress</cv-button>\r\n                </div>\r\n              </div>\r\n\r\n              <div v-if=\"selectedRow.updates && selectedRow.updates.length > 0\">\r\n                <div class=\"action-title\">Updates History</div>\r\n                <div class=\"updates-list\">\r\n                  <div v-for=\"(update, index) in selectedRow.updates\" :key=\"index\" class=\"update-item\">\r\n                    <div class=\"update-header\">\r\n                      <span class=\"update-date\">{{ formatDate(update.date) }}</span>\r\n                      <span class=\"update-by\">by {{ update.updatedBy }}</span>\r\n                    </div>\r\n                    <div class=\"update-content\">{{ update.content }}</div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Add Update Section -->\r\n          <div class=\"modal-section\">\r\n            <div class=\"section-title\">Add Update</div>\r\n            <div class=\"add-update-form\">\r\n              <cv-text-area\r\n                v-model=\"newUpdate\"\r\n                label=\"Update Content\"\r\n                placeholder=\"Enter update details...\"\r\n              ></cv-text-area>\r\n              <div class=\"update-form-actions\">\r\n                <cv-button\r\n                  kind=\"primary\"\r\n                  @click=\"addUpdate(selectedRow)\"\r\n                >Add Update</cv-button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Issues Section -->\r\n          <div class=\"modal-section\" v-if=\"selectedRow.issues && selectedRow.issues.length > 0\">\r\n            <div class=\"section-title\">Related Issues</div>\r\n            <cv-data-table\r\n              :columns=\"issueColumns\"\r\n              :data=\"selectedRow.issues\"\r\n              :title=\"''\"\r\n            ></cv-data-table>\r\n          </div>\r\n\r\n          <!-- Notes Section -->\r\n          <div class=\"modal-section\">\r\n            <div class=\"section-title\">Notes</div>\r\n            <cv-text-area\r\n              v-model=\"selectedRow.notes\"\r\n              label=\"Additional Notes\"\r\n              placeholder=\"Enter any additional notes\"\r\n            ></cv-text-area>\r\n          </div>\r\n\r\n          <!-- Action Buttons -->\r\n          <div class=\"modal-actions\">\r\n            <cv-button kind=\"secondary\" @click=\"modalVisible = false\">Cancel</cv-button>\r\n            <cv-button kind=\"primary\" @click=\"updateEntireAction(selectedRow)\">Update Action</cv-button>\r\n          </div>\r\n        </div>\r\n      </template>\r\n    </cv-modal>\r\n\r\n    <!-- New Action Modal -->\r\n    <cv-modal\r\n      class=\"action-modal\"\r\n      :visible=\"newActionModalVisible\"\r\n      @modal-hidden=\"newActionModalVisible = false\"\r\n      :size=\"'lg'\"\r\n    >\r\n      <template slot=\"title\">\r\n        <div>Create New Action</div>\r\n      </template>\r\n      <template slot=\"content\">\r\n        <div class=\"modal-content\">\r\n          <!-- Basic Information Section -->\r\n          <div class=\"modal-section\">\r\n            <div class=\"section-title\">Basic Information</div>\r\n            <p class=\"form-note\">Fields marked with <span class=\"required-field\">*</span> are required</p>\r\n            <div class=\"form-grid\">\r\n              <div class=\"form-group\">\r\n                <label class=\"form-label\">Process/Commodity <span class=\"required-field\">*</span></label>\r\n                <cv-dropdown\r\n                  v-model=\"newAction.commodity\"\r\n                  label=\"Select Process/Commodity\"\r\n                  :items=\"commodityOptions.filter(item => item !== 'All')\"\r\n                ></cv-dropdown>\r\n              </div>\r\n\r\n              <div class=\"form-group\">\r\n                <label class=\"form-label\">Part Group <span class=\"required-field\">*</span></label>\r\n                <cv-text-input\r\n                  v-model=\"newAction.group\"\r\n                  label=\"Part Group\"\r\n                  placeholder=\"Enter part group name\"\r\n                ></cv-text-input>\r\n              </div>\r\n\r\n              <div class=\"form-group\">\r\n                <label class=\"form-label\">Part Number</label>\r\n                <cv-text-input\r\n                  v-model=\"newAction.pn\"\r\n                  label=\"Part Number\"\r\n                  placeholder=\"Enter part number\"\r\n                ></cv-text-input>\r\n              </div>\r\n\r\n              <div class=\"form-group\">\r\n                <label class=\"form-label\">Test</label>\r\n                <cv-text-input\r\n                  v-model=\"newAction.test\"\r\n                  label=\"Test\"\r\n                  placeholder=\"Enter test name\"\r\n                ></cv-text-input>\r\n              </div>\r\n\r\n              <div class=\"form-group\">\r\n                <label class=\"form-label\">Deadline</label>\r\n                <cv-text-input\r\n                  type=\"date\"\r\n                  v-model=\"newAction.deadline\"\r\n                  label=\"Deadline\"\r\n                ></cv-text-input>\r\n              </div>\r\n\r\n              <div class=\"form-group\">\r\n                <label class=\"form-label\">Expected Resolution Date</label>\r\n                <cv-text-input\r\n                  type=\"date\"\r\n                  v-model=\"newAction.expectedResolution\"\r\n                  label=\"Expected Resolution\"\r\n                ></cv-text-input>\r\n              </div>\r\n\r\n              <div class=\"form-group\">\r\n                <label class=\"form-label\">Status</label>\r\n                <cv-dropdown\r\n                  v-model=\"newAction.status\"\r\n                  label=\"Select Status\"\r\n                  :items=\"['Current', 'In-Progress', 'Monitored', 'Resolved']\"\r\n                ></cv-dropdown>\r\n              </div>\r\n\r\n              <div class=\"form-group\">\r\n                <label class=\"form-label\">Priority</label>\r\n                <cv-dropdown\r\n                  v-model=\"newAction.priority\"\r\n                  label=\"Select Priority\"\r\n                  :items=\"['High', 'Medium', 'Low']\"\r\n                ></cv-dropdown>\r\n              </div>\r\n\r\n              <div class=\"form-group\">\r\n                <label class=\"form-label\">Progress (%)</label>\r\n                <cv-slider\r\n                  v-model=\"newAction.progress\"\r\n                  :min=\"0\"\r\n                  :max=\"100\"\r\n                  :step=\"5\"\r\n                  :label=\"'Progress'\"\r\n                ></cv-slider>\r\n              </div>\r\n\r\n              <div class=\"form-group\">\r\n                <label class=\"form-label\">Assignee</label>\r\n                <cv-text-input\r\n                  v-model=\"newAction.assignee\"\r\n                  label=\"Assignee\"\r\n                  placeholder=\"Enter assignee name\"\r\n                ></cv-text-input>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Action Details Section -->\r\n          <div class=\"modal-section\">\r\n            <div class=\"section-title\">Action Details</div>\r\n            <div class=\"form-group full-width\">\r\n              <label class=\"form-label\">Action Description <span class=\"required-field\">*</span></label>\r\n              <cv-text-area\r\n                v-model=\"newAction.action\"\r\n                label=\"Action Description\"\r\n                placeholder=\"Describe the action to be taken\"\r\n              ></cv-text-area>\r\n            </div>\r\n\r\n            <div class=\"form-group full-width\">\r\n              <label class=\"form-label\">Expected Improvements</label>\r\n              <cv-text-area\r\n                v-model=\"newAction.expectedImprovements\"\r\n                label=\"Expected Improvements\"\r\n                placeholder=\"Describe the expected improvements (e.g., 30% increase in yield, 15% reduction in defects)\"\r\n              ></cv-text-area>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Notes Section -->\r\n          <div class=\"modal-section\">\r\n            <div class=\"section-title\">Notes</div>\r\n            <div class=\"form-group full-width\">\r\n              <label class=\"form-label\">Additional Notes</label>\r\n              <cv-text-area\r\n                v-model=\"newAction.notes\"\r\n                label=\"Notes\"\r\n                placeholder=\"Enter any additional notes\"\r\n              ></cv-text-area>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Action Buttons -->\r\n          <div class=\"modal-actions\">\r\n            <cv-button kind=\"secondary\" @click=\"newActionModalVisible = false\">Cancel</cv-button>\r\n            <cv-button kind=\"primary\" @click=\"createNewAction\">Create Action</cv-button>\r\n          </div>\r\n        </div>\r\n      </template>\r\n    </cv-modal>\r\n\r\n    <!-- Tracking Modal -->\r\n    <cv-modal\r\n      class=\"tracking-modal\"\r\n      :visible=\"trackingModalVisible\"\r\n      @modal-hidden=\"trackingModalVisible = false\"\r\n      :size=\"'xl'\"\r\n    >\r\n      <template slot=\"title\">\r\n        <div>Action Tracking - {{ selectedTrackingItem ? selectedTrackingItem.pn : '' }}</div>\r\n      </template>\r\n      <template slot=\"content\">\r\n        <div class=\"tracking-modal-content\" v-if=\"selectedTrackingItem\">\r\n          <!-- Tracking Tabs -->\r\n          <cv-tabs>\r\n            <cv-tab id=\"action-items-tab\" label=\"Action Items\">\r\n              <div class=\"tracking-tab-content\">\r\n                <div class=\"tracking-section\">\r\n                  <h3 class=\"tracking-section-title\">Action Details</h3>\r\n                  <div class=\"action-summary\">\r\n                    <div class=\"summary-item\">\r\n                      <span class=\"summary-label\">Part Group:</span>\r\n                      <span class=\"summary-value\">{{ selectedTrackingItem.group }}</span>\r\n                    </div>\r\n                    <div class=\"summary-item\">\r\n                      <span class=\"summary-label\">Part Number:</span>\r\n                      <span class=\"summary-value\">{{ selectedTrackingItem.pn }}</span>\r\n                    </div>\r\n                    <div class=\"summary-item\">\r\n                      <span class=\"summary-label\">Status:</span>\r\n                      <span class=\"summary-value\">{{ selectedTrackingItem.status }}</span>\r\n                    </div>\r\n                    <div class=\"summary-item\">\r\n                      <span class=\"summary-label\">Priority:</span>\r\n                      <span class=\"summary-value priority-badge\" :class=\"selectedTrackingItem.priority.toLowerCase()\">\r\n                        {{ selectedTrackingItem.priority }}\r\n                      </span>\r\n                    </div>\r\n                    <div class=\"summary-item\">\r\n                      <span class=\"summary-label\">Progress:</span>\r\n                      <span class=\"summary-value\">{{ selectedTrackingItem.progress }}%</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n              <div class=\"tracking-section\">\r\n                <h3 class=\"tracking-section-title\">Action Items & Progress</h3>\r\n\r\n                <!-- Action Items List -->\r\n                <div class=\"action-items-list\">\r\n                  <div\r\n                    v-for=\"(actionItem, index) in selectedTrackingItem.actionItems\"\r\n                    :key=\"index\"\r\n                    class=\"action-item-card\"\r\n                    :class=\"{ 'completed': actionItem.completed }\"\r\n                  >\r\n                    <!-- Action Item Header -->\r\n                    <div class=\"action-item-header\">\r\n                      <cv-checkbox\r\n                        v-model=\"actionItem.completed\"\r\n                        :label=\"actionItem.title\"\r\n                        @change=\"updateActionItemCompletion(actionItem, index)\"\r\n                      />\r\n                      <span class=\"completion-date\" v-if=\"actionItem.completed && actionItem.completedDate\">\r\n                        Completed on {{ formatDate(actionItem.completedDate) }}\r\n                      </span>\r\n                      <span class=\"last-updated\" v-if=\"actionItem.lastUpdated\">\r\n                        Last updated: {{ formatDate(actionItem.lastUpdated) }}\r\n                      </span>\r\n                    </div>\r\n\r\n                    <!-- Action Item Description -->\r\n                    <div class=\"action-item-description\" v-if=\"actionItem.description\">\r\n                      {{ actionItem.description }}\r\n                    </div>\r\n\r\n                    <!-- Action Item Notes -->\r\n                    <div class=\"action-item-notes\">\r\n                      <div class=\"notes-header\">\r\n                        <h5>Notes</h5>\r\n                        <cv-button\r\n                          size=\"small\"\r\n                          kind=\"ghost\"\r\n                          @click=\"toggleAddNote(index)\"\r\n                        >\r\n                          Add Note\r\n                        </cv-button>\r\n                      </div>\r\n\r\n                      <!-- Add Note Section -->\r\n                      <div v-if=\"actionItem.showAddNote\" class=\"add-note-inline\">\r\n                        <cv-text-area\r\n                          v-model=\"actionItem.newNote\"\r\n                          placeholder=\"Enter note for this action item...\"\r\n                          rows=\"2\"\r\n                        ></cv-text-area>\r\n                        <div class=\"note-actions\">\r\n                          <cv-button\r\n                            kind=\"primary\"\r\n                            size=\"small\"\r\n                            @click=\"addNoteToActionItem(actionItem, index)\"\r\n                            :disabled=\"!actionItem.newNote || !actionItem.newNote.trim()\"\r\n                          >\r\n                            Save Note\r\n                          </cv-button>\r\n                          <cv-button\r\n                            kind=\"secondary\"\r\n                            size=\"small\"\r\n                            @click=\"cancelAddNote(index)\"\r\n                          >\r\n                            Cancel\r\n                          </cv-button>\r\n                        </div>\r\n                      </div>\r\n\r\n                      <!-- Notes List -->\r\n                      <div class=\"notes-list\">\r\n                        <div\r\n                          v-for=\"(note, noteIndex) in actionItem.notes\"\r\n                          :key=\"noteIndex\"\r\n                          class=\"note-item\"\r\n                        >\r\n                          <div class=\"note-header\">\r\n                            <span class=\"note-date\">{{ formatDate(note.date) }}</span>\r\n                            <span class=\"note-author\">by {{ note.author }}</span>\r\n                          </div>\r\n                          <div class=\"note-content\">{{ note.content }}</div>\r\n                        </div>\r\n                        <div v-if=\"!actionItem.notes || actionItem.notes.length === 0\" class=\"no-notes\">\r\n                          No notes for this item yet\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <!-- No Action Items -->\r\n                  <div v-if=\"!selectedTrackingItem.actionItems || selectedTrackingItem.actionItems.length === 0\" class=\"no-action-items\">\r\n                    <p>No action items defined for this tracking item.</p>\r\n                    <cv-button\r\n                      kind=\"primary\"\r\n                      size=\"small\"\r\n                      @click=\"addDefaultActionItems\"\r\n                    >\r\n                      Create Default Action Items\r\n                    </cv-button>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            </cv-tab>\r\n\r\n            <cv-tab id=\"new-alerts-tab\" label=\"New Alerts\">\r\n              <div class=\"tracking-tab-content\">\r\n                <div class=\"new-alerts-section\">\r\n                  <h3 class=\"section-title\">Alert Management</h3>\r\n                  <p class=\"section-description\">Manage alerts and updates for {{ selectedTrackingItem.pn }}</p>\r\n\r\n                  <!-- AI Insight Section -->\r\n                  <div class=\"ai-insight-section\">\r\n                    <h4 class=\"subsection-title\">AI Insight</h4>\r\n                    <div v-if=\"isLoadingAiInsight\" class=\"loading-message\">\r\n                      Generating AI insight...\r\n                    </div>\r\n                    <div v-else class=\"ai-insight-content\">\r\n                      {{ aiInsight || 'No AI insight available for this alert.' }}\r\n                    </div>\r\n                  </div>\r\n\r\n                  <!-- Add New Alert Update -->\r\n                  <div class=\"add-alert-section\">\r\n                    <h4 class=\"subsection-title\">Add Alert Update</h4>\r\n                    <div class=\"add-update-form\">\r\n                      <cv-text-area\r\n                        v-model=\"newAlertUpdate\"\r\n                        label=\"Update Content\"\r\n                        placeholder=\"Enter update details...\"\r\n                        rows=\"4\"\r\n                      ></cv-text-area>\r\n                      <div class=\"update-form-actions\">\r\n                        <cv-button\r\n                          kind=\"primary\"\r\n                          @click=\"addAlertUpdate\"\r\n                          :disabled=\"!newAlertUpdate.trim()\"\r\n                        >\r\n                          Add Update\r\n                        </cv-button>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <!-- Alert History Table -->\r\n                  <div class=\"alert-updates-section\">\r\n                    <h4 class=\"subsection-title\">Alert History</h4>\r\n                    <div v-if=\"alertUpdates.length > 0\" class=\"alert-updates-table\">\r\n                      <div class=\"updates-header\">\r\n                        <div class=\"update-column\">Date</div>\r\n                        <div class=\"update-column\">Update</div>\r\n                        <div class=\"update-column\">Updated By</div>\r\n                      </div>\r\n                      <div\r\n                        v-for=\"(update, index) in alertUpdates\"\r\n                        :key=\"index\"\r\n                        class=\"update-row\"\r\n                      >\r\n                        <div class=\"update-cell\">{{ update.date }}</div>\r\n                        <div class=\"update-cell\">{{ update.update }}</div>\r\n                        <div class=\"update-cell\">{{ update.updatedBy }}</div>\r\n                      </div>\r\n                    </div>\r\n                    <div v-else class=\"no-updates-message\">\r\n                      No alert updates available.\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </cv-tab>\r\n\r\n            <cv-tab id=\"performance-tab\" label=\"Performance Chart & History\">\r\n              <div class=\"tracking-tab-content\">\r\n                <!-- Performance Chart Section -->\r\n                <div class=\"chart-section\">\r\n                  <h3 class=\"section-title\">Performance Chart</h3>\r\n                  <PerformanceChart :trackingItem=\"selectedTrackingItem\" />\r\n                </div>\r\n\r\n                <!-- Performance History Table Section -->\r\n                <div class=\"performance-history-section\">\r\n                  <h3 class=\"section-title\">Monthly Performance History</h3>\r\n                  <p class=\"section-description\">Historical record of performance and status for {{ selectedTrackingItem.pn }}</p>\r\n\r\n                  <cv-data-table\r\n                    :columns=\"alertHistoryColumns\"\r\n                    :title=\"''\"\r\n                    class=\"alert-history-table\"\r\n                  >\r\n                    <template slot=\"data\">\r\n                      <cv-data-table-row\r\n                        v-for=\"(record, index) in alertHistoryData\"\r\n                        :key=\"index\"\r\n                        :class=\"getAlertRowClass(record)\"\r\n                      >\r\n                        <cv-data-table-cell>{{ record.month }}</cv-data-table-cell>\r\n                        <cv-data-table-cell>{{ record.year }}</cv-data-table-cell>\r\n                        <cv-data-table-cell>\r\n                          <div class=\"status-cell\">\r\n                            <span class=\"status-indicator\" :class=\"record.status.toLowerCase()\"></span>\r\n                            <span class=\"status-text\" :class=\"record.status.toLowerCase()\">{{ record.status }}</span>\r\n                          </div>\r\n                        </cv-data-table-cell>\r\n                        <cv-data-table-cell>{{ record.actualRate }}%</cv-data-table-cell>\r\n                        <cv-data-table-cell>{{ record.targetRate }}%</cv-data-table-cell>\r\n                        <cv-data-table-cell>{{ record.xFactor }}</cv-data-table-cell>\r\n                        <cv-data-table-cell>{{ record.volume }}</cv-data-table-cell>\r\n                        <cv-data-table-cell>{{ record.defects }}</cv-data-table-cell>\r\n                        <cv-data-table-cell>{{ record.notes || 'N/A' }}</cv-data-table-cell>\r\n                      </cv-data-table-row>\r\n                    </template>\r\n                  </cv-data-table>\r\n\r\n                  <div v-if=\"!alertHistoryData || alertHistoryData.length === 0\" class=\"no-alert-data\">\r\n                    <p>No performance history available for this part</p>\r\n                    <p class=\"note\">Performance history will be populated as data becomes available</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </cv-tab>\r\n          </cv-tabs>\r\n        </div>\r\n      </template>\r\n    </cv-modal>\r\n  </div>\r\n</template>\r\n\r\n\r\n\r\n<script>\r\nimport MainHeader from '@/components/MainHeader'; // Import the MainHeader component\r\nimport ActionItemsTable from '@/components/ActionTracker/ActionItemsTable.vue'; // Import the ActionItemsTable component\r\n// import LineChart from '@/components/LineChart/LineChart.vue'; // Import the LineChart component\r\nimport PerformanceChart from '@/components/PerformanceChart/PerformanceChart.vue'; // Import the PerformanceChart component\r\n\r\nexport default {\r\n  name: 'ActionTracker',\r\n  components: {\r\n    MainHeader,\r\n    ActionItemsTable,\r\n    // LineChart,\r\n    PerformanceChart\r\n  },\r\n  data() {\r\n    return {\r\n      // Dynamic columns based on status type\r\n      inProgressColumns: ['Priority', 'Commodity', 'Group', 'PN', 'Test', 'Deadline', 'Date Started', 'Assignee', 'Tracking'],\r\n      monitoredColumns: ['Priority', 'Commodity', 'Group', 'PN', 'Test', 'Monitor Date', 'Date Started', 'Assignee', 'Tracking'],\r\n      resolvedColumns: ['Priority', 'Commodity', 'Group', 'PN', 'Test', 'Resolution Date', 'Date Started', 'Assignee', 'Tracking'],\r\n      issueColumns: ['Issue ID', 'Description', 'Severity', 'Date Reported'],\r\n      historyColumns: ['Date', 'Action', 'Updated By'],\r\n      alertHistoryColumns: ['Month', 'Year', 'Status', 'Actual Rate', 'Target Rate', 'X-Factor', 'Volume', 'Defects', 'Notes'],\r\n      modalVisible: false,\r\n      newActionModalVisible: false,\r\n      trackingModalVisible: false,\r\n      selectedRow: null,\r\n      selectedTrackingItem: null,\r\n      searchQuery: '',\r\n      commodityFilter: 'All',\r\n      assigneeFilter: 'All',\r\n      commodityOptions: ['All', 'FUL', 'FAB', 'Power', 'Cable', 'Memory'],\r\n      assigneeOptions: ['All'],\r\n      selectedTab: 'in-progress',\r\n      expandedSideNav: false,\r\n      useFixed: true,\r\n      isLoading: false,\r\n      loadingError: null,\r\n      newAction: {\r\n        commodity: '',\r\n        group: '',\r\n        pn: '',\r\n        test: '',\r\n        deadline: '',\r\n        expectedResolution: '',\r\n        expectedImprovements: '',\r\n        progress: 0,\r\n        status: 'Current',\r\n        priority: 'Medium',\r\n        assignee: '',\r\n        action: '',\r\n        notes: '',\r\n        source: 'manual' // 'manual' or 'pqe'\r\n      },\r\n      newUpdate: '',\r\n      newTrackingUpdate: '',\r\n      newActionNote: '',\r\n      performanceChartData: [],\r\n      alertHistoryData: [],\r\n\r\n      // New Alert functionality\r\n      newAlertUpdate: '',\r\n      alertUpdates: [],\r\n      aiInsight: '',\r\n      isLoadingAiInsight: false,\r\n      performanceChartOptions: {\r\n        title: 'Performance vs Target',\r\n        axes: {\r\n          bottom: {\r\n            title: 'Date',\r\n            mapsTo: 'date',\r\n            scaleType: 'time'\r\n          },\r\n          left: {\r\n            title: 'Performance (%)',\r\n            mapsTo: 'value'\r\n          }\r\n        },\r\n        curve: 'curveMonotoneX',\r\n        height: '300px'\r\n      },\r\n      rows: [],\r\n    };\r\n  },\r\n  computed: {\r\n    // Filter rows based on search and filters\r\n    filteredRows() {\r\n      return this.rows.filter(row => {\r\n        // Filter by commodity\r\n        if (this.commodityFilter !== 'All' && row.commodity !== this.commodityFilter) {\r\n          return false;\r\n        }\r\n\r\n        // Filter by assignee\r\n        if (this.assigneeFilter !== 'All' && row.assignee !== this.assigneeFilter) {\r\n          return false;\r\n        }\r\n\r\n        // Filter by search query\r\n        if (this.searchQuery) {\r\n          const query = this.searchQuery.toLowerCase();\r\n          return (\r\n            row.commodity.toLowerCase().includes(query) ||\r\n            row.group.toLowerCase().includes(query) ||\r\n            row.pn.toLowerCase().includes(query) ||\r\n            row.action.toLowerCase().includes(query) ||\r\n            row.assignee.toLowerCase().includes(query)\r\n          );\r\n        }\r\n\r\n        return true;\r\n      });\r\n    },\r\n\r\n    // In-progress items - actively being worked on (includes current items)\r\n    inProgressItems() {\r\n      return this.filteredRows.filter(row =>\r\n        row.status === 'In-Progress' || row.status === 'Active' || row.status === 'Working' ||\r\n        row.status === 'Current' || row.status === 'New' || row.status === 'Open'\r\n      );\r\n    },\r\n\r\n    // Monitored items - items being tracked but not actively worked\r\n    monitoredItems() {\r\n      return this.filteredRows.filter(row =>\r\n        row.status === 'Monitored' || row.status === 'Watching' || row.status === 'Blocked'\r\n      );\r\n    },\r\n\r\n    // Resolved items - completed or closed items\r\n    resolvedItems() {\r\n      return this.filteredRows.filter(row =>\r\n        row.status === 'Resolved' || row.status === 'Completed' || row.status === 'Closed'\r\n      );\r\n    }\r\n  },\r\n  mounted() {\r\n    // Load action tracker data from API\r\n    this.loadActionTrackerData();\r\n\r\n    // Check if we have query parameters to create a new action\r\n    const query = this.$route.query;\r\n    if (query.createAction === 'true') {\r\n      // Populate the new action form with data from query parameters\r\n      this.newAction = {\r\n        commodity: query.commodity || this.commodityOptions.filter(item => item !== 'All')[0] || '',\r\n        group: query.group || '',\r\n        pn: query.pn || '',\r\n        test: query.test || '',\r\n        deadline: query.deadline || new Date().toISOString().split('T')[0],\r\n        status: query.status || 'Current',\r\n        priority: query.priority || 'Medium',\r\n        assignee: query.assignee || '',\r\n        action: query.action || '',\r\n        notes: query.notes || '',\r\n        source: 'pqe' // Mark as coming from PQE dashboard\r\n      };\r\n\r\n      // Open the new action modal\r\n      this.newActionModalVisible = true;\r\n    }\r\n\r\n    // Check if we have query parameters for alert navigation\r\n    if (query.actionTrackerId && query.tab === 'alerts') {\r\n      // Find the action tracker item and open the tracking modal with alerts tab\r\n      this.$nextTick(() => {\r\n        this.openAlertForActionTracker(query.actionTrackerId, query.alertId);\r\n      });\r\n    }\r\n  },\r\n  methods: {\r\n    // Get authentication config for API calls\r\n    getAuthConfig() {\r\n      const token = localStorage.getItem('token');\r\n      return {\r\n        headers: {\r\n          'Authorization': `Bearer ${token}`\r\n        }\r\n      };\r\n    },\r\n\r\n    // Handle tab selection\r\n    onTabSelected(tabId) {\r\n      this.selectedTab = tabId;\r\n      console.log('Tab selected:', tabId);\r\n    },\r\n\r\n    // Handle item selection from table\r\n    handleItemSelected(item) {\r\n      this.selectedRow = item;\r\n      this.modalVisible = true;\r\n      console.log('Item selected:', item);\r\n    },\r\n\r\n    // Handle item update from table\r\n    handleUpdateItem(item) {\r\n      console.log('Update item:', item);\r\n      this.updateActionItem(item);\r\n    },\r\n\r\n    // Handle tracking item from table\r\n    handleTrackItem(item) {\r\n      console.log('Track item:', item);\r\n      this.selectedTrackingItem = item;\r\n      this.loadPerformanceData(item);\r\n      this.loadAlertHistoryData(item);\r\n      this.loadAlertUpdates(item);\r\n      this.loadAiInsight(item);\r\n      this.trackingModalVisible = true;\r\n    },\r\n\r\n    // Export data functionality\r\n    exportData() {\r\n      console.log('Exporting action tracker data...');\r\n      // TODO: Implement export functionality\r\n      alert('Export functionality will be implemented soon.');\r\n    },\r\n\r\n    // Update assignee options based on loaded data\r\n    updateAssigneeOptions() {\r\n      const assignees = [...new Set(this.rows.map(row => row.assignee).filter(assignee => assignee))];\r\n      this.assigneeOptions = ['All', ...assignees.sort()];\r\n    },\r\n\r\n    // Load performance data for tracking\r\n    async loadPerformanceData(item) {\r\n      try {\r\n        console.log('Loading performance data for:', item.pn);\r\n\r\n        // Generate sample performance data for demonstration\r\n        // In a real implementation, this would fetch actual performance data from the API\r\n        const sampleData = this.generateSamplePerformanceData();\r\n        this.performanceChartData = sampleData;\r\n\r\n      } catch (error) {\r\n        console.error('Error loading performance data:', error);\r\n        this.performanceChartData = [];\r\n      }\r\n    },\r\n\r\n    // Generate sample performance data\r\n    generateSamplePerformanceData() {\r\n      const data = [];\r\n      const today = new Date();\r\n      const target = 95; // Target performance percentage\r\n\r\n      // Generate 30 days of sample data\r\n      for (let i = 29; i >= 0; i--) {\r\n        const date = new Date(today);\r\n        date.setDate(date.getDate() - i);\r\n\r\n        // Generate realistic performance data with some variation\r\n        const basePerformance = 85 + Math.random() * 20; // 85-105%\r\n        const actualPerformance = Math.max(70, Math.min(100, basePerformance + (Math.random() - 0.5) * 10));\r\n\r\n        data.push({\r\n          group: 'Actual Performance',\r\n          date: date.toISOString().split('T')[0],\r\n          value: Math.round(actualPerformance * 100) / 100\r\n        });\r\n\r\n        data.push({\r\n          group: 'Target',\r\n          date: date.toISOString().split('T')[0],\r\n          value: target\r\n        });\r\n      }\r\n\r\n      return data;\r\n    },\r\n\r\n    // Load alert history data for tracking\r\n    async loadAlertHistoryData(item) {\r\n      try {\r\n        console.log('Loading alert history data for:', item.pn);\r\n\r\n        // Generate sample alert history data for demonstration\r\n        // In a real implementation, this would fetch actual alert history from the API\r\n        const historyData = this.generateAlertHistoryData();\r\n        this.alertHistoryData = historyData;\r\n\r\n      } catch (error) {\r\n        console.error('Error loading alert history data:', error);\r\n        this.alertHistoryData = [];\r\n      }\r\n    },\r\n\r\n    // Generate sample alert history data\r\n    generateAlertHistoryData() {\r\n      const data = [];\r\n      const currentDate = new Date();\r\n      const targetRate = 2.5; // Default target rate percentage\r\n\r\n      // Generate 12 months of historical data\r\n      for (let i = 11; i >= 0; i--) {\r\n        const date = new Date(currentDate);\r\n        date.setMonth(date.getMonth() - i);\r\n\r\n        const month = date.toLocaleString('default', { month: 'long' });\r\n        const year = date.getFullYear();\r\n\r\n        // Generate realistic data with some variation\r\n        const volume = Math.floor(Math.random() * 5000) + 1000; // 1000-6000 volume\r\n        const baseDefectRate = targetRate + (Math.random() - 0.5) * 4; // Vary around target\r\n        const actualRate = Math.max(0.1, Math.min(8.0, baseDefectRate)); // 0.1% to 8.0%\r\n        const defects = Math.floor((actualRate / 100) * volume);\r\n        const xFactor = (actualRate / targetRate).toFixed(2);\r\n\r\n        // Determine status based on whether actual rate exceeds target\r\n        const status = actualRate > targetRate ? 'Alert' : 'Normal';\r\n\r\n        // Generate notes for alert conditions\r\n        let notes = null;\r\n        if (status === 'Alert') {\r\n          const alertReasons = [\r\n            'Exceeded target threshold',\r\n            'Process variation detected',\r\n            'Quality issue identified',\r\n            'Supplier batch issue',\r\n            'Equipment calibration needed',\r\n            'Environmental factors'\r\n          ];\r\n          notes = alertReasons[Math.floor(Math.random() * alertReasons.length)];\r\n        }\r\n\r\n        data.push({\r\n          month,\r\n          year,\r\n          status,\r\n          actualRate: actualRate.toFixed(2),\r\n          targetRate: targetRate.toFixed(2),\r\n          xFactor,\r\n          volume: volume.toLocaleString(),\r\n          defects,\r\n          notes\r\n        });\r\n      }\r\n\r\n      return data.reverse(); // Most recent first\r\n    },\r\n\r\n    // Get CSS class for alert history rows\r\n    getAlertRowClass(record) {\r\n      const classes = ['alert-history-row'];\r\n      if (record.status === 'Alert') {\r\n        classes.push('alert-row');\r\n      } else {\r\n        classes.push('normal-row');\r\n      }\r\n      return classes.join(' ');\r\n    },\r\n\r\n    // Update individual action item completion status\r\n    async updateActionItemCompletion(actionItem) {\r\n      try {\r\n        const currentDate = new Date().toISOString().split('T')[0];\r\n\r\n        // Update completion date and last updated\r\n        if (actionItem.completed && !actionItem.completedDate) {\r\n          actionItem.completedDate = currentDate;\r\n        } else if (!actionItem.completed) {\r\n          actionItem.completedDate = null;\r\n        }\r\n        actionItem.lastUpdated = currentDate;\r\n\r\n        // Sort action items by last updated (most recent first)\r\n        this.selectedTrackingItem.actionItems.sort((a, b) => {\r\n          const dateA = new Date(a.lastUpdated || a.completedDate || '1970-01-01');\r\n          const dateB = new Date(b.lastUpdated || b.completedDate || '1970-01-01');\r\n          return dateB - dateA;\r\n        });\r\n\r\n        // Update the action in the API\r\n        const updateData = {\r\n          id: this.selectedTrackingItem.id,\r\n          actionItems: this.selectedTrackingItem.actionItems\r\n        };\r\n        await this.updateActionItem(updateData);\r\n\r\n        // Add an update about the completion status change\r\n        const statusMessage = actionItem.completed ?\r\n          `Action item \"${actionItem.title}\" marked as completed` :\r\n          `Action item \"${actionItem.title}\" marked as incomplete`;\r\n\r\n        await this.addUpdate(this.selectedTrackingItem, statusMessage);\r\n\r\n        console.log('Action item completion status updated:', actionItem);\r\n\r\n      } catch (error) {\r\n        console.error('Error updating action item completion:', error);\r\n        alert(`Failed to update completion status: ${error.message}`);\r\n        // Revert the checkbox state on error\r\n        actionItem.completed = !actionItem.completed;\r\n      }\r\n    },\r\n\r\n    // Toggle add note section for action item\r\n    toggleAddNote(index) {\r\n      const actionItem = this.selectedTrackingItem.actionItems[index];\r\n      actionItem.showAddNote = !actionItem.showAddNote;\r\n      if (actionItem.showAddNote) {\r\n        actionItem.newNote = '';\r\n      }\r\n    },\r\n\r\n    // Cancel add note\r\n    cancelAddNote(index) {\r\n      const actionItem = this.selectedTrackingItem.actionItems[index];\r\n      actionItem.showAddNote = false;\r\n      actionItem.newNote = '';\r\n    },\r\n\r\n    // Add note to specific action item\r\n    async addNoteToActionItem(actionItem) {\r\n      try {\r\n        if (!actionItem.newNote || !actionItem.newNote.trim()) {\r\n          alert('Please enter a note');\r\n          return;\r\n        }\r\n\r\n        const noteData = {\r\n          date: new Date().toISOString().split('T')[0],\r\n          content: actionItem.newNote.trim(),\r\n          author: 'Current User' // In real implementation, get from auth\r\n        };\r\n\r\n        // Initialize notes array if it doesn't exist\r\n        if (!actionItem.notes) {\r\n          actionItem.notes = [];\r\n        }\r\n\r\n        // Add note to the local data (newest first)\r\n        actionItem.notes.unshift(noteData);\r\n\r\n        // Update last updated timestamp\r\n        actionItem.lastUpdated = noteData.date;\r\n\r\n        // Sort action items by last updated (most recent first)\r\n        this.selectedTrackingItem.actionItems.sort((a, b) => {\r\n          const dateA = new Date(a.lastUpdated || a.completedDate || '1970-01-01');\r\n          const dateB = new Date(b.lastUpdated || b.completedDate || '1970-01-01');\r\n          return dateB - dateA;\r\n        });\r\n\r\n        // Save to API\r\n        const updateData = {\r\n          id: this.selectedTrackingItem.id,\r\n          actionItems: this.selectedTrackingItem.actionItems\r\n        };\r\n        await this.updateActionItem(updateData);\r\n\r\n        // Add general update\r\n        await this.addUpdate(this.selectedTrackingItem, `Note added to \"${actionItem.title}\": ${actionItem.newNote.trim()}`);\r\n\r\n        // Clear the input and hide add note section\r\n        actionItem.newNote = '';\r\n        actionItem.showAddNote = false;\r\n\r\n        console.log('Note added to action item:', noteData);\r\n\r\n      } catch (error) {\r\n        console.error('Error adding note to action item:', error);\r\n        alert(`Failed to add note: ${error.message}`);\r\n      }\r\n    },\r\n\r\n    // Add default action items\r\n    addDefaultActionItems() {\r\n      if (!this.selectedTrackingItem.actionItems) {\r\n        this.selectedTrackingItem.actionItems = [];\r\n      }\r\n\r\n      const defaultItems = [\r\n        {\r\n          title: 'Initial Analysis',\r\n          description: 'Perform initial analysis and assessment',\r\n          completed: false,\r\n          completedDate: null,\r\n          lastUpdated: new Date().toISOString().split('T')[0],\r\n          notes: [],\r\n          showAddNote: false,\r\n          newNote: ''\r\n        },\r\n        {\r\n          title: 'Implementation',\r\n          description: 'Implement the proposed solution',\r\n          completed: false,\r\n          completedDate: null,\r\n          lastUpdated: new Date().toISOString().split('T')[0],\r\n          notes: [],\r\n          showAddNote: false,\r\n          newNote: ''\r\n        },\r\n        {\r\n          title: 'Testing & Validation',\r\n          description: 'Test and validate the implementation',\r\n          completed: false,\r\n          completedDate: null,\r\n          lastUpdated: new Date().toISOString().split('T')[0],\r\n          notes: [],\r\n          showAddNote: false,\r\n          newNote: ''\r\n        }\r\n      ];\r\n\r\n      this.selectedTrackingItem.actionItems.push(...defaultItems);\r\n    },\r\n\r\n    // Add tracking update (kept for backward compatibility)\r\n    async addTrackingUpdate() {\r\n      try {\r\n        if (!this.newTrackingUpdate.trim()) {\r\n          alert('Please enter an update description');\r\n          return;\r\n        }\r\n\r\n        const updateData = {\r\n          id: this.selectedTrackingItem.id,\r\n          update: {\r\n            date: new Date().toISOString().split('T')[0],\r\n            content: this.newTrackingUpdate.trim(),\r\n            updatedBy: 'Current User' // In real implementation, get from auth\r\n          }\r\n        };\r\n\r\n        // Add update to the API\r\n        await this.addUpdate(this.selectedTrackingItem, this.newTrackingUpdate.trim());\r\n\r\n        // Update the local data\r\n        if (!this.selectedTrackingItem.updates) {\r\n          this.selectedTrackingItem.updates = [];\r\n        }\r\n        this.selectedTrackingItem.updates.unshift(updateData.update);\r\n\r\n        // Clear the input\r\n        this.newTrackingUpdate = '';\r\n\r\n        // Show success message\r\n        alert('Update added successfully!');\r\n\r\n      } catch (error) {\r\n        console.error('Error adding tracking update:', error);\r\n        alert(`Failed to add update: ${error.message}`);\r\n      }\r\n    },\r\n\r\n    // Load action tracker data from API\r\n    async loadActionTrackerData() {\r\n      try {\r\n        this.isLoading = true;\r\n        this.loadingError = null;\r\n        console.log('Loading action tracker data from API...');\r\n\r\n        // Make API call to get action tracker data\r\n        const response = await fetch('/api-statit2/get_action_tracker_data', {\r\n          method: 'POST',\r\n          headers: {\r\n            'Content-Type': 'application/json'\r\n          },\r\n          body: JSON.stringify({})\r\n        });\r\n\r\n        // Check if response is ok\r\n        if (!response.ok) {\r\n          throw new Error(`Failed to fetch action tracker data: ${response.status} ${response.statusText}`);\r\n        }\r\n\r\n        // Parse response data\r\n        const data = await response.json();\r\n        console.log('Action tracker data loaded:', data);\r\n\r\n        // Check if data has items property\r\n        if (data.items) {\r\n          // Format the data for display\r\n          this.rows = data.items.map(item => ({\r\n            id: item.id,\r\n            commodity: item.commodity,\r\n            group: item.group,\r\n            pn: item.pn,\r\n            editableTest: item.test || 'N/A',\r\n            deadline: item.deadline || '',\r\n            expectedResolution: item.expectedResolution || '',\r\n            expectedImprovements: item.expectedImprovements || '',\r\n            dateStarted: item.dateStarted || '',\r\n            monitorDate: item.monitorDate || '',\r\n            resolutionDate: item.resolutionDate || '',\r\n            progress: item.progress || 0,\r\n            priority: item.priority || 'Medium',\r\n            source: item.source || 'manual',\r\n            completed: item.completed || false,\r\n            completedDate: item.completedDate || null,\r\n            isEditingTest: false,\r\n            isEditingDL: false,\r\n            isEditingER: false,\r\n            action: item.action,\r\n            status: item.status,\r\n            assignee: item.assignee,\r\n            notes: item.actionNotes || [], // Action-specific notes\r\n            actionItems: item.actionItems || [], // Individual action items\r\n            issues: item.issues || [],\r\n            updates: item.updates || [],\r\n            createdAt: item.createdAt,\r\n            updatedAt: item.updatedAt\r\n          }));\r\n\r\n          // Update assignee options\r\n          this.updateAssigneeOptions();\r\n        } else {\r\n          this.rows = [];\r\n          console.warn('No items found in action tracker data');\r\n        }\r\n      } catch (error) {\r\n        console.error('Error loading action tracker data:', error);\r\n        this.loadingError = error.message;\r\n        this.rows = [];\r\n      } finally {\r\n        this.isLoading = false;\r\n      }\r\n    },\r\n\r\n    // Save action tracker data to API\r\n    async saveActionTrackerData(action) {\r\n      try {\r\n        console.log('Saving action tracker data to API:', action);\r\n\r\n        // Make API call to save action tracker data\r\n        const response = await fetch('/api-statit2/save_action_tracker_data', {\r\n          method: 'POST',\r\n          headers: {\r\n            'Content-Type': 'application/json',\r\n            'Authorization': this.getAuthConfig().headers.Authorization\r\n          },\r\n          body: JSON.stringify(action)\r\n        });\r\n\r\n        // Check if response is ok\r\n        if (!response.ok) {\r\n          throw new Error(`Failed to save action tracker data: ${response.status} ${response.statusText}`);\r\n        }\r\n\r\n        // Parse response data\r\n        const data = await response.json();\r\n        console.log('Action tracker data saved:', data);\r\n\r\n        return data;\r\n      } catch (error) {\r\n        console.error('Error saving action tracker data:', error);\r\n        throw error;\r\n      }\r\n    },\r\n\r\n    handleSeeMore(row) {\r\n      this.selectedRow = row;\r\n      this.modalVisible = true;\r\n      console.log('See more clicked:', row);\r\n    },\r\n\r\n    editTest(row) {\r\n      row.isEditingTest = true;\r\n    },\r\n\r\n    async stopEditingTest(row) {\r\n      row.isEditingTest = false;\r\n      console.log('Test name updated:', row.editableTest);\r\n\r\n      try {\r\n        // Update the action in the API\r\n        await this.saveActionTrackerData({\r\n          id: row.id,\r\n          test: row.editableTest\r\n        });\r\n      } catch (error) {\r\n        alert(`Failed to update test: ${error.message}`);\r\n      }\r\n    },\r\n\r\n    editDL(row) {\r\n      row.isEditingDL = true;\r\n    },\r\n\r\n    async stopEditingDL(row) {\r\n      row.isEditingDL = false;\r\n      console.log('Deadline updated:', row.deadline);\r\n\r\n      try {\r\n        // Update the action in the API\r\n        await this.updateActionItem({\r\n          id: row.id,\r\n          deadline: row.deadline\r\n        });\r\n      } catch (error) {\r\n        alert(`Failed to update deadline: ${error.message}`);\r\n      }\r\n    },\r\n\r\n    editER(row) {\r\n      row.isEditingER = true;\r\n    },\r\n\r\n    async stopEditingER(row) {\r\n      row.isEditingER = false;\r\n      console.log('Expected resolution updated:', row.expectedResolution);\r\n\r\n      try {\r\n        // Update the action in the API\r\n        await this.updateActionItem({\r\n          id: row.id,\r\n          expectedResolution: row.expectedResolution\r\n        });\r\n      } catch (error) {\r\n        alert(`Failed to update expected resolution: ${error.message}`);\r\n      }\r\n    },\r\n\r\n    // Format date for display\r\n    formatDate(dateString) {\r\n      if (!dateString) return 'N/A';\r\n\r\n      // Check if the date is in YYYY-MM-DD format\r\n      if (/^\\d{4}-\\d{2}-\\d{2}$/.test(dateString)) {\r\n        const [year, month, day] = dateString.split('-');\r\n        return `${month}/${day}/${year}`;\r\n      }\r\n\r\n      // If it's in MM/DD/YYYY format, return as is\r\n      return dateString;\r\n    },\r\n\r\n    // Update an action item\r\n    async updateActionItem(actionData) {\r\n      try {\r\n        console.log('Updating action item:', actionData);\r\n\r\n        // Make API call to update action tracker data\r\n        const response = await fetch('/api-statit2/update_action_tracker_data', {\r\n          method: 'POST',\r\n          headers: {\r\n            'Content-Type': 'application/json',\r\n            'Authorization': this.getAuthConfig().headers.Authorization\r\n          },\r\n          body: JSON.stringify(actionData)\r\n        });\r\n\r\n        // Check if response is ok\r\n        if (!response.ok) {\r\n          throw new Error(`Failed to update action item: ${response.status} ${response.statusText}`);\r\n        }\r\n\r\n        // Parse response data\r\n        const data = await response.json();\r\n        console.log('Action item updated:', data);\r\n\r\n        // Reload the action tracker data to get the updated list\r\n        await this.loadActionTrackerData();\r\n\r\n        return data;\r\n      } catch (error) {\r\n        console.error('Error updating action item:', error);\r\n        throw error;\r\n      }\r\n    },\r\n\r\n    truncateText(text, maxLength) {\r\n      if (!text) return '';\r\n      if (text.length <= maxLength) return text;\r\n      return text.slice(0, maxLength) + '...';\r\n    },\r\n\r\n    // Update progress for an action item\r\n    async updateProgress(row) {\r\n      try {\r\n        console.log(`Updating progress for ${row.id} to ${row.progress}%`);\r\n\r\n        // Update the action in the API\r\n        await this.updateActionItem({\r\n          id: row.id,\r\n          progress: row.progress\r\n        });\r\n\r\n        // Add an update about the progress change\r\n        await this.addUpdate(row, `Progress updated to ${row.progress}%`);\r\n\r\n        // Show success message\r\n        alert('Progress updated successfully!');\r\n      } catch (error) {\r\n        console.error('Error updating progress:', error);\r\n        alert(`Failed to update progress: ${error.message}`);\r\n      }\r\n    },\r\n\r\n    // Update the entire action item\r\n    async updateEntireAction(row) {\r\n      try {\r\n        console.log('Updating entire action item:', row);\r\n\r\n        // Create an update object with all editable fields\r\n        const updateData = {\r\n          id: row.id,\r\n          commodity: row.commodity,\r\n          group: row.group,\r\n          pn: row.pn,\r\n          test: row.editableTest,\r\n          deadline: row.deadline,\r\n          expectedResolution: row.expectedResolution,\r\n          expectedImprovements: row.expectedImprovements,\r\n          progress: row.progress,\r\n          status: row.status,\r\n          assignee: row.assignee,\r\n          action: row.action,\r\n          notes: row.notes\r\n        };\r\n\r\n        // Update the action in the API\r\n        await this.updateActionItem(updateData);\r\n\r\n        // Add an update about the action update\r\n        await this.addUpdate(row, 'Action details updated');\r\n\r\n        // Close the modal\r\n        this.modalVisible = false;\r\n\r\n        // Show success message\r\n        alert('Action updated successfully!');\r\n      } catch (error) {\r\n        console.error('Error updating action:', error);\r\n        alert(`Failed to update action: ${error.message}`);\r\n      }\r\n    },\r\n\r\n    // Add an update to an action item\r\n    async addUpdate(row, content) {\r\n      try {\r\n        const updateContent = content || this.newUpdate;\r\n\r\n        if (!updateContent) {\r\n          alert('Please enter update content');\r\n          return;\r\n        }\r\n\r\n        console.log(`Adding update to ${row.id}: ${updateContent}`);\r\n\r\n        // Make API call to add update\r\n        const response = await fetch('/api-statit2/add_action_update', {\r\n          method: 'POST',\r\n          headers: {\r\n            'Content-Type': 'application/json',\r\n            'Authorization': this.getAuthConfig().headers.Authorization\r\n          },\r\n          body: JSON.stringify({\r\n            id: row.id,\r\n            update: {\r\n              content: updateContent,\r\n              updatedBy: 'Current User' // In a real app, this would be the logged-in user\r\n            }\r\n          })\r\n        });\r\n\r\n        // Check if response is ok\r\n        if (!response.ok) {\r\n          throw new Error(`Failed to add update: ${response.status} ${response.statusText}`);\r\n        }\r\n\r\n        // Parse response data\r\n        const data = await response.json();\r\n        console.log('Update added:', data);\r\n\r\n        // Clear the update input\r\n        this.newUpdate = '';\r\n\r\n        // Reload the action tracker data to get the updated list\r\n        await this.loadActionTrackerData();\r\n\r\n        // Update the selected row with the latest data\r\n        if (this.selectedRow) {\r\n          const updatedRow = this.rows.find(r => r.id === this.selectedRow.id);\r\n          if (updatedRow) {\r\n            this.selectedRow = updatedRow;\r\n          }\r\n        }\r\n\r\n        // Show success message if this was a manual update\r\n        if (!content) {\r\n          alert('Update added successfully!');\r\n        }\r\n\r\n        return data;\r\n      } catch (error) {\r\n        console.error('Error adding update:', error);\r\n        alert(`Failed to add update: ${error.message}`);\r\n        throw error;\r\n      }\r\n    },\r\n\r\n    openNewActionModal() {\r\n      // Reset the form\r\n      this.newAction = {\r\n        commodity: this.commodityOptions.filter(item => item !== 'All')[0] || '',\r\n        group: '',\r\n        pn: '',\r\n        test: '',\r\n        deadline: new Date().toISOString().split('T')[0],\r\n        status: 'In-Progress',\r\n        assignee: '',\r\n        action: '',\r\n        notes: ''\r\n      };\r\n      // Open the modal\r\n      this.newActionModalVisible = true;\r\n    },\r\n\r\n    async createNewAction() {\r\n      // Validate required fields\r\n      const requiredFields = ['commodity', 'group', 'action'];\r\n      const missingFields = requiredFields.filter(field => !this.newAction[field]);\r\n\r\n      if (missingFields.length > 0) {\r\n        // Show error message for missing fields\r\n        const fieldNames = missingFields.map(field => {\r\n          switch(field) {\r\n            case 'commodity': return 'Process/Commodity';\r\n            case 'group': return 'Part Group';\r\n            case 'action': return 'Action Description';\r\n            default: return field;\r\n          }\r\n        });\r\n\r\n        alert(`Please fill in the required fields: ${fieldNames.join(', ')}`);\r\n        return;\r\n      }\r\n\r\n      try {\r\n        // Ensure deadline is set\r\n        if (!this.newAction.deadline) {\r\n          // Set default deadline to 30 days from now if not provided\r\n          const defaultDate = new Date();\r\n          defaultDate.setDate(defaultDate.getDate() + 30);\r\n          this.newAction.deadline = `${defaultDate.getFullYear()}-${String(defaultDate.getMonth() + 1).padStart(2, '0')}-${String(defaultDate.getDate()).padStart(2, '0')}`;\r\n        }\r\n\r\n        // Format the expected resolution date\r\n        let formattedExpectedResolution = '';\r\n        if (this.newAction.expectedResolution) {\r\n          try {\r\n            const expectedResolutionDate = new Date(this.newAction.expectedResolution);\r\n            formattedExpectedResolution = `${expectedResolutionDate.getFullYear()}-${String(expectedResolutionDate.getMonth() + 1).padStart(2, '0')}-${String(expectedResolutionDate.getDate()).padStart(2, '0')}`;\r\n          } catch (error) {\r\n            console.error('Error formatting expected resolution date:', error);\r\n            formattedExpectedResolution = this.newAction.expectedResolution; // Use as-is if there's an error\r\n          }\r\n        }\r\n\r\n        // Create the new action object\r\n        const newActionItem = {\r\n          commodity: this.newAction.commodity,\r\n          group: this.newAction.group,\r\n          pn: this.newAction.pn || `${this.newAction.group}-${Date.now().toString().slice(-6)}`, // Generate a PN if not provided\r\n          test: this.newAction.test || 'N/A',\r\n          deadline: this.newAction.deadline,\r\n          expectedResolution: formattedExpectedResolution,\r\n          expectedImprovements: this.newAction.expectedImprovements || '',\r\n          progress: this.newAction.progress || 0,\r\n          action: this.newAction.action,\r\n          status: this.newAction.status || 'In-Progress',\r\n          assignee: this.newAction.assignee || 'Unassigned',\r\n          notes: this.newAction.notes || ''\r\n        };\r\n\r\n        // Save the new action to the API\r\n        const savedAction = await this.saveActionTrackerData(newActionItem);\r\n        console.log('New action saved to API:', savedAction);\r\n\r\n        // Reload the action tracker data to get the updated list\r\n        await this.loadActionTrackerData();\r\n\r\n        // Close the modal\r\n        this.newActionModalVisible = false;\r\n\r\n        // Show a success message\r\n        alert('Action item created successfully!');\r\n      } catch (error) {\r\n        console.error('Error creating new action:', error);\r\n        alert(`Failed to create action: ${error.message}`);\r\n      }\r\n    },\r\n\r\n    // Open alert for specific action tracker item (called from PQE dashboard navigation)\r\n    async openAlertForActionTracker(actionTrackerId, alertId) {\r\n      try {\r\n        // Wait for data to load\r\n        await this.loadActionTrackerData();\r\n\r\n        // Find the action tracker item\r\n        const item = this.rows.find(row => row.id === actionTrackerId);\r\n        if (item) {\r\n          // Open the tracking modal with the alerts tab\r\n          this.selectedTrackingItem = item;\r\n          this.loadPerformanceData(item);\r\n          this.loadAlertHistoryData(item);\r\n          this.loadAlertUpdates(item);\r\n          this.loadAiInsight(item);\r\n          this.trackingModalVisible = true;\r\n\r\n          // Switch to the alerts tab after modal opens\r\n          this.$nextTick(() => {\r\n            const alertsTab = document.getElementById('new-alerts-tab');\r\n            if (alertsTab) {\r\n              alertsTab.click();\r\n            }\r\n          });\r\n        } else {\r\n          console.error('Action tracker item not found:', actionTrackerId);\r\n        }\r\n      } catch (error) {\r\n        console.error('Error opening alert for action tracker:', error);\r\n      }\r\n    },\r\n\r\n    // Load alert updates for the selected item\r\n    async loadAlertUpdates(item) {\r\n      try {\r\n        const response = await fetch('/api-statit2/get_pqe_alert_history', {\r\n          method: 'POST',\r\n          headers: {\r\n            'Content-Type': 'application/json',\r\n            ...this.getAuthConfig().headers\r\n          },\r\n          body: JSON.stringify({\r\n            actionTrackerId: item.id,\r\n            pqeOwner: this.$route.query.pqeOwner || 'Unknown'\r\n          })\r\n        });\r\n\r\n        if (!response.ok) {\r\n          throw new Error(`Failed to fetch alert history: ${response.status}`);\r\n        }\r\n\r\n        const data = await response.json();\r\n        if (data.status_res === 'success') {\r\n          this.alertUpdates = data.alert_history || [];\r\n        } else {\r\n          console.error('Failed to load alert updates:', data.message);\r\n          this.alertUpdates = [];\r\n        }\r\n      } catch (error) {\r\n        console.error('Error loading alert updates:', error);\r\n        this.alertUpdates = [];\r\n      }\r\n    },\r\n\r\n    // Load AI insight for the selected item\r\n    async loadAiInsight(item) {\r\n      this.isLoadingAiInsight = true;\r\n      try {\r\n        const alertData = {\r\n          category: item.group || 'Unknown',\r\n          severity: 'Medium', // Default severity\r\n          xFactor: '1.5', // Default X-factor\r\n          status: item.status || 'Unknown'\r\n        };\r\n\r\n        const response = await fetch('/api-statit2/get_pqe_alert_ai_insight', {\r\n          method: 'POST',\r\n          headers: {\r\n            'Content-Type': 'application/json',\r\n            ...this.getAuthConfig().headers\r\n          },\r\n          body: JSON.stringify({\r\n            actionTrackerId: item.id,\r\n            alertData: alertData,\r\n            pqeOwner: this.$route.query.pqeOwner || 'Unknown'\r\n          })\r\n        });\r\n\r\n        if (!response.ok) {\r\n          throw new Error(`Failed to fetch AI insight: ${response.status}`);\r\n        }\r\n\r\n        const data = await response.json();\r\n        if (data.status_res === 'success') {\r\n          this.aiInsight = data.ai_insight || 'No insight available';\r\n        } else {\r\n          console.error('Failed to load AI insight:', data.message);\r\n          this.aiInsight = 'Unable to generate insight at this time';\r\n        }\r\n      } catch (error) {\r\n        console.error('Error loading AI insight:', error);\r\n        this.aiInsight = 'Unable to generate insight at this time';\r\n      } finally {\r\n        this.isLoadingAiInsight = false;\r\n      }\r\n    },\r\n\r\n    // Add alert update\r\n    async addAlertUpdate() {\r\n      if (!this.newAlertUpdate.trim()) {\r\n        alert('Please enter an update');\r\n        return;\r\n      }\r\n\r\n      try {\r\n        const response = await fetch('/api-statit2/add_pqe_alert_update', {\r\n          method: 'POST',\r\n          headers: {\r\n            'Content-Type': 'application/json',\r\n            ...this.getAuthConfig().headers\r\n          },\r\n          body: JSON.stringify({\r\n            actionTrackerId: this.selectedTrackingItem.id,\r\n            update: this.newAlertUpdate,\r\n            updatedBy: this.$route.query.pqeOwner || 'Unknown',\r\n            alertType: 'PQE Update'\r\n          })\r\n        });\r\n\r\n        if (!response.ok) {\r\n          throw new Error(`Failed to add alert update: ${response.status}`);\r\n        }\r\n\r\n        const data = await response.json();\r\n        if (data.status_res === 'success') {\r\n          // Add the new alert to the history\r\n          this.alertUpdates.push(data.alert);\r\n          this.newAlertUpdate = '';\r\n          console.log('Alert update added successfully');\r\n        } else {\r\n          throw new Error(data.message || 'Failed to add alert update');\r\n        }\r\n      } catch (error) {\r\n        console.error('Error adding alert update:', error);\r\n        alert('Failed to add alert update: ' + error.message);\r\n      }\r\n    }\r\n  },\r\n};\r\n\r\n</script>\r\n"]}]}