{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\Validation2\\Validation2.vue?vue&type=style&index=0&id=2af575b9&scoped=true&lang=scss", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\Validation2\\Validation2.vue", "mtime": 1748970271013}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\@vue\\cli-service\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Validation2.vue"], "names": [], "mappings": ";AAs6BA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "Validation2.vue", "sourceRoot": "src/views/Validation2", "sourcesContent": ["<template>\n  <div class=\"validation2-container\">\n    <MainHeader :expandedSideNav=\"expandedSideNav\" :useFixed=\"useFixed\" />\n\n    <!-- Classification Modal -->\n    <cv-modal\n      v-if=\"showClassificationModal\"\n      :visible=\"showClassificationModal\"\n      @modal-hidden=\"closeClassificationModal\"\n      size=\"default\"\n    >\n      <template slot=\"title\">Edit Classification</template>\n      <template slot=\"content\">\n        <div class=\"modal-content-inner\">\n          <p><strong>Defect ID:</strong> {{ selectedDefect ? selectedDefect.DEFECT_ID : '' }}</p>\n          <p class=\"description-text\"><strong>Description:</strong> {{ selectedDefect ? selectedDefect.DESCRIPTION : '' }}</p>\n\n          <div class=\"modal-form\">\n            <div class=\"form-group\">\n              <cv-dropdown\n                v-model=\"editClassification.primary\"\n                label=\"Primary Classification\"\n                :items=\"primaryClassificationOptions\"\n              />\n            </div>\n\n            <div class=\"form-group\">\n              <cv-dropdown\n                v-model=\"editClassification.subcategory\"\n                label=\"Subcategory\"\n                :items=\"subcategoryOptions\"\n              />\n            </div>\n          </div>\n        </div>\n      </template>\n      <template slot=\"secondary-button\">Cancel</template>\n      <template slot=\"primary-button\">Save</template>\n      <template slot=\"actions\">\n        <cv-button kind=\"secondary\" @click=\"closeClassificationModal\">Cancel</cv-button>\n        <cv-button kind=\"primary\" @click=\"saveClassification\">Save</cv-button>\n      </template>\n    </cv-modal>\n\n    <!-- Batch Results Modal -->\n    <cv-modal\n      v-if=\"showBatchResultsModal\"\n      :visible=\"showBatchResultsModal\"\n      @modal-hidden=\"closeBatchResultsModal\"\n      size=\"large\"\n    >\n      <template slot=\"title\">Batch Processing Results</template>\n      <template slot=\"content\">\n        <div class=\"batch-results-content\">\n          <p>Successfully processed {{ batchResults.length }} defects</p>\n\n          <cv-data-table\n            :columns=\"batchResultsColumns\"\n            :data=\"batchResults\"\n            :pagination=\"{ pageSize: 10 }\"\n            title=\"\"\n          >\n            <template slot=\"data\">\n              <cv-data-table-row\n                v-for=\"(result, index) in batchResults\"\n                :key=\"index\"\n              >\n                <cv-data-table-cell>{{ result.defectId }}</cv-data-table-cell>\n                <cv-data-table-cell>{{ result.description }}</cv-data-table-cell>\n                <cv-data-table-cell>\n                  <cv-tag :kind=\"getClassificationTagKind(result.classification.primary)\">\n                    {{ result.classification.primary }}\n                  </cv-tag>\n                </cv-data-table-cell>\n                <cv-data-table-cell>\n                  <cv-tag :kind=\"getClassificationTagKind(result.classification.subcategory)\">\n                    {{ result.classification.subcategory }}\n                  </cv-tag>\n                </cv-data-table-cell>\n              </cv-data-table-row>\n            </template>\n          </cv-data-table>\n        </div>\n      </template>\n      <template slot=\"actions\">\n        <cv-button kind=\"primary\" @click=\"closeBatchResultsModal\">Close</cv-button>\n      </template>\n    </cv-modal>\n\n    <main id=\"main-content\" class=\"main-content\">\n      <!-- Page Header -->\n      <div class=\"page-header\">\n        <h1>Defect Validation 2.0</h1>\n        <p>Analyze and validate defects with AI assistance</p>\n      </div>\n\n      <!-- Summary Tiles -->\n      <div class=\"summary-section\">\n        <cv-tile class=\"summary-tile\">\n          <h4>Total Validations</h4>\n          <p class=\"tile-value\">{{ validationsSummary.total }}</p>\n        </cv-tile>\n        <cv-tile class=\"summary-tile\">\n          <h4>Validated</h4>\n          <p class=\"tile-value validated\">{{ validationsSummary.validated }}</p>\n          <p class=\"tile-percentage\">{{ validationsSummary.validatedPercentage }}%</p>\n        </cv-tile>\n        <cv-tile class=\"summary-tile\">\n          <h4>Unvalidated</h4>\n          <p class=\"tile-value unvalidated\">{{ validationsSummary.unvalidated }}</p>\n          <p class=\"tile-percentage\">{{ validationsSummary.unvalidatedPercentage }}%</p>\n        </cv-tile>\n      </div>\n\n      <!-- Controls Row -->\n      <div class=\"controls-row\">\n        <div class=\"filter-group\">\n          <cv-dropdown\n            v-model=\"selectedPQEOwner\"\n            label=\"PQE Owner\"\n            :items=\"pqeOwnerOptions\"\n            @change=\"handlePQEOwnerChange\"\n          />\n        </div>\n\n        <div class=\"filter-group\">\n          <cv-dropdown\n            v-model=\"selectedTimeFilter\"\n            label=\"Time Period\"\n            :items=\"timeFilterOptions\"\n            @change=\"handleTimeFilterChange\"\n          />\n        </div>\n\n        <div class=\"button-group\">\n          <cv-button\n            kind=\"primary\"\n            @click=\"processNewDefects\"\n            :disabled=\"processingDefects\"\n          >\n            {{ processingDefects ? 'Processing...' : 'Process All New Defects' }}\n          </cv-button>\n\n          <div class=\"batch-process-controls\">\n            <cv-number-input\n              v-model=\"batchSize\"\n              label=\"Batch Size\"\n              :min=\"1\"\n              :max=\"20\"\n              :disabled=\"processingDefects\"\n            />\n            <cv-button\n              kind=\"secondary\"\n              @click=\"processLimitedBatch\"\n              :disabled=\"processingDefects\"\n            >\n              Process Batch & View Results\n            </cv-button>\n          </div>\n        </div>\n\n        <div v-if=\"processingMessage\" class=\"processing-message\">\n          <cv-inline-notification\n            kind=\"info\"\n            :title=\"'Processing Status'\"\n            :sub-title=\"processingMessage\"\n          />\n        </div>\n      </div>\n\n      <div class=\"pareto-section\">\n        <h2>Defect Classification Breakdown</h2>\n        <div v-if=\"loadingCounts\" class=\"loading-indicator\">\n          <cv-loading />\n        </div>\n        <div v-else-if=\"classificationCounts\" class=\"chart-container\">\n          <div class=\"pareto-chart\" data-carbon-theme=\"g90\">\n            <HBarChart\n              :data=\"chartData\"\n              :loading=\"loadingCounts\"\n              title=\"Defect Classification Breakdown\"\n              height=\"400px\"\n            />\n          </div>\n        </div>\n      </div>\n\n      <div class=\"defects-table-section\">\n        <h2>Defect Details</h2>\n        <div v-if=\"loadingData\" class=\"loading-indicator\">\n          <cv-loading />\n        </div>\n        <div v-else class=\"table-container\">\n          <cv-data-table\n            :columns=\"tableColumns\"\n            :data=\"tableData\"\n            :pagination=\"{ pageSize: 10 }\"\n            :sortable=\"true\"\n          >\n            <template slot=\"data\">\n              <cv-data-table-row\n                v-for=\"(row, rowIndex) in tableData\"\n                :key=\"rowIndex\"\n                :value=\"`${row.DEFECT_ID}`\"\n              >\n                <cv-data-table-cell>{{ row.DEFECT_ID }}</cv-data-table-cell>\n                <cv-data-table-cell>{{ formatDate(row.MFSDATE) }}</cv-data-table-cell>\n                <cv-data-table-cell>{{ row.STAGE }}</cv-data-table-cell>\n                <cv-data-table-cell>{{ row.DESCRIPTION }}</cv-data-table-cell>\n                <cv-data-table-cell>\n                  <div class=\"classification-cell\">\n                    <div v-if=\"getPrimaryClassification(row)\" class=\"classification-tag\" :class=\"getPrimaryClassification(row).toLowerCase()\">\n                      {{ getPrimaryClassification(row) }}\n                    </div>\n                    <div v-else class=\"classification-tag unclassified\">\n                      Unclassified\n                    </div>\n                  </div>\n                </cv-data-table-cell>\n                <cv-data-table-cell>\n                  <div class=\"classification-cell\">\n                    <div v-if=\"getSubcategoryClassification(row)\" class=\"classification-tag subcategory\" :class=\"getSubcategoryClass(row)\">\n                      {{ getSubcategoryClassification(row) }}\n                    </div>\n                    <div v-else class=\"classification-tag unclassified\">\n                      Unclassified\n                    </div>\n                    <button\n                      v-if=\"row.DEFECT_ID\"\n                      class=\"edit-button\"\n                      @click=\"openClassificationModal(row)\"\n                    >\n                      Edit\n                    </button>\n                  </div>\n                </cv-data-table-cell>\n              </cv-data-table-row>\n            </template>\n          </cv-data-table>\n        </div>\n      </div>\n    </main>\n  </div>\n</template>\n\n<script>\nimport MainHeader from \"@/components/MainHeader\";\nimport HBarChart from \"@/components/HBarChart/HBarChart\";\nimport {\n  CvModal,\n  CvButton,\n  CvDropdown,\n  CvTile,\n  CvDataTable,\n  CvDataTableRow,\n  CvDataTableCell,\n  CvTag,\n  CvNumberInput,\n  CvInlineNotification,\n  CvLoading\n} from '@carbon/vue';\n\nexport default {\n  name: \"ValidationAiPage\",\n  components: {\n    MainHeader,\n    HBarChart,\n    CvModal,\n    CvButton,\n    CvDropdown,\n    CvTile,\n    CvDataTable,\n    CvDataTableRow,\n    CvDataTableCell,\n    CvTag,\n    CvNumberInput,\n    CvInlineNotification,\n    CvLoading\n  },\n  data() {\n    return {\n      expandedSideNav: true,\n      useFixed: false,\n\n      // PQE Owner data\n      selectedPQEOwner: 'All',\n      pqeOwners: [],\n      pqeOwnerOptions: [{ label: 'All PQE Owners', value: 'All' }],\n\n      // Time filter data\n      selectedTimeFilter: \"month\",\n      timeFilterOptions: [\n        { label: \"Past Month\", value: \"month\" },\n        { label: \"Past Week\", value: \"week\" },\n        { label: \"Past Day\", value: \"day\" }\n      ],\n\n      // Classification options for dropdowns\n      primaryClassificationOptions: [\n        { label: \"Mechanical\", value: \"Mechanical\" },\n        { label: \"Functional\", value: \"Functional\" },\n        { label: \"Need More Info\", value: \"Need More Info\" }\n      ],\n      subcategoryOptions: [\n        { label: \"Scratches\", value: \"Scratches\" },\n        { label: \"Bent\", value: \"Bent\" },\n        { label: \"Plugging\", value: \"Plugging\" },\n        { label: \"Discolor\", value: \"Discolor\" },\n        { label: \"Misalignment\", value: \"Misalignment\" },\n        { label: \"Need More Info\", value: \"Need More Info\" },\n        { label: \"Other\", value: \"Other\" }\n      ],\n\n      // Loading states\n      loadingData: false,\n      loadingCounts: false,\n      processingDefects: false,\n      processingMessage: '',\n\n      // Data arrays\n      validationData: [],\n      filteredData: [],\n      primaryFilter: 'all',\n      subcategoryFilter: 'all',\n      classificationCounts: null,\n\n      // Modal states\n      showClassificationModal: false,\n      showBatchResultsModal: false,\n      selectedDefect: null,\n      editClassification: {\n        primary: 'Mechanical',\n        subcategory: 'Other'\n      },\n\n      // Batch processing\n      batchSize: 5,\n      batchResults: [],\n\n      // Table configurations\n      tableColumns: [\n        { label: \"Defect ID\", key: \"defect_id\" },\n        { label: \"Date\", key: \"date\" },\n        { label: \"Stage\", key: \"stage\" },\n        { label: \"Description\", key: \"description\" },\n        { label: \"Primary\", key: \"primary\" },\n        { label: \"Subcategory\", key: \"subcategory\" }\n      ],\n      batchResultsColumns: [\n        { label: \"Defect ID\", key: \"defectId\" },\n        { label: \"Description\", key: \"description\" },\n        { label: \"Primary\", key: \"primary\" },\n        { label: \"Subcategory\", key: \"subcategory\" }\n      ],\n\n      // Summary data\n      validationsSummary: {\n        total: 0,\n        validated: 0,\n        unvalidated: 0,\n        validatedPercentage: 0,\n        unvalidatedPercentage: 0\n      }\n    };\n  },\n  computed: {\n    tableData() {\n      return this.filteredData || [];\n    },\n    chartData() {\n      if (!this.classificationCounts) return [];\n\n      // Format the data for the horizontal bar chart\n      return [\n        { group: \"Classification\", key: \"Mechanical\", value: this.classificationCounts.Mechanical || 0 },\n        { group: \"Classification\", key: \"Functional\", value: this.classificationCounts.Functional || 0 },\n        { group: \"Classification\", key: \"Unclassified\", value: this.classificationCounts.Unclassified || 0 }\n      ];\n    }\n  },\n  mounted() {\n    this.loadPQEOwners();\n    this.fetchValidationData();\n    this.fetchClassificationCounts();\n  },\n  methods: {\n    // Load PQE owners from API\n    async loadPQEOwners() {\n      try {\n        const config = this.getAuthConfig();\n        const response = await fetch('/api-statit2/get_pqe_owners', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({})\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch PQE owners: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n        if (data.status_res === 'success') {\n          this.pqeOwners = data.pqe_owners || [];\n\n          // Update dropdown options\n          this.pqeOwnerOptions = [\n            { label: 'All PQE Owners', value: 'All' },\n            ...this.pqeOwners.map(owner => ({ label: owner, value: owner }))\n          ];\n\n          console.log(`Loaded ${this.pqeOwners.length} PQE owners`);\n        } else {\n          console.error('Failed to load PQE owners:', data.message);\n        }\n      } catch (error) {\n        console.error('Error loading PQE owners:', error);\n      }\n    },\n\n    // Get authentication config\n    getAuthConfig() {\n      return {\n        headers: {\n          'Authorization': 'Bearer ' + (localStorage.getItem('token') || ''),\n          'X-User-ID': localStorage.getItem('userId') || ''\n        }\n      };\n    },\n\n    // Handle PQE owner change\n    async handlePQEOwnerChange() {\n      console.log(`Selected PQE owner: ${this.selectedPQEOwner}`);\n      await this.fetchValidationData();\n      await this.fetchClassificationCounts();\n      this.updateValidationsSummary();\n    },\n\n    // Handle time filter change\n    async handleTimeFilterChange() {\n      console.log(`Selected time filter: ${this.selectedTimeFilter}`);\n      await this.fetchValidationData();\n      await this.fetchClassificationCounts();\n      this.updateValidationsSummary();\n    },\n\n    async fetchValidationData() {\n      this.loadingData = true;\n      try {\n        const response = await fetch(`${process.env.VUE_APP_API_PATH || '/api-statit2/'}get_validation_data`, {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\"\n          },\n          body: JSON.stringify({\n            timeFilter: this.selectedTimeFilter,\n            pqeOwner: this.selectedPQEOwner !== 'All' ? this.selectedPQEOwner : null\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n\n        const data = await response.json();\n        this.validationData = data.data;\n\n        // Apply filters to the data\n        this.applyFilters();\n        this.updateValidationsSummary();\n      } catch (error) {\n        console.error(\"Error fetching validation data:\", error);\n      } finally {\n        this.loadingData = false;\n      }\n    },\n\n    // Update validations summary\n    updateValidationsSummary() {\n      if (!this.validationData || this.validationData.length === 0) {\n        this.validationsSummary = {\n          total: 0,\n          validated: 0,\n          unvalidated: 0,\n          validatedPercentage: 0,\n          unvalidatedPercentage: 0\n        };\n        return;\n      }\n\n      const total = this.validationData.length;\n      const validated = this.validationData.filter(item =>\n        item.classification &&\n        (typeof item.classification === 'string' || item.classification.primary)\n      ).length;\n      const unvalidated = total - validated;\n\n      this.validationsSummary = {\n        total,\n        validated,\n        unvalidated,\n        validatedPercentage: total > 0 ? Math.round((validated / total) * 100) : 0,\n        unvalidatedPercentage: total > 0 ? Math.round((unvalidated / total) * 100) : 0\n      };\n    },\n\n    async applyFilters() {\n      this.loadingData = true;\n      // Set default values for filters since dropdowns were removed\n      this.primaryFilter = 'all';\n      this.subcategoryFilter = 'all';\n\n      try {\n        // Use the server-side filtering endpoint\n        const response = await fetch(`${process.env.VUE_APP_API_PATH || '/api-statit2/'}query_by_classification`, {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\"\n          },\n          body: JSON.stringify({\n            timeFilter: this.selectedTimeFilter,\n            primaryFilter: this.primaryFilter,\n            subcategoryFilter: this.subcategoryFilter\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n\n        const data = await response.json();\n        this.filteredData = data.data;\n\n        // Update the validation data as well to keep it in sync\n        this.validationData = data.data;\n      } catch (error) {\n        console.error(\"Error applying filters:\", error);\n\n        // Fallback to client-side filtering if server-side fails\n        if (!this.validationData || this.validationData.length === 0) {\n          this.filteredData = [];\n          return;\n        }\n\n        // Filter the data based on primary and subcategory filters\n        this.filteredData = this.validationData.filter(item => {\n          // Handle missing classification\n          if (!item.classification) {\n            return (this.primaryFilter === 'all' || this.primaryFilter === 'Need More Info') &&\n                   (this.subcategoryFilter === 'all' || this.subcategoryFilter === 'Need More Info');\n          }\n\n          // Handle string classification (backward compatibility)\n          if (typeof item.classification === 'string') {\n            const primary = item.classification;\n            return (this.primaryFilter === 'all' || this.primaryFilter === primary) &&\n                   (this.subcategoryFilter === 'all' || this.subcategoryFilter === 'Other');\n          }\n\n          // Handle object classification\n          const primary = item.classification.primary || 'Need More Info';\n          const subcategory = item.classification.subcategory || 'Need More Info';\n\n          return (this.primaryFilter === 'all' || this.primaryFilter === primary) &&\n                 (this.subcategoryFilter === 'all' || this.subcategoryFilter === subcategory);\n        });\n      } finally {\n        this.loadingData = false;\n      }\n    },\n\n    async fetchClassificationCounts() {\n      this.loadingCounts = true;\n      try {\n        const response = await fetch(`${process.env.VUE_APP_API_PATH || '/api-statit2/'}get_classification_counts`, {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\"\n          },\n          body: JSON.stringify({\n            timeFilter: this.selectedTimeFilter\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n\n        const data = await response.json();\n        this.classificationCounts = data.data;\n      } catch (error) {\n        console.error(\"Error fetching classification counts:\", error);\n      } finally {\n        this.loadingCounts = false;\n      }\n    },\n\n    async processNewDefects() {\n      this.processingDefects = true;\n      this.processingMessage = 'Starting AI classification...';\n      try {\n        const response = await fetch(`${process.env.VUE_APP_API_PATH || '/api-statit2/'}process_new_defects`, {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\"\n          },\n          body: JSON.stringify({\n            api_key: 'A-9UZe48N156o-lLuxvjR5IePXDhKwrVHu61UciQ9afl',\n            project_id: '8a74c0cb-b3c8-4fd7-9823-8301276169de',\n            timeFilter: this.selectedTimeFilter // Pass the current time filter\n          })\n        });\n\n        // Check for HTTP errors\n        if (!response.ok) {\n          const errorText = await response.text();\n          let errorMessage;\n          try {\n            const errorJson = JSON.parse(errorText);\n            errorMessage = errorJson.message || `HTTP error! status: ${response.status}`;\n          } catch (e) {\n            errorMessage = `HTTP error! status: ${response.status}: ${errorText}`;\n          }\n          throw new Error(errorMessage);\n        }\n\n        const data = await response.json();\n        console.log(\"Processing result:\", data);\n\n        // Check for API errors\n        if (data.status === 'error') {\n          throw new Error(data.message || 'Unknown error occurred');\n        }\n\n        // Update processing message\n        if (data.message) {\n          this.processingMessage = data.message;\n        }\n\n        // Refresh data after processing\n        setTimeout(() => {\n          this.fetchValidationData();\n          this.fetchClassificationCounts();\n          this.processingDefects = false;\n\n          // Keep the message visible for a bit longer\n          setTimeout(() => {\n            this.processingMessage = '';\n          }, 5000);\n        }, 2000);\n      } catch (error) {\n        console.error(\"Error processing defects:\", error);\n        this.processingDefects = false;\n        this.processingMessage = `Error: ${error.message}`;\n\n        // Clear error message after a delay\n        setTimeout(() => {\n          this.processingMessage = '';\n        }, 10000); // Show error for longer (10 seconds)\n      }\n    },\n\n    async updateClassification(defectId, classification) {\n      try {\n        const response = await fetch(`${process.env.VUE_APP_API_PATH || '/api-statit2/'}update_classification`, {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\"\n          },\n          body: JSON.stringify({\n            defectId,\n            classification\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n\n        // Update local data\n        const defect = this.validationData.find(d => d.DEFECT_ID === defectId);\n        if (defect) {\n          defect.classification = classification;\n        }\n\n        // Refresh classification counts\n        this.fetchClassificationCounts();\n      } catch (error) {\n        console.error(\"Error updating classification:\", error);\n      }\n    },\n\n\n    resetFilters() {\n      // Filters are already set to 'all' in applyFilters method\n      this.applyFilters();\n    },\n\n    formatDate(dateString) {\n      if (!dateString) return '';\n      const date = new Date(dateString);\n      if (isNaN(date.getTime())) return dateString;\n      return date.toLocaleDateString();\n    },\n\n    // Helper methods for classification\n    getPrimaryClassification(row) {\n      if (!row.classification) return null;\n\n      // Handle string classification (backward compatibility)\n      if (typeof row.classification === 'string') {\n        return row.classification;\n      }\n\n      // Handle object classification\n      return row.classification.primary || 'Need More Info';\n    },\n\n    getSubcategoryClassification(row) {\n      if (!row.classification) return null;\n\n      // Handle string classification (backward compatibility)\n      if (typeof row.classification === 'string') {\n        return 'Other';\n      }\n\n      // Handle object classification\n      return row.classification.subcategory || 'Need More Info';\n    },\n\n    getSubcategoryClass(row) {\n      const subcategory = this.getSubcategoryClassification(row);\n      if (!subcategory) return '';\n\n      // Convert to lowercase and remove spaces for CSS class\n      return subcategory.toLowerCase().replace(/\\s+/g, '-');\n    },\n\n    getSubcategoryClassFromString(subcategory) {\n      if (!subcategory) return '';\n\n      // Convert to lowercase and remove spaces for CSS class\n      return subcategory.toLowerCase().replace(/\\s+/g, '-');\n    },\n\n    async processLimitedBatch() {\n      if (this.batchSize < 1 || this.batchSize > 20) {\n        this.processingMessage = 'Batch size must be between 1 and 20';\n        setTimeout(() => {\n          this.processingMessage = '';\n        }, 3000);\n        return;\n      }\n      let token = this.$store.getters.getToken;\n      this.processingDefects = true;\n      this.processingMessage = `Processing batch of ${this.batchSize} defects...`;\n      try {\n        const response = await fetch(`${process.env.VUE_APP_API_PATH || '/api-statit2/'}process_limited_batch`, {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \" + token\n          },\n          body: JSON.stringify({\n            api_key: 'A-9UZe48N156o-lLuxvjR5IePXDhKwrVHu61UciQ9afl',\n            project_id: '8a74c0cb-b3c8-4fd7-9823-8301276169de',\n            timeFilter: this.selectedTimeFilter,\n            batchSize: this.batchSize\n          })\n        });\n\n        // Check for HTTP errors\n        if (!response.ok) {\n          const errorText = await response.text();\n          let errorMessage;\n          try {\n            const errorJson = JSON.parse(errorText);\n            errorMessage = errorJson.message || `HTTP error! status: ${response.status}`;\n          } catch (e) {\n            errorMessage = `HTTP error! status: ${response.status}: ${errorText}`;\n          }\n          throw new Error(errorMessage);\n        }\n\n        const data = await response.json();\n        console.log(\"Batch processing result:\", data);\n\n        // Check for API errors\n        if (data.status === 'error') {\n          throw new Error(data.message || 'Unknown error occurred');\n        }\n\n        // Update processing message\n        if (data.message) {\n          this.processingMessage = data.message;\n        }\n\n        // Store the batch results\n        this.batchResults = data.results || [];\n\n        // Show the batch results modal\n        if (this.batchResults.length > 0) {\n          this.showBatchResultsModal = true;\n        }\n\n        // Refresh data\n        this.fetchValidationData();\n        this.fetchClassificationCounts();\n        this.processingDefects = false;\n\n      } catch (error) {\n        console.error(\"Error processing batch:\", error);\n        this.processingDefects = false;\n        this.processingMessage = `Error: ${error.message}`;\n\n        // Clear error message after a delay\n        setTimeout(() => {\n          this.processingMessage = '';\n        }, 10000); // Show error for longer (10 seconds)\n      }\n    },\n\n    closeBatchResultsModal() {\n      this.showBatchResultsModal = false;\n      this.batchResults = [];\n    },\n\n    // Modal methods\n    openClassificationModal(row) {\n      this.selectedDefect = row;\n\n      // Initialize with current classification or defaults\n      if (row.classification) {\n        if (typeof row.classification === 'string') {\n          // Handle string classification (backward compatibility)\n          this.editClassification = {\n            primary: row.classification,\n            subcategory: 'Other'\n          };\n        } else {\n          // Handle object classification\n          this.editClassification = {\n            primary: row.classification.primary || 'Need More Info',\n            subcategory: row.classification.subcategory || 'Need More Info'\n          };\n        }\n      } else {\n        // Default values for new classification\n        this.editClassification = {\n          primary: 'Mechanical',\n          subcategory: 'Other'\n        };\n      }\n\n      this.showClassificationModal = true;\n    },\n\n    closeClassificationModal() {\n      this.showClassificationModal = false;\n      this.selectedDefect = null;\n    },\n\n    async saveClassification() {\n      if (!this.selectedDefect) return;\n\n      try {\n        const defectId = this.selectedDefect.DEFECT_ID;\n        const classification = { ...this.editClassification };\n\n        const response = await fetch(`${process.env.VUE_APP_API_PATH || '/api-statit2/'}update_classification`, {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\"\n          },\n          body: JSON.stringify({\n            defectId,\n            classification\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n\n        // Update local data\n        const defect = this.validationData.find(d => d.DEFECT_ID === defectId);\n        if (defect) {\n          defect.classification = classification;\n        }\n\n        // Refresh classification counts and apply filters\n        this.fetchClassificationCounts();\n        this.applyFilters();\n\n        // Close the modal\n        this.closeClassificationModal();\n      } catch (error) {\n        console.error(\"Error updating classification:\", error);\n      }\n    },\n\n    // Utility methods for Carbon components\n    getClassificationTagKind(classification) {\n      if (!classification) return 'red';\n\n      const classType = classification.toLowerCase();\n      switch (classType) {\n        case 'mechanical':\n          return 'purple';\n        case 'functional':\n          return 'blue';\n        case 'need more info':\n          return 'orange';\n        default:\n          return 'gray';\n      }\n    },\n\n    formatDate(dateString) {\n      if (!dateString) return 'N/A';\n      try {\n        const date = new Date(dateString);\n        return date.toLocaleDateString();\n      } catch (error) {\n        return dateString;\n      }\n    }\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n.validation2-container {\n  min-height: 100vh;\n  background-color: #161616;\n  color: #f4f4f4;\n}\n\n.main-content {\n  padding: 2rem;\n}\n\n.page-header {\n  margin-bottom: 2rem;\n\n  h1 {\n    font-size: 2rem;\n    margin-bottom: 0.5rem;\n  }\n\n  p {\n    color: #c6c6c6;\n  }\n}\n\n.summary-section {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 1rem;\n  margin-bottom: 2rem;\n\n  .summary-tile {\n    padding: 1.5rem;\n\n    h4 {\n      margin: 0 0 0.5rem 0;\n      font-size: 0.875rem;\n      color: #c6c6c6;\n      text-transform: uppercase;\n      letter-spacing: 0.16px;\n    }\n\n    .tile-value {\n      font-size: 2rem;\n      font-weight: 600;\n      margin: 0;\n\n      &.validated {\n        color: #42be65;\n      }\n\n      &.unvalidated {\n        color: #fa4d56;\n      }\n    }\n\n    .tile-percentage {\n      font-size: 0.875rem;\n      color: #c6c6c6;\n      margin: 0.25rem 0 0 0;\n    }\n  }\n}\n\n.controls-row {\n  display: flex;\n  align-items: flex-start;\n  margin-bottom: 2rem;\n  gap: 1rem;\n  flex-wrap: wrap;\n\n  .filter-group {\n    min-width: 200px;\n  }\n}\n\n.time-period-dropdown {\n  display: flex;\n  flex-direction: column;\n  min-width: 200px;\n}\n\n.button-group {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n\n.batch-process-controls {\n  display: flex;\n  align-items: flex-end;\n  gap: 1rem;\n}\n\n.batch-size-input {\n  display: flex;\n  flex-direction: column;\n  width: 80px;\n}\n\n.cv-input {\n  background-color: #262626;\n  color: #f4f4f4;\n  border: 1px solid #525252;\n  border-radius: 4px;\n  padding: 0.5rem;\n  font-size: 0.875rem;\n  height: 40px;\n  width: 100%;\n}\n\n.dropdown-label {\n  font-size: 0.75rem;\n  margin-bottom: 0.5rem;\n  color: #c6c6c6;\n}\n\n.cv-dropdown {\n  background-color: #262626;\n  color: #f4f4f4;\n  border: 1px solid #525252;\n  border-radius: 4px;\n  padding: 0.5rem;\n  font-size: 0.875rem;\n  height: 40px;\n  width: 100%;\n  appearance: none;\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16'%3E%3Cpath fill='%23f4f4f4' d='M8 11L3 6h10z'/%3E%3C/svg%3E\");\n  background-repeat: no-repeat;\n  background-position: right 0.5rem center;\n  cursor: pointer;\n}\n\n.cv-dropdown:focus {\n  outline: 2px solid #0f62fe;\n  outline-offset: -2px;\n}\n\n.pareto-section, .defects-table-section {\n  margin-bottom: 2rem;\n\n  h2 {\n    margin-bottom: 1rem;\n    font-size: 1.5rem;\n  }\n}\n\n.chart-container {\n  height: 400px;\n  width: 100%;\n}\n\n.loading-indicator {\n  display: flex;\n  justify-content: center;\n  padding: 2rem;\n}\n\n.table-container {\n  margin-top: 1rem;\n}\n\n.classification-cell {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n}\n\n.classification-tag {\n  padding: 0.25rem 0.5rem;\n  border-radius: 1rem;\n  font-size: 0.875rem;\n  font-weight: 600;\n\n  &.mechanical {\n    background-color: #8a3ffc;\n    color: white;\n  }\n\n  &.functional {\n    background-color: #33b1ff;\n    color: white;\n  }\n\n  &.unclassified {\n    background-color: #da1e28;\n    color: white;\n  }\n\n  &.need-more-info {\n    background-color: #ff832b;\n    color: white;\n  }\n\n  &.subcategory {\n    &.scratches {\n      background-color: #6929c4;\n      color: white;\n    }\n\n    &.bent {\n      background-color: #1192e8;\n      color: white;\n    }\n\n    &.plugging {\n      background-color: #005d5d;\n      color: white;\n    }\n\n    &.discolor {\n      background-color: #9f1853;\n      color: white;\n    }\n\n    &.misalignment {\n      background-color: #fa4d56;\n      color: white;\n    }\n\n    &.other {\n      background-color: #4589ff;\n      color: white;\n    }\n  }\n}\n\n.edit-button {\n  background-color: #393939;\n  color: #f4f4f4;\n  border: 1px solid #525252;\n  border-radius: 4px;\n  padding: 0.25rem 0.5rem;\n  font-size: 0.75rem;\n  cursor: pointer;\n  margin-left: 0.5rem;\n\n  &:hover {\n    background-color: #4c4c4c;\n  }\n}\n\n.filter-dropdown {\n  display: flex;\n  flex-direction: column;\n  min-width: 180px;\n  margin-right: 1rem;\n}\n\n.processing-message {\n  margin-left: 1rem;\n  color: #33b1ff;\n  font-size: 0.875rem;\n  animation: pulse 2s infinite;\n}\n\n@keyframes pulse {\n  0% {\n    opacity: 0.6;\n  }\n  50% {\n    opacity: 1;\n  }\n  100% {\n    opacity: 0.6;\n  }\n}\n\n/* Modal styles */\n.modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.7);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 1000;\n}\n\n.modal-content {\n  background-color: #262626;\n  border-radius: 4px;\n  padding: 2rem;\n  width: 90%;\n  max-width: 500px;\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);\n\n  h3 {\n    margin-top: 0;\n    margin-bottom: 1rem;\n    font-size: 1.5rem;\n  }\n\n  .description-text {\n    margin-bottom: 1.5rem;\n    padding: 0.75rem;\n    background-color: #393939;\n    border-radius: 4px;\n    max-height: 100px;\n    overflow-y: auto;\n  }\n}\n\n.batch-results-modal {\n  max-width: 800px;\n  max-height: 90vh;\n  overflow-y: auto;\n}\n\n.batch-results-container {\n  margin: 1rem 0;\n  max-height: 60vh;\n  overflow-y: auto;\n  border: 1px solid #525252;\n  border-radius: 4px;\n}\n\n.batch-results-table {\n  width: 100%;\n  border-collapse: collapse;\n\n  th, td {\n    padding: 0.75rem;\n    text-align: left;\n    border-bottom: 1px solid #525252;\n  }\n\n  th {\n    background-color: #393939;\n    font-weight: 600;\n  }\n\n  tr:hover {\n    background-color: #333333;\n  }\n\n  .description-cell {\n    max-width: 300px;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    white-space: nowrap;\n  }\n}\n\n.modal-form {\n  margin-bottom: 1.5rem;\n\n  .form-group {\n    margin-bottom: 1rem;\n\n    label {\n      display: block;\n      margin-bottom: 0.5rem;\n      font-weight: 600;\n    }\n  }\n\n  .modal-select {\n    width: 100%;\n    padding: 0.5rem;\n    background-color: #393939;\n    color: #f4f4f4;\n    border: 1px solid #525252;\n    border-radius: 4px;\n    font-size: 1rem;\n  }\n}\n\n.modal-actions {\n  display: flex;\n  justify-content: flex-end;\n  gap: 1rem;\n\n  .modal-button {\n    padding: 0.5rem 1rem;\n    border-radius: 4px;\n    font-size: 1rem;\n    cursor: pointer;\n\n    &.cancel {\n      background-color: #393939;\n      color: #f4f4f4;\n      border: 1px solid #525252;\n\n      &:hover {\n        background-color: #4c4c4c;\n      }\n    }\n\n    &.save {\n      background-color: #0f62fe;\n      color: white;\n      border: none;\n\n      &:hover {\n        background-color: #0353e9;\n      }\n    }\n  }\n}\n</style>\n"]}]}