/*
*This file contains a function that excludes the /api-qualify/login route from the token verification funcction
* in token_verification.
*
 */

const jwt = require('./token_verification');

function auth(req, res, next) {
    // Routes that don't require authentication
    const excludedRoutes = [
        '/api-statit2/login',
        '/api-statit2/update_session_monitor',
        '/api-statit2/get_metis_part_numbers',
        '/api-statit2/get_metis_breakout_names',
        '/api-statit2/get_metis_xfactors',
        '/api-statit2/get_action_tracker_data',
        '/api-statit2/save_action_tracker_data',
        '/api-statit2/watsonx_prompt',  // WatsonX.ai API endpoint
        '/api-statit2/log',  // Client-side logging endpoint
        '/api-statit2/get_daily_fails',  // PQE Dashboard endpoints
        '/api-statit2/get_validation_counts',
        '/api-statit2/get_pqe_critical_issues',
        '/api-statit2/get_pqe_breakout_groups',
        // Validation 2 endpoints
        '/api-statit2/get_validation_data',
        '/api-statit2/get_classification_counts',
        '/api-statit2/get_pqe_part_numbers',
        '/api-statit2/process_new_defects',
        '/api-statit2/update_classification',
        '/api-statit2/query_by_classification'
    ];

    if (excludedRoutes.includes(req.path)) {
        console.log(`Skipping authentication for route: ${req.path}`);
        return next();
    }

    return jwt(req, res, next);
}

module.exports = auth;